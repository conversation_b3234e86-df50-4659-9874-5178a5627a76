@echo off
echo ========================================
echo 企业级数据库分析系统 - 完整启动脚本
echo ========================================

echo.
echo 第一步：检查数据库连接...
python test_direct.py
if %errorlevel% neq 0 (
    echo 数据库连接失败，请检查MySQL服务和密码
    pause
    exit /b 1
)

echo.
echo 第二步：安装MCP服务器到Claude Desktop...
fastmcp install claude-desktop enterprise_database_mcp_server.py --server-name "数据库分析助手" --env-file .env
if %errorlevel% neq 0 (
    echo MCP服务器安装失败，将手动提示配置
    echo.
    echo 请手动配置Claude Desktop：
    echo 1. 打开Claude Desktop设置
    echo 2. 添加MCP服务器配置
    echo 3. 服务器路径：%cd%\enterprise_database_mcp_server.py
    echo 4. 环境文件：%cd%\.env
    echo.
)

echo.
echo 第三步：启动HTTP API服务器（用于Streamlit前端）...
echo 正在后台启动API服务器...
set HTTP_API_MODE=true
set HTTP_API_PORT=8000
start "MCP API Server" cmd /c "python enterprise_database_mcp_server.py"

echo 等待API服务器启动...
timeout /t 5 /nobreak >nul

echo.
echo 第四步：启动Streamlit前端...
echo 正在启动Streamlit前端...
set API_BASE_URL=http://localhost:8000
set REFRESH_INTERVAL=30
start "Streamlit Frontend" cmd /c "streamlit run streamlit_dashboard.py --server.port 8501 --server.headless true"

echo 等待Streamlit启动...
timeout /t 5 /nobreak >nul

echo.
echo ========================================
echo 🎉 系统启动完成！
echo ========================================
echo.
echo 📋 访问地址：
echo   🌐 Streamlit前端: http://localhost:8501
echo   📚 API文档: http://localhost:8000/docs
echo   🤖 Claude Desktop: 重启后可用
echo.
echo 📝 使用说明：
echo   1. 重启Claude Desktop以加载MCP服务器
echo   2. 在Claude Desktop中测试：'获取数据库表信息'
echo   3. 访问Streamlit前端进行专业数据分析
echo   4. 查看API文档了解接口详情
echo.
echo 🛑 停止服务：关闭此窗口或按Ctrl+C
echo ========================================

echo.
echo 按任意键打开Streamlit前端...
pause >nul
start http://localhost:8501

echo.
echo 按任意键打开API文档...
pause >nul
start http://localhost:8000/docs

echo.
echo 系统正在运行中...
echo 按任意键退出
pause >nul
