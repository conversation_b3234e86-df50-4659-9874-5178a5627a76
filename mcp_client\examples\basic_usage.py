#!/usr/bin/env python3
"""
基础使用示例
演示如何使用MCP客户端进行基本操作
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from mcp_client.client import MCPClient
from mcp_client.utils.logger import log

async def basic_connection_example():
    """基础连接示例"""
    print("🔗 基础连接示例")
    print("=" * 50)
    
    # 创建客户端
    client = MCPClient()
    
    try:
        # 连接到服务器
        print("正在连接到MCP服务器...")
        success = await client.connect()
        
        if not success:
            print("❌ 连接失败")
            return
        
        print("✅ 连接成功!")
        
        # 获取可用工具
        print("\n📋 获取可用工具:")
        tools = await client.get_available_tools()
        for name, tool in tools.items():
            print(f"  • {name}: {tool['description']}")
        
        # 测试ping
        print("\n🏓 测试连接:")
        async with client.client:
            await client.client.ping()
            print("✅ Ping成功")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
    
    finally:
        await client.disconnect()
        print("\n👋 连接已断开")

async def system_status_example():
    """系统状态查询示例"""
    print("\n📊 系统状态查询示例")
    print("=" * 50)
    
    async with MCPClient() as client:
        try:
            # 获取系统状态
            status = await client.get_system_status()
            
            print("系统状态:")
            if "error" not in status:
                data = status.get("data", {})
                print(f"  • 数据库状态: {data.get('database_status', 'unknown')}")
                print(f"  • 数据总量: {data.get('total_data_rows', 0):,}")
                print(f"  • 连接池大小: {data.get('connection_pool_size', 0)}")
                print(f"  • 运行时间: {data.get('uptime', 'unknown')}")
            else:
                print(f"  ❌ 错误: {status['error']}")
                
        except Exception as e:
            print(f"❌ 获取系统状态失败: {e}")

async def statistical_analysis_example():
    """统计分析示例"""
    print("\n📈 统计分析示例")
    print("=" * 50)
    
    async with MCPClient() as client:
        try:
            # 计算温度平均值
            result = await client.statistical_analysis(
                start_time="2024-01-01 00:00:00",
                end_time="2024-01-02 00:00:00",
                columns=["temperature"],
                operation="average"
            )
            
            print("统计分析结果:")
            if "error" not in result:
                summary = result.get("summary", {})
                print(f"  • 操作类型: {summary.get('operation', 'unknown')}")
                print(f"  • 分析列: {summary.get('columns_analyzed', [])}")
                print(f"  • 数据点数: {summary.get('data_points', 0)}")
                
                # 显示关键指标
                if "key_metrics" in result:
                    print("  • 关键指标:")
                    for key, value in result["key_metrics"].items():
                        print(f"    - {key}: {value}")
            else:
                print(f"  ❌ 错误: {result['error']}")
                
        except Exception as e:
            print(f"❌ 统计分析失败: {e}")

async def anomaly_detection_example():
    """异常检测示例"""
    print("\n🚨 异常检测示例")
    print("=" * 50)
    
    async with MCPClient() as client:
        try:
            # 检测温度异常
            result = await client.detect_anomalies(
                column="temperature",
                method="hybrid",
                time_window="24h",
                sensitivity=2.0
            )
            
            print("异常检测结果:")
            if "error" not in result:
                summary = result.get("summary", {})
                print(f"  • 检测方法: {summary.get('method', 'unknown')}")
                print(f"  • 检测列: {summary.get('column', 'unknown')}")
                print(f"  • 异常总数: {summary.get('total_anomalies', 0)}")
                
                # 显示严重程度分布
                severity_levels = summary.get("severity_levels", {})
                if severity_levels:
                    print("  • 严重程度分布:")
                    for level, count in severity_levels.items():
                        if count > 0:
                            print(f"    - {level}: {count}")
            else:
                print(f"  ❌ 错误: {result['error']}")
                
        except Exception as e:
            print(f"❌ 异常检测失败: {e}")

async def chart_generation_example():
    """图表生成示例"""
    print("\n📊 图表生成示例")
    print("=" * 50)
    
    async with MCPClient() as client:
        try:
            # 生成温度趋势图
            result = await client.generate_chart(
                chart_type="line",
                columns=["temperature", "pressure"],
                time_range="24h",
                title="温度和压力趋势图"
            )
            
            print("图表生成结果:")
            if "error" not in result:
                summary = result.get("summary", {})
                print(f"  • 图表类型: {summary.get('chart_type', 'unknown')}")
                print(f"  • 图表标题: {summary.get('title', 'unknown')}")
                print(f"  • 数据列: {summary.get('columns', [])}")
                print(f"  • 数据点数: {summary.get('data_points', 0)}")
                
                # 检查图表是否准备就绪
                chart_ready = result.get("metadata", {}).get("chart_ready", False)
                print(f"  • 图表状态: {'✅ 就绪' if chart_ready else '❌ 未就绪'}")
            else:
                print(f"  ❌ 错误: {result['error']}")
                
        except Exception as e:
            print(f"❌ 图表生成失败: {e}")

async def intelligent_query_example():
    """智能查询示例"""
    print("\n🤖 智能查询示例")
    print("=" * 50)
    
    async with MCPClient() as client:
        try:
            # 智能查询示例
            queries = [
                "获取系统状态",
                "显示最近24小时温度平均值",
                "检测温度异常",
                "生成温度趋势折线图"
            ]
            
            for query in queries:
                print(f"\n🔍 查询: {query}")
                result = await client.query(query)
                
                if "error" not in result:
                    intent_type = result.get("intent_type", "unknown")
                    description = result.get("description", "无描述")
                    print(f"  • 识别意图: {intent_type}")
                    print(f"  • 描述: {description}")
                    print(f"  • 执行状态: ✅ 成功")
                else:
                    print(f"  • 执行状态: ❌ 失败 - {result['error']}")
                
        except Exception as e:
            print(f"❌ 智能查询失败: {e}")

async def alert_rule_example():
    """提醒规则示例"""
    print("\n⚠️ 提醒规则示例")
    print("=" * 50)
    
    async with MCPClient() as client:
        try:
            # 创建温度提醒规则
            result = await client.create_alert_rule(
                rule_id="temp_high_alert",
                name="高温提醒",
                column="temperature",
                condition="greater_than",
                threshold=35.0
            )
            
            print("提醒规则创建结果:")
            if "error" not in result:
                summary = result.get("summary", {})
                print(f"  • 规则ID: {summary.get('rule_id', 'unknown')}")
                print(f"  • 规则名称: {summary.get('rule_name', 'unknown')}")
                print(f"  • 创建状态: {'✅ 成功' if summary.get('created', False) else '❌ 失败'}")
                print(f"  • 规则状态: {summary.get('status', 'unknown')}")
            else:
                print(f"  ❌ 错误: {result['error']}")
                
        except Exception as e:
            print(f"❌ 创建提醒规则失败: {e}")

async def main():
    """主函数 - 运行所有示例"""
    print("🚀 MCP客户端基础使用示例")
    print("=" * 80)
    
    try:
        # 运行所有示例
        await basic_connection_example()
        await system_status_example()
        await statistical_analysis_example()
        await anomaly_detection_example()
        await chart_generation_example()
        await intelligent_query_example()
        await alert_rule_example()
        
        print("\n🎉 所有示例运行完成!")
        
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断")
    except Exception as e:
        print(f"\n❌ 运行示例时出错: {e}")

if __name__ == "__main__":
    asyncio.run(main())
