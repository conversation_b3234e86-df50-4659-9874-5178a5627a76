"""
ECharts 图表配置生成工具
支持多种图表类型的配置生成和数据转换
"""

import json
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
import logging

logger = logging.getLogger(__name__)

class EChartsConfigGenerator:
    """ECharts 配置生成器"""
    
    def __init__(self):
        self.default_theme = {
            "color": [
                "#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de",
                "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc", "#ff9f7f"
            ],
            "backgroundColor": "transparent",
            "textStyle": {
                "fontFamily": "Arial, sans-serif",
                "fontSize": 12
            }
        }
    
    def generate_config(self, 
                       chart_type: str, 
                       df: pd.DataFrame, 
                       columns: List[str], 
                       title: str = "数据图表",
                       group_by: Optional[str] = None,
                       **kwargs) -> Dict[str, Any]:
        """
        生成 ECharts 配置
        
        Args:
            chart_type: 图表类型
            df: 数据DataFrame
            columns: 要绘制的列
            title: 图表标题
            group_by: 分组字段
            **kwargs: 其他配置参数
        
        Returns:
            ECharts 配置字典
        """
        try:
            # 基础配置
            config = {
                "title": {
                    "text": title,
                    "left": "center",
                    "textStyle": {
                        "fontSize": 16,
                        "fontWeight": "bold"
                    }
                },
                "tooltip": {
                    "trigger": "axis" if chart_type in ["line", "bar"] else "item",
                    "axisPointer": {
                        "type": "cross" if chart_type in ["line", "scatter"] else "shadow"
                    }
                },
                "legend": {
                    "top": "bottom",
                    "data": columns
                },
                "grid": {
                    "left": "3%",
                    "right": "4%",
                    "bottom": "15%",
                    "containLabel": True
                },
                "color": self.default_theme["color"]
            }
            
            # 根据图表类型生成配置
            if chart_type == "line":
                config.update(self._generate_line_config(df, columns))
            elif chart_type == "bar":
                config.update(self._generate_bar_config(df, columns))
            elif chart_type == "pie":
                config.update(self._generate_pie_config(df, columns))
            elif chart_type == "scatter":
                config.update(self._generate_scatter_config(df, columns))
            elif chart_type == "heatmap":
                config.update(self._generate_heatmap_config(df, columns))
            elif chart_type == "histogram":
                config.update(self._generate_histogram_config(df, columns))
            elif chart_type == "box":
                config.update(self._generate_box_config(df, columns))
            else:
                # 默认使用折线图
                config.update(self._generate_line_config(df, columns))
            
            return config
            
        except Exception as e:
            logger.error(f"生成 ECharts 配置失败: {str(e)}")
            return self._generate_error_config(str(e))
    
    def _generate_line_config(self, df: pd.DataFrame, columns: List[str]) -> Dict[str, Any]:
        """生成折线图配置"""
        # 处理时间轴
        if 'timestamp' in df.columns:
            x_data = df['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S').tolist()
        else:
            x_data = list(range(len(df)))
        
        series = []
        for col in columns:
            if col in df.columns:
                series.append({
                    "name": col,
                    "type": "line",
                    "data": df[col].fillna(0).tolist(),
                    "smooth": True,
                    "symbol": "circle",
                    "symbolSize": 4,
                    "lineStyle": {"width": 2}
                })
        
        return {
            "xAxis": {
                "type": "category",
                "data": x_data,
                "axisLabel": {
                    "rotate": 45 if len(x_data) > 10 else 0
                }
            },
            "yAxis": {
                "type": "value",
                "axisLabel": {
                    "formatter": "{value}"
                }
            },
            "series": series
        }
    
    def _generate_bar_config(self, df: pd.DataFrame, columns: List[str]) -> Dict[str, Any]:
        """生成柱状图配置"""
        if len(columns) == 1 and 'timestamp' in df.columns:
            # 时间序列柱状图
            df_grouped = df.groupby(df['timestamp'].dt.hour)[columns[0]].mean().reset_index()
            x_data = [f"{hour}:00" for hour in df_grouped['timestamp']]
            series = [{
                "name": columns[0],
                "type": "bar",
                "data": df_grouped[columns[0]].tolist(),
                "itemStyle": {
                    "borderRadius": [4, 4, 0, 0]
                }
            }]
        else:
            # 多列对比柱状图
            x_data = columns
            avg_values = [df[col].mean() if col in df.columns else 0 for col in columns]
            series = [{
                "name": "平均值",
                "type": "bar",
                "data": avg_values,
                "itemStyle": {
                    "borderRadius": [4, 4, 0, 0]
                }
            }]
        
        return {
            "xAxis": {
                "type": "category",
                "data": x_data
            },
            "yAxis": {
                "type": "value"
            },
            "series": series
        }
    
    def _generate_pie_config(self, df: pd.DataFrame, columns: List[str]) -> Dict[str, Any]:
        """生成饼图配置"""
        data = []
        for col in columns:
            if col in df.columns:
                value = df[col].mean()
                data.append({
                    "name": col,
                    "value": round(value, 2)
                })
        
        return {
            "series": [{
                "name": "数据分布",
                "type": "pie",
                "radius": ["40%", "70%"],
                "center": ["50%", "50%"],
                "data": data,
                "emphasis": {
                    "itemStyle": {
                        "shadowBlur": 10,
                        "shadowOffsetX": 0,
                        "shadowColor": "rgba(0, 0, 0, 0.5)"
                    }
                },
                "label": {
                    "formatter": "{b}: {c} ({d}%)"
                }
            }]
        }
    
    def _generate_scatter_config(self, df: pd.DataFrame, columns: List[str]) -> Dict[str, Any]:
        """生成散点图配置"""
        if len(columns) >= 2:
            # 双变量散点图
            data = []
            for _, row in df.iterrows():
                if not pd.isna(row[columns[0]]) and not pd.isna(row[columns[1]]):
                    data.append([row[columns[0]], row[columns[1]]])
            
            series = [{
                "name": f"{columns[0]} vs {columns[1]}",
                "type": "scatter",
                "data": data,
                "symbolSize": 6
            }]
            
            return {
                "xAxis": {
                    "type": "value",
                    "name": columns[0]
                },
                "yAxis": {
                    "type": "value", 
                    "name": columns[1]
                },
                "series": series
            }
        else:
            # 时间序列散点图
            if 'timestamp' in df.columns:
                x_data = df['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S').tolist()
            else:
                x_data = list(range(len(df)))
            
            data = []
            for i, value in enumerate(df[columns[0]]):
                if not pd.isna(value):
                    data.append([i, value])
            
            return {
                "xAxis": {
                    "type": "category",
                    "data": x_data
                },
                "yAxis": {
                    "type": "value"
                },
                "series": [{
                    "name": columns[0],
                    "type": "scatter",
                    "data": data,
                    "symbolSize": 6
                }]
            }
    
    def _generate_heatmap_config(self, df: pd.DataFrame, columns: List[str]) -> Dict[str, Any]:
        """生成热力图配置"""
        # 计算相关性矩阵
        corr_matrix = df[columns].corr()
        
        data = []
        for i, col1 in enumerate(columns):
            for j, col2 in enumerate(columns):
                if not pd.isna(corr_matrix.loc[col1, col2]):
                    data.append([i, j, round(corr_matrix.loc[col1, col2], 3)])
        
        return {
            "xAxis": {
                "type": "category",
                "data": columns,
                "axisLabel": {
                    "rotate": 45
                }
            },
            "yAxis": {
                "type": "category",
                "data": columns
            },
            "visualMap": {
                "min": -1,
                "max": 1,
                "calculable": True,
                "orient": "horizontal",
                "left": "center",
                "bottom": "5%",
                "inRange": {
                    "color": ["#313695", "#4575b4", "#74add1", "#abd9e9", 
                             "#e0f3f8", "#ffffcc", "#fee090", "#fdae61", 
                             "#f46d43", "#d73027", "#a50026"]
                }
            },
            "series": [{
                "name": "相关性",
                "type": "heatmap",
                "data": data,
                "label": {
                    "show": True,
                    "formatter": "{c}"
                },
                "emphasis": {
                    "itemStyle": {
                        "shadowBlur": 10,
                        "shadowColor": "rgba(0, 0, 0, 0.5)"
                    }
                }
            }]
        }
    
    def _generate_histogram_config(self, df: pd.DataFrame, columns: List[str]) -> Dict[str, Any]:
        """生成直方图配置"""
        series = []
        
        for col in columns:
            if col in df.columns:
                # 计算直方图数据
                hist, bins = pd.cut(df[col].dropna(), bins=20, retbins=True)
                hist_data = hist.value_counts().sort_index()
                
                x_data = [f"{bins[i]:.2f}-{bins[i+1]:.2f}" for i in range(len(bins)-1)]
                y_data = hist_data.tolist()
                
                series.append({
                    "name": col,
                    "type": "bar",
                    "data": y_data,
                    "barWidth": "80%"
                })
        
        return {
            "xAxis": {
                "type": "category",
                "data": x_data,
                "axisLabel": {
                    "rotate": 45
                }
            },
            "yAxis": {
                "type": "value",
                "name": "频次"
            },
            "series": series
        }
    
    def _generate_box_config(self, df: pd.DataFrame, columns: List[str]) -> Dict[str, Any]:
        """生成箱线图配置"""
        data = []
        
        for col in columns:
            if col in df.columns:
                values = df[col].dropna()
                if len(values) > 0:
                    q1 = values.quantile(0.25)
                    q2 = values.quantile(0.5)  # 中位数
                    q3 = values.quantile(0.75)
                    iqr = q3 - q1
                    lower = max(values.min(), q1 - 1.5 * iqr)
                    upper = min(values.max(), q3 + 1.5 * iqr)
                    
                    data.append([lower, q1, q2, q3, upper])
        
        return {
            "xAxis": {
                "type": "category",
                "data": columns
            },
            "yAxis": {
                "type": "value"
            },
            "series": [{
                "name": "箱线图",
                "type": "boxplot",
                "data": data
            }]
        }
    
    def _generate_error_config(self, error_msg: str) -> Dict[str, Any]:
        """生成错误配置"""
        return {
            "title": {
                "text": "图表生成错误",
                "left": "center",
                "textStyle": {
                    "color": "#ff4d4f"
                }
            },
            "graphic": {
                "type": "text",
                "left": "center",
                "top": "middle",
                "style": {
                    "text": f"错误信息: {error_msg}",
                    "fontSize": 14,
                    "fill": "#ff4d4f"
                }
            }
        }

def create_echarts_html(config: Dict[str, Any], width: str = "100%", height: str = "400px") -> str:
    """
    创建包含 ECharts 图表的 HTML
    
    Args:
        config: ECharts 配置
        width: 图表宽度
        height: 图表高度
    
    Returns:
        HTML 字符串
    """
    config_json = json.dumps(config, ensure_ascii=False, indent=2)
    
    html_template = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
        <style>
            body {{ margin: 0; padding: 10px; font-family: Arial, sans-serif; }}
            #chart {{ width: {width}; height: {height}; }}
        </style>
    </head>
    <body>
        <div id="chart"></div>
        <script>
            var chartDom = document.getElementById('chart');
            var myChart = echarts.init(chartDom);
            var option = {config_json};
            
            myChart.setOption(option);
            
            // 响应式调整
            window.addEventListener('resize', function() {{
                myChart.resize();
            }});
            
            // 错误处理
            myChart.on('error', function(params) {{
                console.error('ECharts 渲染错误:', params);
            }});
        </script>
    </body>
    </html>
    """
    
    return html_template


def convert_dataframe_to_echarts_data(df: pd.DataFrame, chart_type: str, columns: List[str]) -> Dict[str, Any]:
    """
    将 DataFrame 转换为 ECharts 数据格式

    Args:
        df: 数据DataFrame
        chart_type: 图表类型
        columns: 数据列

    Returns:
        转换后的数据
    """
    try:
        if chart_type in ["line", "bar"]:
            # 时间序列数据
            if 'timestamp' in df.columns:
                x_data = df['timestamp'].dt.strftime('%Y-%m-%d %H:%M:%S').tolist()
            else:
                x_data = list(range(len(df)))

            series_data = {}
            for col in columns:
                if col in df.columns:
                    series_data[col] = df[col].fillna(0).tolist()

            return {
                "x_data": x_data,
                "series_data": series_data
            }

        elif chart_type == "pie":
            # 饼图数据
            data = []
            for col in columns:
                if col in df.columns:
                    value = df[col].mean()
                    data.append({"name": col, "value": round(value, 2)})
            return {"pie_data": data}

        elif chart_type == "scatter":
            # 散点图数据
            if len(columns) >= 2:
                data = []
                for _, row in df.iterrows():
                    if not pd.isna(row[columns[0]]) and not pd.isna(row[columns[1]]):
                        data.append([row[columns[0]], row[columns[1]]])
                return {"scatter_data": data, "x_name": columns[0], "y_name": columns[1]}
            else:
                # 单变量散点图
                data = []
                for i, value in enumerate(df[columns[0]]):
                    if not pd.isna(value):
                        data.append([i, value])
                return {"scatter_data": data, "x_name": "索引", "y_name": columns[0]}

        elif chart_type == "heatmap":
            # 热力图数据
            corr_matrix = df[columns].corr()
            data = []
            for i, col1 in enumerate(columns):
                for j, col2 in enumerate(columns):
                    if not pd.isna(corr_matrix.loc[col1, col2]):
                        data.append([i, j, round(corr_matrix.loc[col1, col2], 3)])
            return {"heatmap_data": data, "labels": columns}

        else:
            # 默认返回原始数据
            return {"raw_data": df.to_dict('records')}

    except Exception as e:
        logger.error(f"数据转换失败: {str(e)}")
        return {"error": str(e)}


def validate_echarts_config(config: Dict[str, Any]) -> bool:
    """
    验证 ECharts 配置的有效性

    Args:
        config: ECharts 配置

    Returns:
        是否有效
    """
    try:
        # 检查必要的字段
        if not isinstance(config, dict):
            return False

        # 检查 series 字段
        if "series" in config:
            series = config["series"]
            if not isinstance(series, list):
                return False

            for s in series:
                if not isinstance(s, dict) or "type" not in s:
                    return False

        # 检查 JSON 序列化
        json.dumps(config)
        return True

    except Exception as e:
        logger.error(f"配置验证失败: {str(e)}")
        return False


def get_echarts_theme_config(theme_name: str = "default") -> Dict[str, Any]:
    """
    获取 ECharts 主题配置

    Args:
        theme_name: 主题名称

    Returns:
        主题配置
    """
    themes = {
        "default": {
            "color": [
                "#5470c6", "#91cc75", "#fac858", "#ee6666", "#73c0de",
                "#3ba272", "#fc8452", "#9a60b4", "#ea7ccc", "#ff9f7f"
            ],
            "backgroundColor": "transparent"
        },
        "dark": {
            "color": [
                "#dd6b66", "#759aa0", "#e69d87", "#8dc1a9", "#ea7e53",
                "#eedd78", "#73a373", "#73b9bc", "#7289ab", "#91ca8c"
            ],
            "backgroundColor": "#1e1e1e",
            "textStyle": {
                "color": "#ffffff"
            }
        },
        "light": {
            "color": [
                "#37A2DA", "#32C5E9", "#67E0E3", "#9FE6B8", "#FFDB5C",
                "#ff9f7f", "#fb7293", "#E062AE", "#E690D1", "#e7bcf3"
            ],
            "backgroundColor": "#ffffff"
        }
    }

    return themes.get(theme_name, themes["default"])


class EChartsDataProcessor:
    """ECharts 数据处理器"""

    @staticmethod
    def process_time_series(df: pd.DataFrame, time_col: str = 'timestamp') -> pd.DataFrame:
        """处理时间序列数据"""
        if time_col in df.columns:
            df[time_col] = pd.to_datetime(df[time_col])
            df = df.sort_values(time_col)
        return df

    @staticmethod
    def aggregate_data(df: pd.DataFrame,
                      time_col: str = 'timestamp',
                      aggregation: str = 'raw',
                      columns: List[str] = None) -> pd.DataFrame:
        """聚合数据"""
        if aggregation == 'raw' or time_col not in df.columns:
            return df

        if columns is None:
            columns = [col for col in df.columns if col != time_col]

        if aggregation == 'hourly':
            df_grouped = df.groupby(df[time_col].dt.floor('h'))[columns].mean().reset_index()
        elif aggregation == 'daily':
            df_grouped = df.groupby(df[time_col].dt.floor('d'))[columns].mean().reset_index()
        elif aggregation == 'weekly':
            df_grouped = df.groupby(df[time_col].dt.floor('w'))[columns].mean().reset_index()
        else:
            df_grouped = df

        return df_grouped

    @staticmethod
    def handle_missing_values(df: pd.DataFrame, method: str = 'forward_fill') -> pd.DataFrame:
        """处理缺失值"""
        if method == 'forward_fill':
            return df.ffill()
        elif method == 'backward_fill':
            return df.bfill()
        elif method == 'interpolate':
            return df.interpolate()
        elif method == 'drop':
            return df.dropna()
        else:
            return df.fillna(0)

    @staticmethod
    def filter_outliers(df: pd.DataFrame, columns: List[str], method: str = 'iqr') -> pd.DataFrame:
        """过滤异常值"""
        if method == 'iqr':
            for col in columns:
                if col in df.columns and df[col].dtype in ['int64', 'float64']:
                    Q1 = df[col].quantile(0.25)
                    Q3 = df[col].quantile(0.75)
                    IQR = Q3 - Q1
                    lower_bound = Q1 - 1.5 * IQR
                    upper_bound = Q3 + 1.5 * IQR
                    df = df[(df[col] >= lower_bound) & (df[col] <= upper_bound)]

        return df
