#!/usr/bin/env python3
"""
清理并重新启动系统
解决CallToolResult缓存问题
"""

import os
import sys
import time
import subprocess
import shutil
from pathlib import Path

def kill_existing_processes():
    """杀死现有的相关进程"""
    print("🧹 清理现有进程...")
    
    try:
        # 杀死Streamlit进程
        subprocess.run(["taskkill", "/F", "/IM", "streamlit.exe"], 
                      capture_output=True, shell=True)
        subprocess.run(["taskkill", "/F", "/T", "/FI", "WINDOWTITLE eq Streamlit*"], 
                      capture_output=True, shell=True)
        print("✅ Streamlit进程已清理")
    except:
        pass
    
    try:
        # 杀死Python进程（小心处理）
        result = subprocess.run(["tasklist", "/FI", "IMAGENAME eq python.exe"], 
                               capture_output=True, text=True, shell=True)
        if "enterprise_ai_frontend.py" in result.stdout:
            subprocess.run(["taskkill", "/F", "/T", "/FI", "WINDOWTITLE eq *enterprise_ai_frontend*"], 
                          capture_output=True, shell=True)
        print("✅ 相关Python进程已清理")
    except:
        pass

def clear_streamlit_cache():
    """清理Streamlit缓存"""
    print("🗑️ 清理Streamlit缓存...")
    
    try:
        # 清理用户缓存目录
        cache_dirs = [
            Path.home() / ".streamlit",
            Path.home() / "AppData" / "Local" / "streamlit",
            Path.cwd() / ".streamlit"
        ]
        
        for cache_dir in cache_dirs:
            if cache_dir.exists():
                try:
                    shutil.rmtree(cache_dir)
                    print(f"✅ 已清理: {cache_dir}")
                except:
                    pass
        
        # 清理Python缓存
        pycache_dirs = list(Path.cwd().rglob("__pycache__"))
        for pycache_dir in pycache_dirs:
            try:
                shutil.rmtree(pycache_dir)
                print(f"✅ 已清理Python缓存: {pycache_dir}")
            except:
                pass
                
    except Exception as e:
        print(f"⚠️ 缓存清理警告: {e}")

def setup_environment():
    """设置环境变量"""
    env_vars = {
        "DB_HOST": "localhost",
        "DB_PORT": "3306", 
        "DB_USER": "root",
        "DB_PASSWORD": "123456",
        "DB_NAME": "sensor_data",
        "DB_CHARSET": "utf8mb4",
        "PYTHONIOENCODING": "utf-8",
        "STREAMLIT_SERVER_HEADLESS": "false",
        "STREAMLIT_BROWSER_GATHER_USAGE_STATS": "false"
    }
    
    print("🌍 设置环境变量:")
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  {key} = {value}")

def verify_file_integrity():
    """验证文件完整性"""
    print("🔍 验证文件完整性...")
    
    frontend_path = Path("mcp_client/enterprise_ai_frontend.py")
    if not frontend_path.exists():
        print(f"❌ 前端文件不存在: {frontend_path}")
        return False
    
    # 检查文件是否包含修复后的代码
    with open(frontend_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    if "result.get(" in content:
        print("❌ 发现未修复的result.get()调用")
        return False
    
    if "result.data" in content:
        print("✅ 文件包含修复后的result.data调用")
        return True
    
    print("⚠️ 文件可能需要检查")
    return True

def start_clean_streamlit():
    """启动干净的Streamlit"""
    print("🚀 启动干净的Streamlit...")
    
    frontend_path = Path("mcp_client/enterprise_ai_frontend.py")
    
    cmd = [
        sys.executable, "-m", "streamlit", "run",
        str(frontend_path),
        "--server.port=8502",
        "--server.headless=false",
        "--server.runOnSave=true",
        "--browser.gatherUsageStats=false"
    ]
    
    print(f"执行命令: {' '.join(cmd)}")
    
    try:
        # 启动新的Streamlit进程
        process = subprocess.Popen(
            cmd,
            cwd=str(Path.cwd()),
            env=os.environ.copy(),
            creationflags=subprocess.CREATE_NEW_CONSOLE if os.name == 'nt' else 0
        )
        
        print("✅ Streamlit启动中...")
        print("📍 新地址: http://localhost:8502")
        
        return process
        
    except Exception as e:
        print(f"❌ Streamlit启动失败: {e}")
        return None

def main():
    """主函数"""
    print("🔄 企业级数据分析系统 - 清理重启")
    print("=" * 50)
    
    # 1. 杀死现有进程
    kill_existing_processes()
    time.sleep(2)
    
    # 2. 清理缓存
    clear_streamlit_cache()
    time.sleep(1)
    
    # 3. 设置环境
    setup_environment()
    
    # 4. 验证文件
    if not verify_file_integrity():
        print("❌ 文件验证失败，请检查修复状态")
        return
    
    # 5. 启动Streamlit
    process = start_clean_streamlit()
    if not process:
        print("❌ 启动失败")
        return
    
    print("\n🎉 清理重启完成!")
    print("📍 新的Web界面: http://localhost:8502")
    print("📍 MCP服务器: http://127.0.0.1:8000/mcp")
    print("\n💡 如果还有问题，请检查浏览器缓存")
    print("⏹️ 按 Ctrl+C 停止")
    
    try:
        # 等待进程
        process.wait()
    except KeyboardInterrupt:
        print("\n👋 已停止")
        try:
            process.terminate()
        except:
            pass

if __name__ == "__main__":
    main()
