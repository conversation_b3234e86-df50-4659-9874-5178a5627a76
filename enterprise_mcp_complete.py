#!/usr/bin/env python3
"""
企业级数据库分析MCP服务器 - 完整功能版本
Windows兼容，包含所有核心功能
"""

import os
import logging
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
import mysql.connector
import pandas as pd
import numpy as np
from dotenv import load_dotenv
from fastmcp import FastMCP
from pydantic import BaseModel, Field
import plotly.graph_objects as go

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', '3306')),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'sensor_data'),
    'charset': os.getenv('DB_CHARSET', 'utf8mb4')
}

# 创建MCP服务器
mcp = FastMCP(
    name="Enterprise Database Analyzer",
    instructions="""
I am an enterprise-level database analysis assistant for sensor_data analysis.

I can help you with:
1. Advanced statistical analysis (sum, average, min, max, etc.)
2. Intelligent anomaly detection with multiple algorithms
3. Data visualization (charts and graphs)
4. Trend analysis and prediction
5. Alert rule management
6. System monitoring

Available columns: timestamp, temperature, pressure, humidity, flow_rate, voltage, current, power, device_id, location, status
"""
)

# Pydantic模型
class StatResult(BaseModel):
    column: str = Field(description="Column name")
    operation: str = Field(description="Operation performed")
    result: float = Field(description="Result value")
    count: int = Field(description="Number of data points")
    processing_time: float = Field(description="Processing time in seconds")

class AnomalyResult(BaseModel):
    total_points: int = Field(description="Total data points")
    anomaly_count: int = Field(description="Number of anomalies")
    anomaly_rate: float = Field(description="Anomaly rate")
    confidence_score: float = Field(description="Detection confidence")
    method_used: str = Field(description="Detection method used")
    possible_causes: List[str] = Field(description="Possible causes")
    anomalies: List[Dict] = Field(description="Anomaly details")
    processing_time: float = Field(description="Processing time")

def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        raise

@mcp.tool
async def advanced_statistical_analysis(
    start_time: str = Field(description="Start time (YYYY-MM-DD HH:MM:SS)"),
    end_time: str = Field(description="End time (YYYY-MM-DD HH:MM:SS)"),
    columns: List[str] = Field(description="Column names to analyze"),
    operation: str = Field(description="Statistical operation: sum, average, count, min, max, std, median"),
    group_by: Optional[str] = Field(default=None, description="Group by field (optional)")
) -> List[StatResult]:
    """Advanced statistical analysis tool"""
    start_time_func = time.time()
    
    try:
        connection = get_db_connection()
        results = []
        
        for column in columns:
            # Build query based on operation
            if operation == "sum":
                agg_func = f"SUM({column})"
            elif operation == "average":
                agg_func = f"AVG({column})"
            elif operation == "count":
                agg_func = f"COUNT({column})"
            elif operation == "min":
                agg_func = f"MIN({column})"
            elif operation == "max":
                agg_func = f"MAX({column})"
            elif operation == "std":
                agg_func = f"STDDEV({column})"
            else:
                agg_func = f"AVG({column})"  # Default to average
            
            query = f"""
            SELECT {agg_func} as result, COUNT(*) as total_count
            FROM sensor_data 
            WHERE timestamp BETWEEN %s AND %s
            """
            
            df = pd.read_sql(query, connection, params=(start_time, end_time))
            
            if not df.empty and df.iloc[0]['result'] is not None:
                result = StatResult(
                    column=column,
                    operation=operation,
                    result=float(df.iloc[0]['result']),
                    count=int(df.iloc[0]['total_count']),
                    processing_time=time.time() - start_time_func
                )
                results.append(result)
        
        connection.close()
        return results
        
    except Exception as e:
        logger.error(f"Statistical analysis failed: {e}")
        raise

@mcp.tool
async def intelligent_anomaly_detection(
    column: str = Field(description="Column to analyze for anomalies"),
    method: str = Field(default="statistical", description="Detection method: statistical, zscore, hybrid"),
    time_window: str = Field(default="24h", description="Time window: 1h, 6h, 24h, 3d, 7d"),
    sensitivity: float = Field(default=2.0, description="Sensitivity level (1.0-5.0)"),
    include_reasons: bool = Field(default=True, description="Include reason analysis")
) -> AnomalyResult:
    """Intelligent anomaly detection with multiple algorithms"""
    start_time_func = time.time()
    
    try:
        # Calculate time range
        end_time = datetime.now()
        if time_window.endswith('h'):
            hours = int(time_window[:-1])
            start_time = end_time - timedelta(hours=hours)
        elif time_window.endswith('d'):
            days = int(time_window[:-1])
            start_time = end_time - timedelta(days=days)
        else:
            start_time = end_time - timedelta(hours=24)
        
        connection = get_db_connection()
        
        query = f"""
        SELECT timestamp, {column}, device_id, location
        FROM sensor_data 
        WHERE timestamp BETWEEN %s AND %s 
        AND {column} IS NOT NULL
        ORDER BY timestamp
        """
        
        df = pd.read_sql(query, connection, params=(start_time, end_time))
        connection.close()
        
        if df.empty:
            raise ValueError("No valid data found in specified time range")
        
        # Perform anomaly detection
        values = df[column].values
        anomalies = []
        
        # Statistical method
        mean_val = np.mean(values)
        std_val = np.std(values)
        
        for idx, value in enumerate(values):
            z_score = abs(value - mean_val) / std_val if std_val > 0 else 0
            if z_score > sensitivity:
                anomalies.append({
                    'index': idx,
                    'timestamp': df.iloc[idx]['timestamp'].isoformat(),
                    'value': float(value),
                    'z_score': float(z_score),
                    'deviation': float(abs(value - mean_val)),
                    'device_id': str(df.iloc[idx]['device_id']),
                    'location': str(df.iloc[idx]['location'])
                })
        
        # Calculate results
        anomaly_count = len(anomalies)
        total_points = len(values)
        anomaly_rate = anomaly_count / total_points if total_points > 0 else 0
        confidence_score = min(0.95, 0.5 + (total_points / 1000) * 0.4)
        
        # Possible causes analysis
        possible_causes = []
        if include_reasons and anomaly_count > 0:
            if anomaly_rate > 0.1:
                possible_causes.append("Data collection system may have issues")
            if len(set(a['device_id'] for a in anomalies)) == 1:
                possible_causes.append("Specific device may have hardware problems")
            possible_causes.append("Environmental changes or external interference")
        
        return AnomalyResult(
            total_points=total_points,
            anomaly_count=anomaly_count,
            anomaly_rate=anomaly_rate,
            confidence_score=confidence_score,
            method_used=method,
            possible_causes=possible_causes,
            anomalies=anomalies[:20],  # Limit return count
            processing_time=time.time() - start_time_func
        )
        
    except Exception as e:
        logger.error(f"Anomaly detection failed: {e}")
        raise

@mcp.tool
async def generate_advanced_chart(
    chart_type: str = Field(description="Chart type: line, bar, pie, scatter"),
    columns: List[str] = Field(description="Columns to visualize"),
    time_range: str = Field(default="24h", description="Time range: 1h, 6h, 24h, 3d, 7d"),
    title: str = Field(default="Data Chart", description="Chart title")
) -> str:
    """Generate advanced interactive charts"""
    try:
        # Calculate time range
        end_time = datetime.now()
        if time_range.endswith('h'):
            hours = int(time_range[:-1])
            start_time = end_time - timedelta(hours=hours)
        elif time_range.endswith('d'):
            days = int(time_range[:-1])
            start_time = end_time - timedelta(days=days)
        else:
            start_time = end_time - timedelta(hours=24)
        
        connection = get_db_connection()
        
        columns_str = ", ".join(columns + ["timestamp"])
        query = f"""
        SELECT {columns_str}
        FROM sensor_data 
        WHERE timestamp BETWEEN %s AND %s
        ORDER BY timestamp
        """
        
        df = pd.read_sql(query, connection, params=(start_time, end_time))
        connection.close()
        
        if df.empty:
            return "No data found in specified time range"
        
        # Create chart
        fig = go.Figure()
        
        if chart_type == "line":
            for col in columns:
                fig.add_trace(go.Scatter(
                    x=df['timestamp'],
                    y=df[col],
                    mode='lines+markers',
                    name=col
                ))
        elif chart_type == "bar":
            for col in columns:
                fig.add_trace(go.Bar(
                    x=df['timestamp'],
                    y=df[col],
                    name=col
                ))
        else:
            # Default to line chart
            for col in columns:
                fig.add_trace(go.Scatter(
                    x=df['timestamp'],
                    y=df[col],
                    mode='lines',
                    name=col
                ))
        
        fig.update_layout(
            title=title,
            xaxis_title="Time",
            yaxis_title="Value",
            height=500
        )
        
        return fig.to_html(include_plotlyjs='cdn')
        
    except Exception as e:
        logger.error(f"Chart generation failed: {e}")
        return f"Chart generation failed: {str(e)}"

@mcp.tool
async def create_alert_rule(
    rule_id: str = Field(description="Rule ID"),
    name: str = Field(description="Rule name"),
    column: str = Field(description="Column to monitor"),
    condition: str = Field(description="Trigger condition: greater_than, less_than, range"),
    threshold: float = Field(description="Threshold value"),
    severity: str = Field(default="medium", description="Severity: low, medium, high, critical")
) -> str:
    """Create alert monitoring rule"""
    try:
        rule = {
            "rule_id": rule_id,
            "name": name,
            "column": column,
            "condition": condition,
            "threshold": threshold,
            "severity": severity,
            "created_time": datetime.now().isoformat()
        }
        
        logger.info(f"Created alert rule: {rule}")
        return f"Alert rule '{name}' created successfully with ID: {rule_id}"
        
    except Exception as e:
        logger.error(f"Failed to create alert rule: {e}")
        return f"Failed to create alert rule: {str(e)}"

@mcp.tool
async def get_system_status() -> dict:
    """Get comprehensive system status"""
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM sensor_data")
        total_rows = cursor.fetchone()[0]
        
        cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM sensor_data")
        time_range = cursor.fetchone()
        
        cursor.close()
        connection.close()
        
        return {
            "database_status": "connected",
            "total_data_rows": total_rows,
            "time_range": {
                "earliest": time_range[0].isoformat() if time_range[0] else None,
                "latest": time_range[1].isoformat() if time_range[1] else None
            },
            "system_time": datetime.now().isoformat(),
            "available_tools": [
                "advanced_statistical_analysis",
                "intelligent_anomaly_detection", 
                "generate_advanced_chart",
                "create_alert_rule",
                "get_system_status"
            ]
        }
        
    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        return {"error": str(e)}

if __name__ == "__main__":
    print(">> Enterprise Database Analysis MCP Server starting...")
    print("=" * 60)
    
    # Test database connection
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM sensor_data")
        count = cursor.fetchone()[0]
        cursor.close()
        connection.close()
        
        print(f">> Database connection successful, data rows: {count}")
    except Exception as e:
        print(f">> Database connection failed: {e}")
        exit(1)
    
    print(f"\n>> Database configuration:")
    print(f"   Host: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"   Database: {DB_CONFIG['database']}")
    
    print(f"\n>> Available MCP tools:")
    print("   >> advanced_statistical_analysis - Advanced statistical analysis")
    print("   >> intelligent_anomaly_detection - Smart anomaly detection")
    print("   >> generate_advanced_chart - Interactive data visualization")
    print("   >> create_alert_rule - Alert rule management")
    print("   >> get_system_status - System status monitoring")
    
    print(f"\n>> Server ready for Claude Desktop connection...")
    print(">> Install: fastmcp install claude-desktop enterprise_mcp_complete.py")
    
    # Start MCP server
    mcp.run()
