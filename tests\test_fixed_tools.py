#!/usr/bin/env python3
"""测试修复后的工具调用"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

async def test_fixed_tools():
    """测试修复后的工具"""
    try:
        # 创建客户端
        transport = StreamableHttpTransport("http://127.0.0.1:8000/mcp")
        client = Client(transport)
        
        print("🔗 连接到MCP服务器...")
        
        async with client:
            print("✅ 连接成功")
            
            # 测试修复后的统计分析 - 使用正确的operation参数
            print("\n🧪 测试修复后的统计分析")
            result = await client.call_tool("advanced_statistical_analysis", {
                "start_time": "2020-01-01 00:00:00",
                "end_time": "2030-01-01 00:00:00",
                "columns": ["temperature"],
                "operation": "average"  # 使用正确的参数值
            })
            
            if hasattr(result, 'data') and result.data:
                print("✅ 统计分析修复成功")
                print(f"📊 结果类型: {type(result.data)}")
                print(f"📊 结果内容: {result.data}")
            else:
                print("❌ 统计分析仍有问题")
                print(f"结果: {result}")
            
            # 测试count操作
            print("\n🧪 测试count操作")
            result = await client.call_tool("advanced_statistical_analysis", {
                "start_time": "2020-01-01 00:00:00",
                "end_time": "2030-01-01 00:00:00",
                "columns": ["temperature"],
                "operation": "count"
            })
            
            if hasattr(result, 'data') and result.data:
                print("✅ count操作成功")
                print(f"📊 数据总数: {result.data}")
            else:
                print("❌ count操作失败")
            
            # 测试异常检测
            print("\n🧪 测试异常检测")
            result = await client.call_tool("intelligent_anomaly_detection", {
                "column": "temperature",
                "method": "hybrid",
                "time_window": "24h",
                "sensitivity": 2.0
            })
            
            if hasattr(result, 'data') and result.data:
                print("✅ 异常检测成功")
            else:
                print("❌ 异常检测失败")
            
            print("\n🎉 所有测试完成！")
            print("现在可以在Streamlit前端正常使用AI分析功能了！")
            print("📍 新的Streamlit地址: http://localhost:8502")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_fixed_tools())
