# 🤖 企业级数据库分析系统 - MCP客户端

## 📋 项目概述

这是一个完整的本地MCP客户端，用于与企业级数据库分析MCP服务器进行交互。无需联网，完全本地化运行。

## 🏗️ 项目结构

```
mcp_client/
├── README.md                    # 项目说明
├── requirements.txt             # 依赖包
├── config.py                   # 配置文件
├── client.py                   # 主客户端
├── ui/
│   ├── __init__.py
│   ├── chat_interface.py       # 聊天界面
│   ├── dashboard.py            # 仪表板界面
│   └── components/             # UI组件
│       ├── __init__.py
│       ├── data_display.py     # 数据显示组件
│       ├── chart_viewer.py     # 图表查看器
│       └── status_panel.py     # 状态面板
├── handlers/
│   ├── __init__.py
│   ├── tool_handler.py         # 工具处理器
│   ├── data_handler.py         # 数据处理器
│   └── response_handler.py     # 响应处理器
├── utils/
│   ├── __init__.py
│   ├── logger.py               # 日志工具
│   ├── formatter.py            # 格式化工具
│   └── validator.py            # 验证工具
└── examples/
    ├── __init__.py
    ├── basic_usage.py          # 基础使用示例
    ├── advanced_analysis.py    # 高级分析示例
    └── batch_operations.py     # 批量操作示例
```

## 🚀 功能特性

### ✅ 核心功能
- **完全本地化**: 无需联网，数据安全
- **智能对话**: 自然语言交互界面
- **实时分析**: 实时数据分析和可视化
- **多种界面**: 命令行、Web界面、GUI界面

### ✅ 数据分析功能
- **统计分析**: 高级统计分析和计算
- **异常检测**: 智能异常检测和预警
- **趋势分析**: 数据趋势分析和预测
- **数据可视化**: 交互式图表和报表

### ✅ 系统管理
- **连接管理**: 自动连接和重连机制
- **性能监控**: 系统性能实时监控
- **日志记录**: 详细的操作日志
- **错误处理**: 完善的错误处理机制

## 🔧 安装和配置

### 1. 安装依赖
```bash
cd mcp_client
pip install -r requirements.txt
```

### 2. 配置服务器
编辑 `config.py` 文件，设置MCP服务器路径和参数。

### 3. 启动客户端
```bash
# 命令行界面
python client.py

# Web界面
python ui/dashboard.py

# 聊天界面
python ui/chat_interface.py
```

## 📊 使用示例

### 基础查询
```python
from mcp_client import MCPClient

client = MCPClient()
result = await client.query("获取最新的温度数据")
print(result)
```

### 高级分析
```python
# 异常检测
anomalies = await client.detect_anomalies(
    column="temperature",
    method="hybrid",
    time_window="24h"
)

# 数据可视化
chart = await client.generate_chart(
    chart_type="line",
    columns=["temperature", "pressure"],
    time_range="7d"
)
```

## 🎯 主要优势

1. **本地化部署**: 完全离线运行，数据安全可控
2. **智能交互**: 自然语言处理，操作简单直观
3. **功能完整**: 涵盖数据分析的各个方面
4. **性能优秀**: 高效的数据处理和响应速度
5. **易于扩展**: 模块化设计，便于功能扩展

## 📞 技术支持

如有问题，请查看 `examples/` 目录中的示例代码，或参考项目文档。
