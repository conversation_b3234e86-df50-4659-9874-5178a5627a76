# FastMCP 文档 - 第 12 部分
# 主要内容: `PromptInfo` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/inspect.py#L29"><Icon icon="github" size="14" /></a></sup>
# 包含段落: 130 个
# 总行数: 1038

================================================================================

## `PromptInfo` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/inspect.py#L29"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `PromptInfo` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/inspect.py#L29"><Icon icon="github" size="14" /></a></sup>

Information about a prompt.


------------------------------------------------------------

## `ResourceInfo` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/inspect.py#L41"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `ResourceInfo` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/inspect.py#L41"><Icon icon="github" size="14" /></a></sup>

Information about a resource.


------------------------------------------------------------

## `TemplateInfo` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/inspect.py#L54"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `TemplateInfo` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/inspect.py#L54"><Icon icon="github" size="14" /></a></sup>

Information about a resource template.


------------------------------------------------------------

## `FastMCPInfo` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/inspect.py#L67"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 5

### `FastMCPInfo` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/inspect.py#L67"><Icon icon="github" size="14" /></a></sup>

Information extracted from a FastMCP instance.



------------------------------------------------------------

## json_schema
类型: docs, 行数: 5

# json_schema
Source: https://gofastmcp.com/python-sdk/fastmcp-utilities-json_schema




------------------------------------------------------------

## `fastmcp.utilities.json_schema`
类型: docs, 行数: 2

# `fastmcp.utilities.json_schema`


------------------------------------------------------------

## Functions
类型: api, 行数: 2

## Functions


------------------------------------------------------------

## `compress_schema` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/json_schema.py#L130"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 17

### `compress_schema` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/json_schema.py#L130"><Icon icon="github" size="14" /></a></sup>

```python
compress_schema(schema: dict, prune_params: list[str] | None = None, prune_defs: bool = True, prune_additional_properties: bool = True, prune_titles: bool = False) -> dict
```

Remove the given parameters from the schema.

**Args:**

* `schema`: The schema to compress
* `prune_params`: List of parameter names to remove from properties
* `prune_defs`: Whether to remove unused definitions
* `prune_additional_properties`: Whether to remove additionalProperties: false
* `prune_titles`: Whether to remove title fields from the schema



------------------------------------------------------------

## logging
类型: docs, 行数: 5

# logging
Source: https://gofastmcp.com/python-sdk/fastmcp-utilities-logging




------------------------------------------------------------

## `fastmcp.utilities.logging`
类型: docs, 行数: 4

# `fastmcp.utilities.logging`

Logging utilities for FastMCP.


------------------------------------------------------------

## Functions
类型: api, 行数: 2

## Functions


------------------------------------------------------------

## `get_logger` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/logging.py#L10"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 16

### `get_logger` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/logging.py#L10"><Icon icon="github" size="14" /></a></sup>

```python
get_logger(name: str) -> logging.Logger
```

Get a logger nested under FastMCP namespace.

**Args:**

* `name`: the name of the logger, which will be prefixed with 'FastMCP.'

**Returns:**

* a configured logger instance


------------------------------------------------------------

## `configure_logging` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/logging.py#L22"><Icon icon="github" size="14" /></a></sup>
类型: setup, 行数: 14

### `configure_logging` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/logging.py#L22"><Icon icon="github" size="14" /></a></sup>

```python
configure_logging(level: Literal['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL'] | int = 'INFO', logger: logging.Logger | None = None, enable_rich_tracebacks: bool = True) -> None
```

Configure logging for FastMCP.

**Args:**

* `logger`: the logger to configure
* `level`: the log level to use



------------------------------------------------------------

## mcp_config
类型: setup, 行数: 5

# mcp_config
Source: https://gofastmcp.com/python-sdk/fastmcp-utilities-mcp_config




------------------------------------------------------------

## `fastmcp.utilities.mcp_config`
类型: setup, 行数: 2

# `fastmcp.utilities.mcp_config`


------------------------------------------------------------

## Functions
类型: api, 行数: 2

## Functions


------------------------------------------------------------

## `infer_transport_type_from_url` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/mcp_config.py#L20"><Icon icon="github" size="14" /></a></sup>
类型: setup, 行数: 8

### `infer_transport_type_from_url` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/mcp_config.py#L20"><Icon icon="github" size="14" /></a></sup>

```python
infer_transport_type_from_url(url: str | AnyUrl) -> Literal['http', 'sse']
```

Infer the appropriate transport type from the given URL.


------------------------------------------------------------

## Classes
类型: api, 行数: 2

## Classes


------------------------------------------------------------

## `StdioMCPServer` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/mcp_config.py#L40"><Icon icon="github" size="14" /></a></sup>
类型: setup, 行数: 4

### `StdioMCPServer` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/mcp_config.py#L40"><Icon icon="github" size="14" /></a></sup>

**Methods:**


------------------------------------------------------------

## `to_transport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/mcp_config.py#L47"><Icon icon="github" size="14" /></a></sup>
类型: setup, 行数: 6

#### `to_transport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/mcp_config.py#L47"><Icon icon="github" size="14" /></a></sup>

```python
to_transport(self) -> StdioTransport
```


------------------------------------------------------------

## `RemoteMCPServer` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/mcp_config.py#L58"><Icon icon="github" size="14" /></a></sup>
类型: setup, 行数: 4

### `RemoteMCPServer` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/mcp_config.py#L58"><Icon icon="github" size="14" /></a></sup>

**Methods:**


------------------------------------------------------------

## `to_transport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/mcp_config.py#L71"><Icon icon="github" size="14" /></a></sup>
类型: setup, 行数: 6

#### `to_transport` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/mcp_config.py#L71"><Icon icon="github" size="14" /></a></sup>

```python
to_transport(self) -> StreamableHttpTransport | SSETransport
```


------------------------------------------------------------

## `MCPConfig` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/mcp_config.py#L88"><Icon icon="github" size="14" /></a></sup>
类型: setup, 行数: 4

### `MCPConfig` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/mcp_config.py#L88"><Icon icon="github" size="14" /></a></sup>

**Methods:**


------------------------------------------------------------

## `from_dict` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/mcp_config.py#L92"><Icon icon="github" size="14" /></a></sup>
类型: setup, 行数: 7

#### `from_dict` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/mcp_config.py#L92"><Icon icon="github" size="14" /></a></sup>

```python
from_dict(cls, config: dict[str, Any]) -> MCPConfig
```



------------------------------------------------------------

## openapi
类型: api, 行数: 5

# openapi
Source: https://gofastmcp.com/python-sdk/fastmcp-utilities-openapi




------------------------------------------------------------

## `fastmcp.utilities.openapi`
类型: api, 行数: 2

# `fastmcp.utilities.openapi`


------------------------------------------------------------

## Functions
类型: api, 行数: 2

## Functions


------------------------------------------------------------

## `parse_openapi_to_http_routes` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L112"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 11

### `parse_openapi_to_http_routes` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L112"><Icon icon="github" size="14" /></a></sup>

```python
parse_openapi_to_http_routes(openapi_dict: dict[str, Any]) -> list[HTTPRoute]
```

Parses an OpenAPI schema dictionary into a list of HTTPRoute objects
using the openapi-pydantic library.

Supports both OpenAPI 3.0.x and 3.1.x versions.


------------------------------------------------------------

## `clean_schema_for_display` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L570"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 8

### `clean_schema_for_display` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L570"><Icon icon="github" size="14" /></a></sup>

```python
clean_schema_for_display(schema: JsonSchema | None) -> JsonSchema | None
```

Clean up a schema dictionary for display by removing internal/complex fields.


------------------------------------------------------------

## `generate_example_from_schema` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L630"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 9

### `generate_example_from_schema` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L630"><Icon icon="github" size="14" /></a></sup>

```python
generate_example_from_schema(schema: JsonSchema | None) -> Any
```

Generate a simple example value from a JSON schema dictionary.
Very basic implementation focusing on types.


------------------------------------------------------------

## `format_json_for_description` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L713"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 8

### `format_json_for_description` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L713"><Icon icon="github" size="14" /></a></sup>

```python
format_json_for_description(data: Any, indent: int = 2) -> str
```

Formats Python data as a JSON string block for markdown.


------------------------------------------------------------

## `format_description_with_responses` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L722"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 23

### `format_description_with_responses` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L722"><Icon icon="github" size="14" /></a></sup>

```python
format_description_with_responses(base_description: str, responses: dict[str, Any], parameters: list[ParameterInfo] | None = None, request_body: RequestBodyInfo | None = None) -> str
```

Formats the base description string with response, parameter, and request body information.

**Args:**

* `base_description`: The initial description to be formatted.
* `responses`: A dictionary of response information, keyed by status code.
* `parameters`: A list of parameter information,
  including path and query parameters. Each parameter includes details such as name,
  location, whether it is required, and a description.
* `request_body`: Information about the request body,
  including its description, whether it is required, and its content schema.

**Returns:**

* The formatted description string with additional details about responses, parameters,
* and the request body.


------------------------------------------------------------

## Classes
类型: api, 行数: 2

## Classes


------------------------------------------------------------

## `ParameterInfo` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L42"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 4

### `ParameterInfo` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L42"><Icon icon="github" size="14" /></a></sup>

Represents a single parameter for an HTTP operation in our IR.


------------------------------------------------------------

## `RequestBodyInfo` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L52"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 4

### `RequestBodyInfo` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L52"><Icon icon="github" size="14" /></a></sup>

Represents the request body for an HTTP operation in our IR.


------------------------------------------------------------

## `ResponseInfo` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L62"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 4

### `ResponseInfo` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L62"><Icon icon="github" size="14" /></a></sup>

Represents response information in our IR.


------------------------------------------------------------

## `HTTPRoute` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L70"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 4

### `HTTPRoute` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L70"><Icon icon="github" size="14" /></a></sup>

Intermediate Representation for a single OpenAPI operation.


------------------------------------------------------------

## `OpenAPIParser` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L164"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 6

### `OpenAPIParser` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L164"><Icon icon="github" size="14" /></a></sup>

Unified parser for OpenAPI schemas with generic type parameters to handle both 3.0 and 3.1.

**Methods:**


------------------------------------------------------------

## `parse` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L469"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 9

#### `parse` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/openapi.py#L469"><Icon icon="github" size="14" /></a></sup>

```python
parse(self) -> list[HTTPRoute]
```

Parse the OpenAPI schema into HTTP routes.



------------------------------------------------------------

## tests
类型: docs, 行数: 5

# tests
Source: https://gofastmcp.com/python-sdk/fastmcp-utilities-tests




------------------------------------------------------------

## `fastmcp.utilities.tests`
类型: docs, 行数: 2

# `fastmcp.utilities.tests`


------------------------------------------------------------

## Functions
类型: api, 行数: 2

## Functions


------------------------------------------------------------

## `temporary_settings` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/tests.py#L21"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 12

### `temporary_settings` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/tests.py#L21"><Icon icon="github" size="14" /></a></sup>

```python
temporary_settings(**kwargs: Any)
```

Temporarily override FastMCP setting values.

**Args:**

* `**kwargs`: The settings to override, including nested settings.


------------------------------------------------------------

## `run_server_in_process` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/tests.py#L74"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 22

### `run_server_in_process` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/tests.py#L74"><Icon icon="github" size="14" /></a></sup>

```python
run_server_in_process(server_fn: Callable[..., None], *args, **kwargs) -> Generator[str, None, None]
```

Context manager that runs a FastMCP server in a separate process and
returns the server URL. When the context manager is exited, the server process is killed.

**Args:**

* `server_fn`: The function that runs a FastMCP server. FastMCP servers are
  not pickleable, so we need a function that creates and runs one.
* `*args`: Arguments to pass to the server function.
* `provide_host_and_port`: Whether to provide the host and port to the server function as kwargs.
* `**kwargs`: Keyword arguments to pass to the server function.

**Returns:**

* The server URL.



------------------------------------------------------------

## types
类型: docs, 行数: 5

# types
Source: https://gofastmcp.com/python-sdk/fastmcp-utilities-types




------------------------------------------------------------

## `fastmcp.utilities.types`
类型: docs, 行数: 4

# `fastmcp.utilities.types`

Common types used across FastMCP.


------------------------------------------------------------

## Functions
类型: api, 行数: 2

## Functions


------------------------------------------------------------

## `get_cached_typeadapter` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L35"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 11

### `get_cached_typeadapter` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L35"><Icon icon="github" size="14" /></a></sup>

```python
get_cached_typeadapter(cls: T) -> TypeAdapter[T]
```

TypeAdapters are heavy objects, and in an application context we'd typically
create them once in a global scope and reuse them as often as possible.
However, this isn't feasible for user-generated functions. Instead, we use a
cache to minimize the cost of creating them as much as possible.


------------------------------------------------------------

## `issubclass_safe` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L45"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 8

### `issubclass_safe` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L45"><Icon icon="github" size="14" /></a></sup>

```python
issubclass_safe(cls: type, base: type) -> bool
```

Check if cls is a subclass of base, even if cls is a type variable.


------------------------------------------------------------

## `is_class_member_of_type` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L55"><Icon icon="github" size="14" /></a></sup>
类型: api, 行数: 11

### `is_class_member_of_type` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L55"><Icon icon="github" size="14" /></a></sup>

```python
is_class_member_of_type(cls: type, base: type) -> bool
```

Check if cls is a member of base, even if cls is a type variable.

Base can be a type, a UnionType, or an Annotated type. Generic types are not
considered members (e.g. T is not a member of list\[T]).


------------------------------------------------------------

## `find_kwarg_by_type` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L77"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 10

### `find_kwarg_by_type` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L77"><Icon icon="github" size="14" /></a></sup>

```python
find_kwarg_by_type(fn: Callable, kwarg_type: type) -> str | None
```

Find the name of the kwarg that is of type kwarg\_type.

Includes union types that contain the kwarg\_type, as well as Annotated types.


------------------------------------------------------------

## Classes
类型: api, 行数: 2

## Classes


------------------------------------------------------------

## `FastMCPBaseModel` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L28"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 4

### `FastMCPBaseModel` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L28"><Icon icon="github" size="14" /></a></sup>

Base model for FastMCP models.


------------------------------------------------------------

## `Image` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L94"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 6

### `Image` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L94"><Icon icon="github" size="14" /></a></sup>

Helper class for returning images from tools.

**Methods:**


------------------------------------------------------------

## `to_image_content` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L131"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 8

#### `to_image_content` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L131"><Icon icon="github" size="14" /></a></sup>

```python
to_image_content(self, mime_type: str | None = None, annotations: Annotations | None = None) -> ImageContent
```

Convert to MCP ImageContent.


------------------------------------------------------------

## `Audio` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L153"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 6

### `Audio` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L153"><Icon icon="github" size="14" /></a></sup>

Helper class for returning audio from tools.

**Methods:**


------------------------------------------------------------

## `to_audio_content` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L190"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 6

#### `to_audio_content` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L190"><Icon icon="github" size="14" /></a></sup>

```python
to_audio_content(self, mime_type: str | None = None, annotations: Annotations | None = None) -> AudioContent
```


------------------------------------------------------------

## `File` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L211"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 6

### `File` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L211"><Icon icon="github" size="14" /></a></sup>

Helper class for returning audio from tools.

**Methods:**


------------------------------------------------------------

## `to_resource_content` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L250"><Icon icon="github" size="14" /></a></sup>
类型: docs, 行数: 7

#### `to_resource_content` <sup><a href="https://github.com/jlowin/fastmcp/blob/main/src/fastmcp/utilities/types.py#L250"><Icon icon="github" size="14" /></a></sup>

```python
to_resource_content(self, mime_type: str | None = None, annotations: Annotations | None = None) -> EmbeddedResource
```



------------------------------------------------------------

## Bearer Token Authentication
类型: docs, 行数: 28

# Bearer Token Authentication
Source: https://gofastmcp.com/servers/auth/bearer

Secure your FastMCP server's HTTP endpoints by validating JWT Bearer tokens.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

<VersionBadge version="2.6.0" />

<Tip>
  Authentication and authorization are only relevant for HTTP-based transports.
</Tip>

<Note>
  The [MCP specification](https://modelcontextprotocol.io/specification/2025-03-26/basic/authorization) requires servers to implement full OAuth 2.1 authorization flows with dynamic client registration, server metadata discovery, and complete token endpoints. FastMCP's Bearer Token authentication provides a simpler, more practical alternative by directly validating pre-issued JWT tokens—ideal for service-to-service communication and programmatic environments where full OAuth flows may be impractical, and in accordance with how the MCP ecosystem is pragmatically evolving. However, please note that since it doesn't implement the full OAuth 2.1 flow, this implementation does not strictly comply with the MCP specification.
</Note>

Bearer Token authentication is a common way to secure HTTP-based APIs. In this model, the client sends a token (usually a JSON Web Token or JWT) in the `Authorization` header with the "Bearer" scheme. The server then validates this token to grant or deny access.

FastMCP supports Bearer Token authentication for its HTTP-based transports (`http` and `sse`), allowing you to protect your server from unauthorized access.


------------------------------------------------------------

## Authentication Strategy
类型: docs, 行数: 11

## Authentication Strategy

FastMCP uses **asymmetric encryption** for token validation, which provides a clean security separation between token issuers and FastMCP servers. This approach means:

* **No shared secrets**: Your FastMCP server never needs access to private keys or client secrets
* **Public key verification**: The server only needs a public key (or JWKS endpoint) to verify token signatures
* **Secure token issuance**: Tokens are signed by an external service using a private key that never leaves the issuer
* **Scalable architecture**: Multiple FastMCP servers can validate tokens without coordinating secrets

This design allows you to integrate FastMCP servers into existing authentication infrastructures without compromising security boundaries.


------------------------------------------------------------

## Configuration
类型: setup, 行数: 8

## Configuration

To enable Bearer Token validation on your FastMCP server, use the `BearerAuthProvider` class. This provider validates incoming JWTs by verifying signatures, checking expiration, and optionally validating claims.

<Warning>
  The `BearerAuthProvider` validates tokens; it does **not** issue them (or implement any part of an OAuth flow). You'll need to generate tokens separately, either using FastMCP utilities or an external Identity Provider (IdP) or OAuth 2.1 Authorization Server.
</Warning>


------------------------------------------------------------

## Basic Setup
类型: setup, 行数: 20

### Basic Setup

To configure bearer token authentication, instantiate a `BearerAuthProvider` instance and pass it to the `auth` parameter of the `FastMCP` instance.

The `BearerAuthProvider` requires either a static public key or a JWKS URI (but not both!) in order to verify the token's signature. All other parameters are optional -- if they are provided, they will be used as additional validation criteria.

```python {2, 10}
from fastmcp import FastMCP
from fastmcp.server.auth import BearerAuthProvider

auth = BearerAuthProvider(
    jwks_uri="https://my-identity-provider.com/.well-known/jwks.json",
    issuer="https://my-identity-provider.com/",
    algorithm="RS512",
    audience="my-mcp-server"
)

mcp = FastMCP(name="My MCP Server", auth=auth)
```


------------------------------------------------------------

## Configuration Parameters
类型: setup, 行数: 28

### Configuration Parameters

<Card icon="code" title="BearerAuthProvider Configuration">
  <ParamField body="public_key" type="str">
    RSA public key in PEM format for static key validation. Required if `jwks_uri` is not provided
  </ParamField>

  <ParamField body="jwks_uri" type="str">
    URL for JSON Web Key Set endpoint. Required if `public_key` is not provided
  </ParamField>

  <ParamField body="issuer" type="str | None">
    Expected JWT `iss` claim value
  </ParamField>

  <ParamField body="algorithm" type="str | None">
    Algorithm for decoding JWT token. Defaults to 'RS256'
  </ParamField>

  <ParamField body="audience" type="str | None">
    Expected JWT `aud` claim value
  </ParamField>

  <ParamField body="required_scopes" type="list[str] | None">
    Global scopes required for all requests
  </ParamField>
</Card>


------------------------------------------------------------

## Public Key
类型: docs, 行数: 19

#### Public Key

If you have a public key in PEM format, you can provide it to the `BearerAuthProvider` as a string.

```python {12}
from fastmcp.server.auth import BearerAuthProvider
import inspect

public_key_pem = inspect.cleandoc(
    """
    -----BEGIN PUBLIC KEY-----
    MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAy...
    -----END PUBLIC KEY-----
    """
)

auth = BearerAuthProvider(public_key=public_key_pem)
```


------------------------------------------------------------

## JWKS URI
类型: docs, 行数: 12

#### JWKS URI

```python
provider = BearerAuthProvider(
    jwks_uri="https://idp.example.com/.well-known/jwks.json"
)
```

<Note>
  JWKS is recommended for production as it supports automatic key rotation and multiple signing keys.
</Note>


------------------------------------------------------------

## Generating Tokens
类型: docs, 行数: 8

## Generating Tokens

For development and testing, FastMCP provides the `RSAKeyPair` utility class to generate tokens without needing an external OAuth provider.

<Warning>
  The `RSAKeyPair` utility is intended for development and testing only. For production, use a proper OAuth 2.1 Authorization Server or Identity Provider.
</Warning>


------------------------------------------------------------

## Basic Token Generation
类型: docs, 行数: 7

### Basic Token Generation

```python
from fastmcp import FastMCP
from fastmcp.server.auth import BearerAuthProvider
from fastmcp.server.auth.providers.bearer import RSAKeyPair


------------------------------------------------------------

## Generate a new key pair
类型: docs, 行数: 3

# Generate a new key pair
key_pair = RSAKeyPair.generate()


------------------------------------------------------------

## Configure the auth provider with the public key
类型: setup, 行数: 9

# Configure the auth provider with the public key
auth = BearerAuthProvider(
    public_key=key_pair.public_key,
    issuer="https://dev.example.com",
    audience="my-dev-server"
)

mcp = FastMCP(name="Development Server", auth=auth)


------------------------------------------------------------

## Generate a token for testing
类型: docs, 行数: 11

# Generate a token for testing
token = key_pair.create_token(
    subject="dev-user",
    issuer="https://dev.example.com",
    audience="my-dev-server",
    scopes=["read", "write"]
)

print(f"Test token: {token}")
```


------------------------------------------------------------

## Token Creation Parameters
类型: docs, 行数: 34

### Token Creation Parameters

The `create_token()` method accepts these parameters:

<Card icon="code" title="create_token() Parameters">
  <ParamField body="subject" type="str" default="fastmcp-user">
    JWT subject claim (usually user ID)
  </ParamField>

  <ParamField body="issuer" type="str" default="https://fastmcp.example.com">
    JWT issuer claim
  </ParamField>

  <ParamField body="audience" type="str | None">
    JWT audience claim
  </ParamField>

  <ParamField body="scopes" type="list[str] | None">
    OAuth scopes to include
  </ParamField>

  <ParamField body="expires_in_seconds" type="int" default="3600">
    Token expiration time in seconds
  </ParamField>

  <ParamField body="additional_claims" type="dict | None">
    Extra claims to include in the token
  </ParamField>

  <ParamField body="kid" type="str | None">
    Key ID for JWKS lookup
  </ParamField>
</Card>


------------------------------------------------------------

## Accessing Token Claims
类型: docs, 行数: 25

## Accessing Token Claims

Once authenticated, your tools, resources, or prompts can access token information using the `get_access_token()` dependency function:

```python
from fastmcp import FastMCP, Context, ToolError
from fastmcp.server.dependencies import get_access_token, AccessToken

@mcp.tool
async def get_my_data(ctx: Context) -> dict:
    access_token: AccessToken = get_access_token()
    
    user_id = access_token.client_id  # From JWT 'sub' or 'client_id' claim
    user_scopes = access_token.scopes
    
    if "data:read_sensitive" not in user_scopes:
        raise ToolError("Insufficient permissions: 'data:read_sensitive' scope required.")
    
    return {
        "user": user_id,
        "sensitive_data": f"Private data for {user_id}",
        "granted_scopes": user_scopes
    }
```


------------------------------------------------------------

## AccessToken Properties
类型: docs, 行数: 21

### AccessToken Properties

<Card icon="code" title="AccessToken Properties">
  <ParamField body="token" type="str">
    The raw JWT string
  </ParamField>

  <ParamField body="client_id" type="str">
    Authenticated principal identifier
  </ParamField>

  <ParamField body="scopes" type="list[str]">
    Granted scopes
  </ParamField>

  <ParamField body="expires_at" type="datetime | None">
    Token expiration timestamp
  </ParamField>
</Card>



------------------------------------------------------------

## Server Composition
类型: docs, 行数: 21

# Server Composition
Source: https://gofastmcp.com/servers/composition

Combine multiple FastMCP servers into a single, larger application using mounting and importing.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

<VersionBadge version="2.2.0" />

As your MCP applications grow, you might want to organize your tools, resources, and prompts into logical modules or reuse existing server components. FastMCP supports composition through two methods:

* **`import_server`**: For a one-time copy of components with prefixing (static composition).
* **`mount`**: For creating a live link where the main server delegates requests to the subserver (dynamic composition).


------------------------------------------------------------

## Why Compose Servers?
类型: docs, 行数: 7

## Why Compose Servers?

* **Modularity**: Break down large applications into smaller, focused servers (e.g., a `WeatherServer`, a `DatabaseServer`, a `CalendarServer`).
* **Reusability**: Create common utility servers (e.g., a `TextProcessingServer`) and mount them wherever needed.
* **Teamwork**: Different teams can work on separate FastMCP servers that are later combined.
* **Organization**: Keep related functionality grouped together logically.


------------------------------------------------------------

## Importing vs Mounting
类型: docs, 行数: 12

### Importing vs Mounting

The choice of importing or mounting depends on your use case and requirements.

| Feature              | Importing                                    | Mounting                                   |
| -------------------- | -------------------------------------------- | ------------------------------------------ |
| **Method**           | `FastMCP.import_server(server, prefix=None)` | `FastMCP.mount(server, prefix=None)`       |
| **Composition Type** | One-time copy (static)                       | Live link (dynamic)                        |
| **Updates**          | Changes to subserver NOT reflected           | Changes to subserver immediately reflected |
| **Prefix**           | Optional - omit for original names           | Optional - omit for original names         |
| **Best For**         | Bundling finalized components                | Modular runtime composition                |


------------------------------------------------------------

## Proxy Servers
类型: docs, 行数: 8

### Proxy Servers

FastMCP supports [MCP proxying](/servers/proxy), which allows you to mirror a local or remote server in a local FastMCP instance. Proxies are fully compatible with both importing and mounting.

<VersionBadge version="2.4.0" />

You can also create proxies from configuration dictionaries that follow the MCPConfig schema, which is useful for quickly connecting to one or more remote servers. See the [Proxy Servers documentation](/servers/proxy#configuration-based-proxies) for details on configuration-based proxying. Note that MCPConfig follows an emerging standard and its format may evolve over time.


------------------------------------------------------------

## Importing (Static Composition)
类型: docs, 行数: 8

## Importing (Static Composition)

The `import_server()` method copies all components (tools, resources, templates, prompts) from one `FastMCP` instance (the *subserver*) into another (the *main server*). An optional `prefix` can be provided to avoid naming conflicts. If no prefix is provided, components are imported without modification. When multiple servers are imported with the same prefix (or no prefix), the most recently imported server's components take precedence.

```python
from fastmcp import FastMCP
import asyncio


------------------------------------------------------------

## Define subservers
类型: docs, 行数: 13

# Define subservers
weather_mcp = FastMCP(name="WeatherService")

@weather_mcp.tool
def get_forecast(city: str) -> dict:
    """Get weather forecast."""
    return {"city": city, "forecast": "Sunny"}

@weather_mcp.resource("data://cities/supported")
def list_supported_cities() -> list[str]:
    """List cities with weather support."""
    return ["London", "Paris", "Tokyo"]


------------------------------------------------------------

## Define main server
类型: docs, 行数: 3

# Define main server
main_mcp = FastMCP(name="MainApp")


------------------------------------------------------------

## Import subserver
类型: docs, 行数: 4

# Import subserver
async def setup():
    await main_mcp.import_server(weather_mcp, prefix="weather")


------------------------------------------------------------

## Result: main_mcp now contains prefixed components:
类型: docs, 行数: 1

# Result: main_mcp now contains prefixed components:

------------------------------------------------------------

## - Tool: "weather_get_forecast"
类型: docs, 行数: 1

# - Tool: "weather_get_forecast"

------------------------------------------------------------

## - Resource: "data://weather/cities/supported"
类型: docs, 行数: 7

# - Resource: "data://weather/cities/supported" 

if __name__ == "__main__":
    asyncio.run(setup())
    main_mcp.run()
```


------------------------------------------------------------

## How Importing Works
类型: docs, 行数: 19

### How Importing Works

When you call `await main_mcp.import_server(subserver, prefix={whatever})`:

1. **Tools**: All tools from `subserver` are added to `main_mcp` with names prefixed using `{prefix}_`.
   * `subserver.tool(name="my_tool")` becomes `main_mcp.tool(name="{prefix}_my_tool")`.
2. **Resources**: All resources are added with URIs prefixed in the format `protocol://{prefix}/path`.
   * `subserver.resource(uri="data://info")` becomes `main_mcp.resource(uri="data://{prefix}/info")`.
3. **Resource Templates**: Templates are prefixed similarly to resources.
   * `subserver.resource(uri="data://{id}")` becomes `main_mcp.resource(uri="data://{prefix}/{id}")`.
4. **Prompts**: All prompts are added with names prefixed using `{prefix}_`.
   * `subserver.prompt(name="my_prompt")` becomes `main_mcp.prompt(name="{prefix}_my_prompt")`.

Note that `import_server` performs a **one-time copy** of components. Changes made to the `subserver` *after* importing **will not** be reflected in `main_mcp`. The `subserver`'s `lifespan` context is also **not** executed by the main server.

<Tip>
  The `prefix` parameter is optional. If omitted, components are imported without modification.
</Tip>


------------------------------------------------------------

## Importing Without Prefixes
类型: docs, 行数: 11

#### Importing Without Prefixes

<VersionBadge version="2.9.0" />

You can also import servers without specifying a prefix, which copies components using their original names:

```python

from fastmcp import FastMCP
import asyncio


------------------------------------------------------------

## Define subservers
类型: docs, 行数: 13

# Define subservers
weather_mcp = FastMCP(name="WeatherService")

@weather_mcp.tool
def get_forecast(city: str) -> dict:
    """Get weather forecast."""
    return {"city": city, "forecast": "Sunny"}

@weather_mcp.resource("data://cities/supported")
def list_supported_cities() -> list[str]:
    """List cities with weather support."""
    return ["London", "Paris", "Tokyo"]


------------------------------------------------------------

## Define main server
类型: docs, 行数: 3

# Define main server
main_mcp = FastMCP(name="MainApp")


------------------------------------------------------------

## Import subserver
类型: docs, 行数: 2

# Import subserver
async def setup():

------------------------------------------------------------

## Import without prefix - components keep original names
类型: docs, 行数: 3

    # Import without prefix - components keep original names
    await main_mcp.import_server(weather_mcp)


------------------------------------------------------------

## Result: main_mcp now contains:
类型: docs, 行数: 1

# Result: main_mcp now contains:

------------------------------------------------------------

## - Tool: "get_forecast" (original name preserved)
类型: docs, 行数: 1

# - Tool: "get_forecast" (original name preserved)

------------------------------------------------------------

## - Resource: "data://cities/supported" (original URI preserved)
类型: docs, 行数: 7

# - Resource: "data://cities/supported" (original URI preserved)

if __name__ == "__main__":
    asyncio.run(setup())
    main_mcp.run()
```


------------------------------------------------------------

## Conflict Resolution
类型: docs, 行数: 6

#### Conflict Resolution

<VersionBadge version="2.9.0" />

When importing multiple servers with the same prefix, or no prefix, components from the **most recently imported** server take precedence.


------------------------------------------------------------

## Mounting (Live Linking)
类型: docs, 行数: 8

## Mounting (Live Linking)

The `mount()` method creates a **live link** between the `main_mcp` server and the `subserver`. Instead of copying components, requests for components matching the optional `prefix` are **delegated** to the `subserver` at runtime. If no prefix is provided, the subserver's components are accessible without prefixing. When multiple servers are mounted with the same prefix (or no prefix), the most recently mounted server takes precedence for conflicting component names.

```python
import asyncio
from fastmcp import FastMCP, Client


------------------------------------------------------------

## Define subserver
类型: docs, 行数: 8

# Define subserver
dynamic_mcp = FastMCP(name="DynamicService")

@dynamic_mcp.tool
def initial_tool():
    """Initial tool demonstration."""
    return "Initial Tool Exists"


------------------------------------------------------------

## Mount subserver (synchronous operation)
类型: docs, 行数: 4

# Mount subserver (synchronous operation)
main_mcp = FastMCP(name="MainAppLive")
main_mcp.mount(dynamic_mcp, prefix="dynamic")


------------------------------------------------------------

## Add a tool AFTER mounting - it will be accessible through main_mcp
类型: docs, 行数: 6

# Add a tool AFTER mounting - it will be accessible through main_mcp
@dynamic_mcp.tool
def added_later():
    """Tool added after mounting."""
    return "Tool Added Dynamically!"


------------------------------------------------------------

## Testing access to mounted tools
类型: docs, 行数: 4

# Testing access to mounted tools
async def test_dynamic_mount():
    tools = await main_mcp.get_tools()
    print("Available tools:", list(tools.keys()))

------------------------------------------------------------

## Shows: ['dynamic_initial_tool', 'dynamic_added_later']
类型: docs, 行数: 5

    # Shows: ['dynamic_initial_tool', 'dynamic_added_later']
    
    async with Client(main_mcp) as client:
        result = await client.call_tool("dynamic_added_later")
        print("Result:", result.data)

------------------------------------------------------------

## Shows: "Tool Added Dynamically!"
类型: docs, 行数: 6

        # Shows: "Tool Added Dynamically!"

if __name__ == "__main__":
    asyncio.run(test_dynamic_mount())
```


------------------------------------------------------------

## How Mounting Works
类型: docs, 行数: 15

### How Mounting Works

When mounting is configured:

1. **Live Link**: The parent server establishes a connection to the mounted server.
2. **Dynamic Updates**: Changes to the mounted server are immediately reflected when accessed through the parent.
3. **Prefixed Access**: The parent server uses prefixes to route requests to the mounted server.
4. **Delegation**: Requests for components matching the prefix are delegated to the mounted server at runtime.

The same prefixing rules apply as with `import_server` for naming tools, resources, templates, and prompts.

<Tip>
  The `prefix` parameter is optional. If omitted, components are mounted without modification.
</Tip>


------------------------------------------------------------

## Mounting Without Prefixes
类型: docs, 行数: 6

#### Mounting Without Prefixes

<VersionBadge version="2.9.0" />

You can also mount servers without specifying a prefix, which makes components accessible without prefixing. This works identically to [importing without prefixes](#importing-without-prefixes), including [conflict resolution](#conflict-resolution).


------------------------------------------------------------

## Direct vs. Proxy Mounting
类型: docs, 行数: 16

### Direct vs. Proxy Mounting

<VersionBadge version="2.2.7" />

FastMCP supports two mounting modes:

1. **Direct Mounting** (default): The parent server directly accesses the mounted server's objects in memory.
   * No client lifecycle events occur on the mounted server
   * The mounted server's lifespan context is not executed
   * Communication is handled through direct method calls
2. **Proxy Mounting**: The parent server treats the mounted server as a separate entity and communicates with it through a client interface.
   * Full client lifecycle events occur on the mounted server
   * The mounted server's lifespan is executed when a client connects
   * Communication happens via an in-memory Client transport

```python

------------------------------------------------------------

## Direct mounting (default when no custom lifespan)
类型: docs, 行数: 3

# Direct mounting (default when no custom lifespan)
main_mcp.mount(api_server, prefix="api")


------------------------------------------------------------

## Proxy mounting (preserves full client lifecycle)
类型: docs, 行数: 3

# Proxy mounting (preserves full client lifecycle)
main_mcp.mount(api_server, prefix="api", as_proxy=True)


------------------------------------------------------------

## Mounting without a prefix (components accessible without prefixing)
类型: docs, 行数: 6

# Mounting without a prefix (components accessible without prefixing)
main_mcp.mount(api_server)
```

FastMCP automatically uses proxy mounting when the mounted server has a custom lifespan, but you can override this behavior with the `as_proxy` parameter.


------------------------------------------------------------

## Interaction with Proxy Servers
类型: docs, 行数: 5

#### Interaction with Proxy Servers

When using `FastMCP.as_proxy()` to create a proxy server, mounting that server will always use proxy mounting:

```python

------------------------------------------------------------

## Create a proxy for a remote server
类型: docs, 行数: 3

# Create a proxy for a remote server
remote_proxy = FastMCP.as_proxy(Client("http://example.com/mcp"))


------------------------------------------------------------

## Mount the proxy (always uses proxy mounting)
类型: docs, 行数: 4

# Mount the proxy (always uses proxy mounting)
main_server.mount(remote_proxy, prefix="remote")
```


------------------------------------------------------------

## Resource Prefix Formats
类型: docs, 行数: 6

## Resource Prefix Formats

<VersionBadge version="2.4.0" />

When mounting or importing servers, resource URIs are usually prefixed to avoid naming conflicts. FastMCP supports two different formats for resource prefixes:


------------------------------------------------------------

## Path Format (Default)
类型: docs, 行数: 10

### Path Format (Default)

In path format, prefixes are added to the path component of the URI:

```
resource://prefix/path/to/resource
```

This is the default format since FastMCP 2.4. This format is recommended because it avoids issues with URI protocol restrictions (like underscores not being allowed in protocol names).


------------------------------------------------------------

## Protocol Format (Legacy)
类型: docs, 行数: 10

### Protocol Format (Legacy)

In protocol format, prefixes are added as part of the protocol:

```
prefix+resource://path/to/resource
```

This was the default format in FastMCP before 2.4. While still supported, it's not recommended for new code as it can cause problems with prefix names that aren't valid in URI protocols.


------------------------------------------------------------

## Configuring the Prefix Format
类型: setup, 行数: 20

### Configuring the Prefix Format

You can configure the prefix format globally in code:

```python
import fastmcp
fastmcp.settings.resource_prefix_format = "protocol" 
```

Or via environment variable:

```bash
FASTMCP_RESOURCE_PREFIX_FORMAT=protocol
```

Or per-server:

```python
from fastmcp import FastMCP


------------------------------------------------------------

## Create a server that uses legacy protocol format
类型: docs, 行数: 3

# Create a server that uses legacy protocol format
server = FastMCP("LegacyServer", resource_prefix_format="protocol")


------------------------------------------------------------

## Create a server that uses new path format
类型: docs, 行数: 7

# Create a server that uses new path format
server = FastMCP("NewServer", resource_prefix_format="path")
```

When mounting or importing servers, the prefix format of the parent server is used.



------------------------------------------------------------

## MCP Context
类型: docs, 行数: 16

# MCP Context
Source: https://gofastmcp.com/servers/context

Access MCP capabilities like logging, progress, and resources within your MCP objects.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

When defining FastMCP [tools](/servers/tools), [resources](/servers/resources), resource templates, or [prompts](/servers/prompts), your functions might need to interact with the underlying MCP session or access advanced server capabilities. FastMCP provides the `Context` object for this purpose.


------------------------------------------------------------

## What Is Context?
类型: docs, 行数: 12

## What Is Context?

The `Context` object provides a clean interface to access MCP features within your functions, including:

* **Logging**: Send debug, info, warning, and error messages back to the client
* **Progress Reporting**: Update the client on the progress of long-running operations
* **Resource Access**: Read data from resources registered with the server
* **LLM Sampling**: Request the client's LLM to generate text based on provided messages
* **User Elicitation**: Request structured input from users during tool execution
* **Request Information**: Access metadata about the current request
* **Server Access**: When needed, access the underlying FastMCP server instance


------------------------------------------------------------

## Accessing the Context
类型: docs, 行数: 2

## Accessing the Context


------------------------------------------------------------

## Via Dependency Injection
类型: docs, 行数: 13

### Via Dependency Injection

To use the context object within any of your functions, simply add a parameter to your function signature and type-hint it as `Context`. FastMCP will automatically inject the context instance when your function is called.

**Key Points:**

* The parameter name (e.g., `ctx`, `context`) doesn't matter, only the type hint `Context` is important.
* The context parameter can be placed anywhere in your function's signature; it will not be exposed to MCP clients as a valid parameter.
* The context is optional - functions that don't need it can omit the parameter entirely.
* Context methods are async, so your function usually needs to be async as well.
* The type hint can be a union (`Context | None`) or use `Annotated[]` and it will still work properly.
* Context is only available during a request; attempting to use context methods outside a request will raise errors. If you need to debug or call your context methods outside of a request, you can type your variable as `Context | None=None` to avoid missing argument errors.


------------------------------------------------------------

## Tools
类型: docs, 行数: 10

#### Tools

```python {1, 6}
from fastmcp import FastMCP, Context

mcp = FastMCP(name="Context Demo")

@mcp.tool
async def process_file(file_uri: str, ctx: Context) -> str:
    """Processes a file, using context for logging and resource access."""

------------------------------------------------------------

## Context is available as the ctx parameter
类型: docs, 行数: 4

    # Context is available as the ctx parameter
    return "Processed file"
```


------------------------------------------------------------

## Resources and Templates
类型: docs, 行数: 12

#### Resources and Templates

<VersionBadge version="2.2.5" />

```python {1, 6, 12}
from fastmcp import FastMCP, Context

mcp = FastMCP(name="Context Demo")

@mcp.resource("resource://user-data")
async def get_user_data(ctx: Context) -> dict:
    """Fetch personalized user data based on the request context."""

------------------------------------------------------------

## Context is available as the ctx parameter
类型: docs, 行数: 6

    # Context is available as the ctx parameter
    return {"user_id": "example"}

@mcp.resource("resource://users/{user_id}/profile")
async def get_user_profile(user_id: str, ctx: Context) -> dict:
    """Fetch user profile with context-aware logging."""

------------------------------------------------------------

## Context is available as the ctx parameter
类型: docs, 行数: 4

    # Context is available as the ctx parameter
    return {"id": user_id}
```


------------------------------------------------------------

## Prompts
类型: docs, 行数: 12

#### Prompts

<VersionBadge version="2.2.5" />

```python {1, 6}
from fastmcp import FastMCP, Context

mcp = FastMCP(name="Context Demo")

@mcp.prompt
async def data_analysis_request(dataset: str, ctx: Context) -> str:
    """Generate a request to analyze data with contextual information."""

------------------------------------------------------------

## Context is available as the ctx parameter
类型: docs, 行数: 4

    # Context is available as the ctx parameter
    return f"Please analyze the following dataset: {dataset}"
```


------------------------------------------------------------

## Via Dependency Function
类型: api, 行数: 14

### Via Dependency Function

<VersionBadge version="2.2.11" />

While the simplest way to access context is through function parameter injection as shown above, there are cases where you need to access the context in code that may not be easy to modify to accept a context parameter, or that is nested deeper within your function calls.

FastMCP provides dependency functions that allow you to retrieve the active context from anywhere within a server request's execution flow:

```python {2,9}
from fastmcp import FastMCP
from fastmcp.server.dependencies import get_context

mcp = FastMCP(name="Dependency Demo")


------------------------------------------------------------

## Utility function that needs context but doesn't receive it as a parameter
类型: api, 行数: 2

# Utility function that needs context but doesn't receive it as a parameter
async def process_data(data: list[float]) -> dict:

------------------------------------------------------------

