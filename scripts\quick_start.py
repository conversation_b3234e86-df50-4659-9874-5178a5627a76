#!/usr/bin/env python3
"""
数据库分析MCP服务器快速启动脚本
自动检查环境、创建数据库、安装依赖并启动服务
"""

import os
import sys
import subprocess
import mysql.connector
from pathlib import Path

def check_mysql_connection():
    """检查MySQL连接"""
    print("🔍 检查MySQL数据库连接...")
    
    try:
        connection = mysql.connector.connect(
            host="localhost",
            port=3306,
            user="root",
            password="123456"
        )
        
        cursor = connection.cursor()
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()
        print(f"✅ MySQL连接成功，版本: {version[0]}")
        
        # 检查数据库是否存在
        cursor.execute("SHOW DATABASES LIKE 'sensor_data'")
        db_exists = cursor.fetchone()
        
        if db_exists:
            print("✅ sensor_data数据库已存在")
        else:
            print("⚠️ sensor_data数据库不存在，将自动创建")
            return False
            
        cursor.close()
        connection.close()
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ MySQL连接失败: {e}")
        print("请检查:")
        print("  1. MySQL服务是否启动")
        print("  2. 用户名密码是否正确")
        print("  3. 端口是否正确")
        return False

def create_database():
    """创建数据库和表"""
    print("🗄️ 创建数据库和示例数据...")
    
    try:
        # 读取简化SQL脚本
        sql_file = Path("create_database_simple.sql")
        if not sql_file.exists():
            print("❌ create_database_simple.sql文件不存在")
            return False

        with open(sql_file, 'r', encoding='utf-8') as f:
            sql_content = f.read()
        
        # 连接MySQL并执行脚本
        connection = mysql.connector.connect(
            host="localhost",
            port=3306,
            user="root",
            password="123456"
        )
        
        cursor = connection.cursor()
        
        # 分割SQL语句并执行
        statements = sql_content.split(';')
        for statement in statements:
            statement = statement.strip()
            if statement and not statement.startswith('--'):
                try:
                    cursor.execute(statement)
                except mysql.connector.Error as e:
                    if "already exists" not in str(e).lower():
                        print(f"⚠️ SQL执行警告: {e}")
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("✅ 数据库创建完成")
        return True
        
    except Exception as e:
        print(f"❌ 数据库创建失败: {e}")
        return False

def install_dependencies():
    """安装Python依赖"""
    print("📦 安装Python依赖包...")
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ])
        print("✅ 依赖包安装完成")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖安装失败: {e}")
        return False

def test_server():
    """测试服务器功能"""
    print("🧪 测试服务器功能...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_server.py"
        ], capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ 服务器功能测试通过")
            return True
        else:
            print("❌ 服务器功能测试失败")
            print("错误输出:")
            print(result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ 测试超时，但这可能是正常的")
        return True
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False

def install_to_claude():
    """安装到Claude Desktop"""
    print("🔧 安装到Claude Desktop...")
    
    try:
        # 检查fastmcp是否已安装
        try:
            subprocess.check_call(["fastmcp", "--version"], 
                                stdout=subprocess.DEVNULL, 
                                stderr=subprocess.DEVNULL)
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("📦 安装FastMCP...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", "fastmcp"])
        
        # 获取当前目录
        current_dir = Path.cwd()
        server_path = current_dir / "database_analysis_server_simple.py"
        env_path = current_dir / ".env"
        
        # 构建安装命令
        cmd = [
            "fastmcp", "install", "claude-desktop", str(server_path),
            "--name", "数据库分析助手",
            "--env-file", str(env_path)
        ]
        
        # 添加依赖
        dependencies = [
            "mysql-connector-python",
            "pandas", 
            "numpy",
            "matplotlib",
            "plotly",
            "speechrecognition",
            "pyttsx3",
            "scikit-learn"
        ]
        
        for dep in dependencies:
            cmd.extend(["--with", dep])
        
        subprocess.check_call(cmd)
        print("✅ 成功安装到Claude Desktop")
        print("🔄 请重启Claude Desktop以加载MCP服务器")
        return True
        
    except Exception as e:
        print(f"❌ Claude Desktop安装失败: {e}")
        print("💡 您可以手动安装:")
        print(f"   fastmcp install claude-desktop database_analysis_server_simple.py --name '数据库分析助手' --env-file .env")
        return False

def main():
    """主启动流程"""
    print("🚀 数据库分析MCP服务器快速启动")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    
    success_count = 0
    total_steps = 5
    
    # 步骤1: 检查MySQL连接
    if check_mysql_connection():
        success_count += 1
    else:
        # 如果连接失败，尝试创建数据库
        if create_database():
            success_count += 1
    
    # 步骤2: 创建数据库（如果需要）
    if success_count > 0:
        success_count += 1  # 数据库步骤已完成
    
    # 步骤3: 安装依赖
    if install_dependencies():
        success_count += 1
    
    # 步骤4: 测试服务器
    if test_server():
        success_count += 1
    
    # 步骤5: 安装到Claude Desktop
    choice = input("\n是否安装到Claude Desktop? (y/n): ").lower().strip()
    if choice in ['y', 'yes', '是']:
        if install_to_claude():
            success_count += 1
    else:
        print("⏭️ 跳过Claude Desktop安装")
        success_count += 1
    
    # 总结
    print(f"\n{'='*50}")
    print(f"🎯 完成度: {success_count}/{total_steps}")
    
    if success_count == total_steps:
        print("🎉 所有步骤完成！系统已就绪。")
        print("\n📋 下一步:")
        print("1. 重启Claude Desktop")
        print("2. 在Claude Desktop中测试MCP工具")
        print("3. 尝试语音命令: '分析过去24小时的温度数据'")
        
        # 提供直接启动选项
        start_choice = input("\n是否直接启动服务器进行测试? (y/n): ").lower().strip()
        if start_choice in ['y', 'yes', '是']:
            print("🚀 启动服务器...")
            try:
                subprocess.run([sys.executable, "database_analysis_server_simple.py"])
            except KeyboardInterrupt:
                print("\n👋 服务器已停止")
    else:
        print("⚠️ 部分步骤失败，请检查错误信息并手动完成。")
        print("\n🔧 手动步骤:")
        print("1. 检查MySQL连接配置")
        print("2. 运行: pip install -r requirements.txt")
        print("3. 运行: python test_server.py")
        print("4. 运行: fastmcp install claude-desktop database_analysis_server_simple.py")

if __name__ == "__main__":
    main()
