#!/usr/bin/env python3
"""
最简化MCP测试服务器
"""

import os
from dotenv import load_dotenv
from fastmcp import FastMCP

# 加载环境变量
load_dotenv()

# 创建MCP服务器
mcp = FastMCP(
    name="测试助手",
    instructions="我是一个测试助手"
)

@mcp.tool
async def hello_world() -> str:
    """测试工具"""
    return "Hello from MCP server!"

@mcp.tool
async def get_env_info() -> dict:
    """获取环境信息"""
    return {
        "db_host": os.getenv('DB_HOST', 'NOT_SET'),
        "db_user": os.getenv('DB_USER', 'NOT_SET'),
        "db_name": os.getenv('DB_NAME', 'NOT_SET'),
        "password_length": len(os.getenv('DB_PASSWORD', ''))
    }

if __name__ == "__main__":
    print(">> 最简化MCP测试服务器启动...")
    print(">> 可用工具: hello_world, get_env_info")
    print(">> 服务器已就绪...")
    
    # 启动MCP服务器
    mcp.run()
