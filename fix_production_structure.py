#!/usr/bin/env python3
"""
修正production目录结构
"""

import os
import shutil
from pathlib import Path

def fix_production_structure():
    """修正production目录结构"""
    
    # 创建mcp_client目录
    mcp_client_dir = Path('production/mcp_client')
    mcp_client_dir.mkdir(exist_ok=True)
    
    # 需要移动到mcp_client目录的文件
    files_to_move = [
        'simple_http_server.py',
        'enterprise_ai_frontend.py', 
        'client.py',
        'config.py'
    ]
    
    print("🔧 修正production目录结构...")
    
    for filename in files_to_move:
        src = Path(f'production/{filename}')
        dst = Path(f'production/mcp_client/{filename}')
        
        if src.exists():
            try:
                shutil.move(str(src), str(dst))
                print(f"📁 移动: {src} -> {dst}")
            except Exception as e:
                print(f"❌ 移动失败: {src} - {e}")
        else:
            print(f"⚠️ 文件不存在: {src}")
    
    # 复制必要的客户端文件
    client_files_to_copy = [
        'mcp_client/utils',
        'mcp_client/handlers', 
        'mcp_client/ui',
        'mcp_client/cache',
        'mcp_client/logs',
        'mcp_client/temp',
        'mcp_client/exports',
    ]
    
    print("\n📂 复制客户端支持文件...")
    
    for item in client_files_to_copy:
        src = Path(item)
        dst = Path(f'production/{item}')
        
        if src.exists():
            try:
                if src.is_dir():
                    if dst.exists():
                        shutil.rmtree(dst)
                    shutil.copytree(src, dst)
                    print(f"📁 复制目录: {src} -> {dst}")
                else:
                    shutil.copy2(src, dst)
                    print(f"📄 复制文件: {src} -> {dst}")
            except Exception as e:
                print(f"❌ 复制失败: {src} - {e}")
    
    # 复制requirements.txt到mcp_client目录
    req_src = Path('mcp_client/requirements.txt')
    req_dst = Path('production/mcp_client/requirements.txt')
    
    if req_src.exists():
        try:
            shutil.copy2(req_src, req_dst)
            print(f"📄 复制: {req_src} -> {req_dst}")
        except Exception as e:
            print(f"❌ 复制失败: {req_src} - {e}")

def create_production_requirements():
    """创建生产环境的requirements.txt"""
    
    requirements_content = """# 企业级数据分析系统 - 生产环境依赖

# FastMCP核心
fastmcp>=2.10.0

# 数据库连接
pymysql>=1.0.2
mysql-connector-python>=8.0.33

# 数据处理
pandas>=2.0.0
numpy>=1.24.0

# 可视化
plotly>=5.15.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Web框架
streamlit>=1.28.0
uvicorn>=0.23.0
fastapi>=0.100.0

# 机器学习
scikit-learn>=1.3.0

# 语音处理
pyttsx3>=2.90
SpeechRecognition>=3.10.0

# 其他工具
python-dotenv>=1.0.0
pydantic>=2.0.0
asyncio-mqtt>=0.13.0
"""
    
    with open('production/requirements.txt', 'w', encoding='utf-8') as f:
        f.write(requirements_content)
    
    print("✅ 创建生产环境requirements.txt")

if __name__ == "__main__":
    print("🔧 开始修正production目录结构...")
    
    fix_production_structure()
    create_production_requirements()
    
    print("\n✅ production目录结构修正完成!")
    print("\n📁 最终目录结构:")
    print("production/")
    print("├── enterprise_database_mcp_server.py  # 主服务器")
    print("├── mcp_client/")
    print("│   ├── simple_http_server.py         # HTTP服务器")
    print("│   ├── enterprise_ai_frontend.py     # 前端界面")
    print("│   ├── client.py                     # 客户端")
    print("│   ├── config.py                     # 配置")
    print("│   └── requirements.txt              # 客户端依赖")
    print("├── requirements.txt                  # 主要依赖")
    print("├── start_system.py                   # Python启动脚本")
    print("├── start_system.bat                  # Windows启动脚本")
    print("└── README.md                         # 说明文档")
