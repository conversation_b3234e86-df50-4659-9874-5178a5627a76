#!/usr/bin/env python3
"""
简单HTTP服务器启动脚本
基于FastMCP文档的标准方法
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

def start_http_server():
    """启动HTTP服务器"""
    print("🚀 启动MCP HTTP服务器")
    print("=" * 50)
    
    # 设置环境变量
    env_vars = {
        "DB_HOST": "localhost",
        "DB_PORT": "3306",
        "DB_USER": "root",
        "DB_PASSWORD": "123456",
        "DB_NAME": "sensor_data",
        "DB_CHARSET": "utf8mb4",
        "PYTHONIOENCODING": "utf-8"
    }
    
    print("🌍 设置环境变量:")
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  {key} = {value}")
    
    # 导入服务器模块
    print("\n📦 导入服务器模块...")
    try:
        # 动态导入服务器
        import importlib.util
        server_path = Path(__file__).parent.parent / "enterprise_database_mcp_server.py"
        
        if not server_path.exists():
            print(f"❌ 服务器文件不存在: {server_path}")
            return False
        
        spec = importlib.util.spec_from_file_location("server", server_path)
        server_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(server_module)
        
        # 获取MCP实例
        mcp = server_module.mcp
        print("✅ 服务器模块导入成功")
        
    except Exception as e:
        print(f"❌ 导入服务器模块失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    # 启动HTTP服务器
    print("\n🌐 启动HTTP服务器...")
    print("📍 地址: http://127.0.0.1:8000/mcp/")
    print("⏹️ 按 Ctrl+C 停止服务器")
    print("🔄 服务器启动后，请在新终端运行第2步")
    print("-" * 50)
    
    try:
        # 根据FastMCP文档，使用HTTP传输
        mcp.run(
            transport="http",
            host="127.0.0.1",
            port=8000,
            log_level="INFO"
        )
        
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
        return True
    except Exception as e:
        print(f"\n❌ 服务器启动失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    try:
        start_http_server()
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
