#!/usr/bin/env python3
"""最终功能测试"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

async def test_existing_tools():
    """测试现有工具是否正常工作"""
    try:
        # 创建客户端
        transport = StreamableHttpTransport("http://127.0.0.1:8000/mcp")
        client = Client(transport)
        
        print("🔗 连接到MCP服务器...")
        
        async with client:
            print("✅ 连接成功")
            
            # 测试1: 统计分析
            print("\n🧪 测试1: 统计分析")
            result = await client.call_tool("advanced_statistical_analysis", {
                "start_time": "2020-01-01 00:00:00",
                "end_time": "2030-01-01 00:00:00",
                "columns": ["temperature"],
                "operation": "summary"
            })
            
            if hasattr(result, 'data') and result.data:
                print("✅ 统计分析成功")
            else:
                print("❌ 统计分析失败")
            
            # 测试2: 异常检测
            print("\n🧪 测试2: 异常检测")
            result = await client.call_tool("intelligent_anomaly_detection", {
                "column": "temperature",
                "method": "hybrid",
                "time_window": "24h",
                "sensitivity": 2.0
            })
            
            if hasattr(result, 'data') and result.data:
                print("✅ 异常检测成功")
            else:
                print("❌ 异常检测失败")
            
            # 测试3: 趋势分析
            print("\n🧪 测试3: 趋势分析")
            result = await client.call_tool("advanced_trend_analysis", {
                "columns": ["temperature"],
                "time_range": "7d",
                "prediction_hours": 24,
                "method": "ensemble"
            })
            
            if hasattr(result, 'data') and result.data:
                print("✅ 趋势分析成功")
            else:
                print("❌ 趋势分析失败")
            
            # 测试4: 系统状态
            print("\n🧪 测试4: 系统状态")
            result = await client.call_tool("get_system_status")
            
            if hasattr(result, 'data') and result.data:
                print("✅ 系统状态获取成功")
            else:
                print("❌ 系统状态获取失败")
                
            print("\n🎉 所有核心功能测试完成！")
            print("现在可以在Streamlit前端中正常使用AI分析功能了！")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_existing_tools())
