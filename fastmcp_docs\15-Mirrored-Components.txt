# FastMCP 文档 - 第 15 部分
# 主要内容: Mirrored Components
# 包含段落: 97 个
# 总行数: 1028

================================================================================

## Mirrored Components
类型: docs, 行数: 11

## Mirrored Components

<VersionBadge version="2.10.5" />

When you access tools, resources, or prompts from a proxy server, they are "mirrored" from the remote server. Mirrored components cannot be modified directly since they reflect the state of the remote server. For example, you can not simply "disable" a mirrored component.

However, you can create a copy of a mirrored component and store it as a new locally-defined component. Local components always take precedence over mirored ones because the proxy server will check its own registry before it attempts to engage the remote server.

Therefore, to enable or disable a proxy tool, resource, or prompt, you should first create a local copy and add it to your own server. Here's an example of how to do that for a tool:

```python

------------------------------------------------------------

## Create your own server
类型: docs, 行数: 3

# Create your own server
my_server = FastMCP("MyServer")


------------------------------------------------------------

## Get a proxy server
类型: docs, 行数: 3

# Get a proxy server
proxy = FastMCP.as_proxy("backend_server.py")


------------------------------------------------------------

## Add mirrored components to your server
类型: docs, 行数: 4

# Add mirrored components to your server
async with proxy:
    mirrored_tool = await proxy.get_tool("useful_tool")
    

------------------------------------------------------------

## Create a local copy that you can modify
类型: docs, 行数: 3

    # Create a local copy that you can modify
    local_tool = mirrored_tool.copy()
    

------------------------------------------------------------

## Add the local copy to your server
类型: docs, 行数: 3

    # Add the local copy to your server
    my_server.add_tool(local_tool)
    

------------------------------------------------------------

## Now you can disable YOUR copy
类型: docs, 行数: 4

    # Now you can disable YOUR copy
    local_tool.disable()
```


------------------------------------------------------------

## `FastMCPProxy` Class
类型: api, 行数: 4

## `FastMCPProxy` Class

Internally, `FastMCP.as_proxy()` uses the `FastMCPProxy` class. You generally don't need to interact with this class directly, but it's available if needed for advanced scenarios.


------------------------------------------------------------

## Direct Usage
类型: docs, 行数: 5

### Direct Usage

```python
from fastmcp.server.proxy import FastMCPProxy, ProxyClient


------------------------------------------------------------

## Provide a client factory for explicit session control
类型: docs, 行数: 7

# Provide a client factory for explicit session control
def create_client():
    return ProxyClient("backend_server.py")

proxy = FastMCPProxy(client_factory=create_client)
```


------------------------------------------------------------

## Parameters
类型: docs, 行数: 5

### Parameters

* **`client`**: **\[DEPRECATED]** A `Client` instance. Use `client_factory` instead for explicit session management.
* **`client_factory`**: A callable that returns a `Client` instance when called. This gives you full control over session creation and reuse strategies.


------------------------------------------------------------

## Explicit Session Management
类型: docs, 行数: 5

### Explicit Session Management

`FastMCPProxy` requires explicit session management - no automatic detection is performed. You must choose your session strategy:

```python

------------------------------------------------------------

## Share session across all requests (be careful with concurrency)
类型: docs, 行数: 7

# Share session across all requests (be careful with concurrency)
shared_client = ProxyClient("backend_server.py")
def shared_session_factory():
    return shared_client

proxy = FastMCPProxy(client_factory=shared_session_factory)


------------------------------------------------------------

## Create fresh sessions per request (recommended)
类型: docs, 行数: 10

# Create fresh sessions per request (recommended)
def fresh_session_factory():
    return ProxyClient("backend_server.py")

proxy = FastMCPProxy(client_factory=fresh_session_factory)
```

For automatic session strategy selection, use the convenience method `FastMCP.as_proxy()` instead.

```python

------------------------------------------------------------

## Custom factory with specific configuration
类型: setup, 行数: 3

# Custom factory with specific configuration
def custom_client_factory():
    client = ProxyClient("backend_server.py")

------------------------------------------------------------

## Add any custom configuration here
类型: setup, 行数: 7

    # Add any custom configuration here
    return client

proxy = FastMCPProxy(client_factory=custom_client_factory)
```



------------------------------------------------------------

## Resources & Templates
类型: docs, 行数: 18

# Resources & Templates
Source: https://gofastmcp.com/servers/resources

Expose data sources and dynamic content generators to your MCP client.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

Resources represent data or files that an MCP client can read, and resource templates extend this concept by allowing clients to request dynamically generated resources based on parameters passed in the URI.

FastMCP simplifies defining both static and dynamic resources, primarily using the `@mcp.resource` decorator.


------------------------------------------------------------

## What Are Resources?
类型: docs, 行数: 10

## What Are Resources?

Resources provide read-only access to data for the LLM or client application. When a client requests a resource URI:

1. FastMCP finds the corresponding resource definition.
2. If it's dynamic (defined by a function), the function is executed.
3. The content (text, JSON, binary data) is returned to the client.

This allows LLMs to access files, database content, configuration, or dynamically generated information relevant to the conversation.


------------------------------------------------------------

## Resources
类型: docs, 行数: 2

## Resources


------------------------------------------------------------

## The `@resource` Decorator
类型: docs, 行数: 10

### The `@resource` Decorator

The most common way to define a resource is by decorating a Python function. The decorator requires the resource's unique URI.

```python
import json
from fastmcp import FastMCP

mcp = FastMCP(name="DataServer")


------------------------------------------------------------

## Basic dynamic resource returning a string
类型: docs, 行数: 6

# Basic dynamic resource returning a string
@mcp.resource("resource://greeting")
def get_greeting() -> str:
    """Provides a simple greeting message."""
    return "Hello from FastMCP Resources!"


------------------------------------------------------------

## Resource returning JSON data (dict is auto-serialized)
类型: docs, 行数: 19

# Resource returning JSON data (dict is auto-serialized)
@mcp.resource("data://config")
def get_config() -> dict:
    """Provides application configuration as JSON."""
    return {
        "theme": "dark",
        "version": "1.2.0",
        "features": ["tools", "resources"],
    }
```

**Key Concepts:**

* **URI:** The first argument to `@resource` is the unique URI (e.g., `"resource://greeting"`) clients use to request this data.
* **Lazy Loading:** The decorated function (`get_greeting`, `get_config`) is only executed when a client specifically requests that resource URI via `resources/read`.
* **Inferred Metadata:** By default:
  * Resource Name: Taken from the function name (`get_greeting`).
  * Resource Description: Taken from the function's docstring.


------------------------------------------------------------

## Decorator Arguments
类型: docs, 行数: 9

#### Decorator Arguments

You can customize the resource's properties using arguments in the `@mcp.resource` decorator:

```python
from fastmcp import FastMCP

mcp = FastMCP(name="DataServer")


------------------------------------------------------------

## Example specifying metadata
类型: tutorial, 行数: 39

# Example specifying metadata
@mcp.resource(
    uri="data://app-status",      # Explicit URI (required)
    name="ApplicationStatus",     # Custom name
    description="Provides the current status of the application.", # Custom description
    mime_type="application/json", # Explicit MIME type
    tags={"monitoring", "status"} # Categorization tags
)
def get_application_status() -> dict:
    """Internal function description (ignored if description is provided above)."""
    return {"status": "ok", "uptime": 12345, "version": mcp.settings.version} # Example usage
```

<Card icon="code" title="@resource Decorator Arguments">
  <ParamField body="uri" type="str" required>
    The unique identifier for the resource
  </ParamField>

  <ParamField body="name" type="str | None">
    A human-readable name. If not provided, defaults to function name
  </ParamField>

  <ParamField body="description" type="str | None">
    Explanation of the resource. If not provided, defaults to docstring
  </ParamField>

  <ParamField body="mime_type" type="str | None">
    Specifies the content type. FastMCP often infers a default like `text/plain` or `application/json`, but explicit is better for non-text types
  </ParamField>

  <ParamField body="tags" type="set[str] | None">
    A set of strings for categorization, potentially used by clients for filtering
  </ParamField>

  <ParamField body="enabled" type="bool" default="True">
    A boolean to enable or disable the resource. See [Disabling Resources](#disabling-resources) for more information
  </ParamField>
</Card>


------------------------------------------------------------

## Return Values
类型: docs, 行数: 9

### Return Values

FastMCP automatically converts your function's return value into the appropriate MCP resource content:

* **`str`**: Sent as `TextResourceContents` (with `mime_type="text/plain"` by default).
* **`dict`, `list`, `pydantic.BaseModel`**: Automatically serialized to a JSON string and sent as `TextResourceContents` (with `mime_type="application/json"` by default).
* **`bytes`**: Base64 encoded and sent as `BlobResourceContents`. You should specify an appropriate `mime_type` (e.g., `"image/png"`, `"application/octet-stream"`).
* **`None`**: Results in an empty resource content list being returned.


------------------------------------------------------------

## Disabling Resources
类型: docs, 行数: 21

### Disabling Resources

<VersionBadge version="2.8.0" />

You can control the visibility and availability of resources and templates by enabling or disabling them. Disabled resources will not appear in the list of available resources or templates, and attempting to read a disabled resource will result in an "Unknown resource" error.

By default, all resources are enabled. You can disable a resource upon creation using the `enabled` parameter in the decorator:

```python
@mcp.resource("data://secret", enabled=False)
def get_secret_data():
    """This resource is currently disabled."""
    return "Secret data"
```

You can also toggle a resource's state programmatically after it has been created:

```python
@mcp.resource("data://config")
def get_config(): return {"version": 1}


------------------------------------------------------------

## Disable and re-enable the resource
类型: docs, 行数: 5

# Disable and re-enable the resource
get_config.disable()
get_config.enable()
```


------------------------------------------------------------

## Accessing MCP Context
类型: docs, 行数: 30

### Accessing MCP Context

<VersionBadge version="2.2.5" />

Resources and resource templates can access additional MCP information and features through the `Context` object. To access it, add a parameter to your resource function with a type annotation of `Context`:

```python {6, 14}
from fastmcp import FastMCP, Context

mcp = FastMCP(name="DataServer")

@mcp.resource("resource://system-status")
async def get_system_status(ctx: Context) -> dict:
    """Provides system status information."""
    return {
        "status": "operational",
        "request_id": ctx.request_id
    }

@mcp.resource("resource://{name}/details")
async def get_details(name: str, ctx: Context) -> dict:
    """Get details for a specific name."""
    return {
        "name": name,
        "accessed_at": ctx.request_id
    }
```

For full documentation on the Context object and all its capabilities, see the [Context documentation](/servers/context).


------------------------------------------------------------

## Async Resources
类型: docs, 行数: 21

### Async Resources

Use `async def` for resource functions that perform I/O operations (e.g., reading from a database or network) to avoid blocking the server.

```python
import aiofiles
from fastmcp import FastMCP

mcp = FastMCP(name="DataServer")

@mcp.resource("file:///app/data/important_log.txt", mime_type="text/plain")
async def read_important_log() -> str:
    """Reads content from a specific log file asynchronously."""
    try:
        async with aiofiles.open("/app/data/important_log.txt", mode="r") as f:
            content = await f.read()
        return content
    except FileNotFoundError:
        return "Log file not found."
```


------------------------------------------------------------

## Resource Classes
类型: api, 行数: 11

### Resource Classes

While `@mcp.resource` is ideal for dynamic content, you can directly register pre-defined resources (like static files or simple text) using `mcp.add_resource()` and concrete `Resource` subclasses.

```python
from pathlib import Path
from fastmcp import FastMCP
from fastmcp.resources import FileResource, TextResource, DirectoryResource

mcp = FastMCP(name="DataServer")


------------------------------------------------------------

## 1. Exposing a static file directly
类型: docs, 行数: 3

# 1. Exposing a static file directly
readme_path = Path("./README.md").resolve()
if readme_path.exists():

------------------------------------------------------------

## Use a file:// URI scheme
类型: docs, 行数: 11

    # Use a file:// URI scheme
    readme_resource = FileResource(
        uri=f"file://{readme_path.as_posix()}",
        path=readme_path, # Path to the actual file
        name="README File",
        description="The project's README.",
        mime_type="text/markdown",
        tags={"documentation"}
    )
    mcp.add_resource(readme_resource)


------------------------------------------------------------

## 2. Exposing simple, predefined text
类型: docs, 行数: 9

# 2. Exposing simple, predefined text
notice_resource = TextResource(
    uri="resource://notice",
    name="Important Notice",
    text="System maintenance scheduled for Sunday.",
    tags={"notification"}
)
mcp.add_resource(notice_resource)


------------------------------------------------------------

## 3. Using a custom key different from the URI
类型: docs, 行数: 8

# 3. Using a custom key different from the URI
special_resource = TextResource(
    uri="resource://common-notice",
    name="Special Notice",
    text="This is a special notice with a custom storage key.",
)
mcp.add_resource(special_resource, key="resource://custom-key")


------------------------------------------------------------

## 4. Exposing a directory listing
类型: docs, 行数: 24

# 4. Exposing a directory listing
data_dir_path = Path("./app_data").resolve()
if data_dir_path.is_dir():
    data_listing_resource = DirectoryResource(
        uri="resource://data-files",
        path=data_dir_path, # Path to the directory
        name="Data Directory Listing",
        description="Lists files available in the data directory.",
        recursive=False # Set to True to list subdirectories
    )
    mcp.add_resource(data_listing_resource) # Returns JSON list of files
```

**Common Resource Classes:**

* `TextResource`: For simple string content.
* `BinaryResource`: For raw `bytes` content.
* `FileResource`: Reads content from a local file path. Handles text/binary modes and lazy reading.
* `HttpResource`: Fetches content from an HTTP(S) URL (requires `httpx`).
* `DirectoryResource`: Lists files in a local directory (returns JSON).
* (`FunctionResource`: Internal class used by `@mcp.resource`).

Use these when the content is static or sourced directly from a file/URL, bypassing the need for a dedicated Python function.


------------------------------------------------------------

## Custom Resource Keys
类型: docs, 行数: 7

#### Custom Resource Keys

<VersionBadge version="2.2.0" />

When adding resources directly with `mcp.add_resource()`, you can optionally provide a custom storage key:

```python

------------------------------------------------------------

## Creating a resource with standard URI as the key
类型: docs, 行数: 4

# Creating a resource with standard URI as the key
resource = TextResource(uri="resource://data")
mcp.add_resource(resource)  # Will be stored and accessed using "resource://data"


------------------------------------------------------------

## Creating a resource with a custom key
类型: docs, 行数: 7

# Creating a resource with a custom key
special_resource = TextResource(uri="resource://special-data")
mcp.add_resource(special_resource, key="internal://data-v2")  # Will be stored and accessed using "internal://data-v2"
```

Note that this parameter is only available when using `add_resource()` directly and not through the `@resource` decorator, as URIs are provided explicitly when using the decorator.


------------------------------------------------------------

## Notifications
类型: docs, 行数: 11

### Notifications

<VersionBadge version="2.9.1" />

FastMCP automatically sends `notifications/resources/list_changed` notifications to connected clients when resources or templates are added, enabled, or disabled. This allows clients to stay up-to-date with the current resource set without manually polling for changes.

```python
@mcp.resource("data://example")
def example_resource() -> str:
    return "Hello!"


------------------------------------------------------------

## These operations trigger notifications:
类型: docs, 行数: 10

# These operations trigger notifications:
mcp.add_resource(example_resource)  # Sends resources/list_changed notification
example_resource.disable()          # Sends resources/list_changed notification  
example_resource.enable()           # Sends resources/list_changed notification
```

Notifications are only sent when these operations occur within an active MCP request context (e.g., when called from within a tool or other MCP operation). Operations performed during server initialization do not trigger notifications.

Clients can handle these notifications using a [message handler](/clients/messages) to automatically refresh their resource lists or update their interfaces.


------------------------------------------------------------

## Resource Templates
类型: docs, 行数: 19

## Resource Templates

Resource Templates allow clients to request resources whose content depends on parameters embedded in the URI. Define a template using the **same `@mcp.resource` decorator**, but include `{parameter_name}` placeholders in the URI string and add corresponding arguments to your function signature.

Resource templates share most configuration options with regular resources (name, description, mime\_type, tags), but add the ability to define URI parameters that map to function parameters.

Resource templates generate a new resource for each unique set of parameters, which means that resources can be dynamically created on-demand. For example, if the resource template `"user://profile/{name}"` is registered, MCP clients could request `"user://profile/ford"` or `"user://profile/marvin"` to retrieve either of those two user profiles as resources, without having to register each resource individually.

<Tip>
  Functions with `*args` are not supported as resource templates. However, unlike tools and prompts, resource templates do support `**kwargs` because the URI template defines specific parameter names that will be collected and passed as keyword arguments.
</Tip>

Here is a complete example that shows how to define two resource templates:

```python
from fastmcp import FastMCP

mcp = FastMCP(name="DataServer")


------------------------------------------------------------

## Template URI includes {city} placeholder
类型: docs, 行数: 4

# Template URI includes {city} placeholder
@mcp.resource("weather://{city}/current")
def get_weather(city: str) -> dict:
    """Provides weather information for a specific city."""

------------------------------------------------------------

## In a real implementation, this would call a weather API
类型: api, 行数: 1

    # In a real implementation, this would call a weather API

------------------------------------------------------------

## Here we're using simplified logic for example purposes
类型: tutorial, 行数: 8

    # Here we're using simplified logic for example purposes
    return {
        "city": city.capitalize(),
        "temperature": 22,
        "condition": "Sunny",
        "unit": "celsius"
    }


------------------------------------------------------------

## Template with multiple parameters
类型: docs, 行数: 4

# Template with multiple parameters
@mcp.resource("repos://{owner}/{repo}/info")
def get_repo_info(owner: str, repo: str) -> dict:
    """Retrieves information about a GitHub repository."""

------------------------------------------------------------

## In a real implementation, this would call the GitHub API
类型: api, 行数: 17

    # In a real implementation, this would call the GitHub API
    return {
        "owner": owner,
        "name": repo,
        "full_name": f"{owner}/{repo}",
        "stars": 120,
        "forks": 48
    }
```

With these two templates defined, clients can request a variety of resources:

* `weather://london/current` → Returns weather for London
* `weather://paris/current` → Returns weather for Paris
* `repos://jlowin/fastmcp/info` → Returns info about the jlowin/fastmcp repository
* `repos://prefecthq/prefect/info` → Returns info about the prefecthq/prefect repository


------------------------------------------------------------

## Wildcard Parameters
类型: docs, 行数: 16

### Wildcard Parameters

<VersionBadge version="2.2.4" />

<Tip>
  Please note: FastMCP's support for wildcard parameters is an **extension** of the Model Context Protocol standard, which otherwise follows RFC 6570. Since all template processing happens in the FastMCP server, this should not cause any compatibility issues with other MCP implementations.
</Tip>

Resource templates support wildcard parameters that can match multiple path segments. While standard parameters (`{param}`) only match a single path segment and don't cross "/" boundaries, wildcard parameters (`{param*}`) can capture multiple segments including slashes. Wildcards capture all subsequent path segments *up until* the defined part of the URI template (whether literal or another parameter). This allows you to have multiple wildcard parameters in a single URI template.

```python {15, 23}
from fastmcp import FastMCP

mcp = FastMCP(name="DataServer")



------------------------------------------------------------

## Standard parameter only matches one segment
类型: docs, 行数: 4

# Standard parameter only matches one segment
@mcp.resource("files://{filename}")
def get_file(filename: str) -> str:
    """Retrieves a file by name."""

------------------------------------------------------------

## Will only match files://<single-segment>
类型: docs, 行数: 4

    # Will only match files://<single-segment>
    return f"File content for: {filename}"



------------------------------------------------------------

## Wildcard parameter can match multiple segments
类型: docs, 行数: 4

# Wildcard parameter can match multiple segments
@mcp.resource("path://{filepath*}")
def get_path_content(filepath: str) -> str:
    """Retrieves content at a specific path."""

------------------------------------------------------------

## Can match path://docs/server/resources.mdx
类型: docs, 行数: 4

    # Can match path://docs/server/resources.mdx
    return f"Content at path: {filepath}"



------------------------------------------------------------

## Mixing standard and wildcard parameters
类型: docs, 行数: 5

# Mixing standard and wildcard parameters
@mcp.resource("repo://{owner}/{path*}/template.py")
def get_template_file(owner: str, path: str) -> dict:
    """Retrieves a file from a specific repository and path, but 
    only if the resource ends with `template.py`"""

------------------------------------------------------------

## Can match repo://jlowin/fastmcp/src/resources/template.py
类型: docs, 行数: 16

    # Can match repo://jlowin/fastmcp/src/resources/template.py
    return {
        "owner": owner,
        "path": path + "/template.py",
        "content": f"File at {path}/template.py in {owner}'s repository"
    }
```

Wildcard parameters are useful when:

* Working with file paths or hierarchical data
* Creating APIs that need to capture variable-length path segments
* Building URL-like patterns similar to REST APIs

Note that like regular parameters, each wildcard parameter must still be a named parameter in your function signature, and all required function parameters must appear in the URI template.


------------------------------------------------------------

## Default Values
类型: docs, 行数: 24

### Default Values

<VersionBadge version="2.2.0" />

When creating resource templates, FastMCP enforces two rules for the relationship between URI template parameters and function parameters:

1. **Required Function Parameters:** All function parameters without default values (required parameters) must appear in the URI template.
2. **URI Parameters:** All URI template parameters must exist as function parameters.

However, function parameters with default values don't need to be included in the URI template. When a client requests a resource, FastMCP will:

* Extract parameter values from the URI for parameters included in the template
* Use default values for any function parameters not in the URI template

This allows for flexible API designs. For example, a simple search template with optional parameters:

```python
from fastmcp import FastMCP

mcp = FastMCP(name="DataServer")

@mcp.resource("search://{query}")
def search_resources(query: str, max_results: int = 10, include_archived: bool = False) -> dict:
    """Search for resources matching the query string."""

------------------------------------------------------------

## Only 'query' is required in the URI, the other parameters use their defaults
类型: docs, 行数: 19

    # Only 'query' is required in the URI, the other parameters use their defaults
    results = perform_search(query, limit=max_results, archived=include_archived)
    return {
        "query": query,
        "max_results": max_results,
        "include_archived": include_archived,
        "results": results
    }
```

With this template, clients can request `search://python` and the function will be called with `query="python", max_results=10, include_archived=False`. MCP Developers can still call the underlying `search_resources` function directly with more specific parameters.

An even more powerful pattern is registering a single function with multiple URI templates, allowing different ways to access the same data:

```python
from fastmcp import FastMCP

mcp = FastMCP(name="DataServer")


------------------------------------------------------------

## Define a user lookup function that can be accessed by different identifiers
类型: api, 行数: 27

# Define a user lookup function that can be accessed by different identifiers
@mcp.resource("users://email/{email}")
@mcp.resource("users://name/{name}")
def lookup_user(name: str | None = None, email: str | None = None) -> dict:
    """Look up a user by either name or email."""
    if email:
        return find_user_by_email(email) # pseudocode
    elif name:
        return find_user_by_name(name) # pseudocode
    else:
        return {"error": "No lookup parameters provided"}
```

Now an LLM or client can retrieve user information in two different ways:

* `users://email/<EMAIL>` → Looks up user by email (with name=None)
* `users://name/Bob` → Looks up user by name (with email=None)

In this stacked decorator pattern:

* The `name` parameter is only provided when using the `users://name/{name}` template
* The `email` parameter is only provided when using the `users://email/{email}` template
* Each parameter defaults to `None` when not included in the URI
* The function logic handles whichever parameter is provided

Templates provide a powerful way to expose parameterized data access points following REST-like principles.


------------------------------------------------------------

## Error Handling
类型: docs, 行数: 27

## Error Handling

<VersionBadge version="2.4.1" />

If your resource function encounters an error, you can raise a standard Python exception (`ValueError`, `TypeError`, `FileNotFoundError`, custom exceptions, etc.) or a FastMCP `ResourceError`.

By default, all exceptions (including their details) are logged and converted into an MCP error response to be sent back to the client LLM. This helps the LLM understand failures and react appropriately.

If you want to mask internal error details for security reasons, you can:

1. Use the `mask_error_details=True` parameter when creating your `FastMCP` instance:

```python
mcp = FastMCP(name="SecureServer", mask_error_details=True)
```

2. Or use `ResourceError` to explicitly control what error information is sent to clients:

```python
from fastmcp import FastMCP
from fastmcp.exceptions import ResourceError

mcp = FastMCP(name="DataServer")

@mcp.resource("resource://safe-error")
def fail_with_details() -> str:
    """This resource provides detailed error information."""

------------------------------------------------------------

## ResourceError contents are always sent back to clients,
类型: docs, 行数: 1

    # ResourceError contents are always sent back to clients,

------------------------------------------------------------

## regardless of mask_error_details setting
类型: docs, 行数: 6

    # regardless of mask_error_details setting
    raise ResourceError("Unable to retrieve data: file not found")

@mcp.resource("resource://masked-error")
def fail_with_masked_details() -> str:
    """This resource masks internal error details when mask_error_details=True."""

------------------------------------------------------------

## This message would be masked if mask_error_details=True
类型: docs, 行数: 15

    # This message would be masked if mask_error_details=True
    raise ValueError("Sensitive internal file path: /etc/secrets.conf")

@mcp.resource("data://{id}")
def get_data_by_id(id: str) -> dict:
    """Template resources also support the same error handling pattern."""
    if id == "secure":
        raise ValueError("Cannot access secure data")
    elif id == "missing":
        raise ResourceError("Data ID 'missing' not found in database")
    return {"id": id, "value": "data"}
```

When `mask_error_details=True`, only error messages from `ResourceError` will include details, other exceptions will be converted to a generic message.


------------------------------------------------------------

## Server Behavior
类型: docs, 行数: 2

## Server Behavior


------------------------------------------------------------

## Duplicate Resources
类型: docs, 行数: 17

### Duplicate Resources

<VersionBadge version="2.1.0" />

You can configure how the FastMCP server handles attempts to register multiple resources or templates with the same URI. Use the `on_duplicate_resources` setting during `FastMCP` initialization.

```python
from fastmcp import FastMCP

mcp = FastMCP(
    name="ResourceServer",
    on_duplicate_resources="error" # Raise error on duplicates
)

@mcp.resource("data://config")
def get_config_v1(): return {"version": 1}


------------------------------------------------------------

## This registration attempt will raise a ValueError because
类型: docs, 行数: 1

# This registration attempt will raise a ValueError because

------------------------------------------------------------

## "data://config" is already registered and the behavior is "error".
类型: setup, 行数: 1

# "data://config" is already registered and the behavior is "error".

------------------------------------------------------------

## @mcp.resource("data://config")
类型: setup, 行数: 1

# @mcp.resource("data://config")

------------------------------------------------------------

## def get_config_v2(): return {"version": 2}
类型: setup, 行数: 11

# def get_config_v2(): return {"version": 2}
```

The duplicate behavior options are:

* `"warn"` (default): Logs a warning, and the new resource/template replaces the old one.
* `"error"`: Raises a `ValueError`, preventing the duplicate registration.
* `"replace"`: Silently replaces the existing resource/template with the new one.
* `"ignore"`: Keeps the original resource/template and ignores the new registration attempt.



------------------------------------------------------------

## LLM Sampling
类型: docs, 行数: 18

# LLM Sampling
Source: https://gofastmcp.com/servers/sampling

Request the client's LLM to generate text based on provided messages through the MCP context.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

<VersionBadge version="2.0.0" />

LLM sampling allows MCP tools to request the client's LLM to generate text based on provided messages. This is useful when tools need to leverage the LLM's capabilities to process data, generate responses, or perform text-based analysis.


------------------------------------------------------------

## Why Use LLM Sampling?
类型: docs, 行数: 9

## Why Use LLM Sampling?

LLM sampling enables tools to:

* **Leverage AI capabilities**: Use the client's LLM for text generation and analysis
* **Offload complex reasoning**: Let the LLM handle tasks requiring natural language understanding
* **Generate dynamic content**: Create responses, summaries, or transformations based on data
* **Maintain context**: Use the same LLM instance that the user is already interacting with


------------------------------------------------------------

## Basic Usage
类型: docs, 行数: 17

### Basic Usage

Use `ctx.sample()` to request text generation from the client's LLM:

```python {14}
from fastmcp import FastMCP, Context

mcp = FastMCP("SamplingDemo")

@mcp.tool
async def analyze_sentiment(text: str, ctx: Context) -> dict:
    """Analyze the sentiment of text using the client's LLM."""
    prompt = f"""Analyze the sentiment of the following text as positive, negative, or neutral. 
    Just output a single word - 'positive', 'negative', or 'neutral'.
    
    Text to analyze: {text}"""
    

------------------------------------------------------------

## Request LLM analysis
类型: docs, 行数: 3

    # Request LLM analysis
    response = await ctx.sample(prompt)
    

------------------------------------------------------------

## Process the LLM's response
类型: docs, 行数: 3

    # Process the LLM's response
    sentiment = response.text.strip().lower()
    

------------------------------------------------------------

## Map to standard sentiment values
类型: docs, 行数: 11

    # Map to standard sentiment values
    if "positive" in sentiment:
        sentiment = "positive"
    elif "negative" in sentiment:
        sentiment = "negative"
    else:
        sentiment = "neutral"
    
    return {"text": text, "sentiment": sentiment}
```


------------------------------------------------------------

## Method Signature
类型: api, 行数: 36

## Method Signature

<Card icon="code" title="Context Sampling Method">
  <ResponseField name="ctx.sample" type="async method">
    Request text generation from the client's LLM

    <Expandable title="Parameters">
      <ResponseField name="messages" type="str | list[str | SamplingMessage]">
        A string or list of strings/message objects to send to the LLM
      </ResponseField>

      <ResponseField name="system_prompt" type="str | None" default="None">
        Optional system prompt to guide the LLM's behavior
      </ResponseField>

      <ResponseField name="temperature" type="float | None" default="None">
        Optional sampling temperature (controls randomness, typically 0.0-1.0)
      </ResponseField>

      <ResponseField name="max_tokens" type="int | None" default="512">
        Optional maximum number of tokens to generate
      </ResponseField>

      <ResponseField name="model_preferences" type="ModelPreferences | str | list[str] | None" default="None">
        Optional model selection preferences (e.g., model hint string, list of hints, or ModelPreferences object)
      </ResponseField>
    </Expandable>

    <Expandable title="Response">
      <ResponseField name="response" type="TextContent | ImageContent">
        The LLM's response content (typically TextContent with a .text attribute)
      </ResponseField>
    </Expandable>
  </ResponseField>
</Card>


------------------------------------------------------------

## Simple Text Generation
类型: docs, 行数: 2

## Simple Text Generation


------------------------------------------------------------

## Basic Prompting
类型: docs, 行数: 14

### Basic Prompting

Generate text with simple string prompts:

```python {6}
@mcp.tool
async def generate_summary(content: str, ctx: Context) -> str:
    """Generate a summary of the provided content."""
    prompt = f"Please provide a concise summary of the following content:\n\n{content}"
    
    response = await ctx.sample(prompt)
    return response.text
```


------------------------------------------------------------

## System Prompt
类型: docs, 行数: 19

### System Prompt

Use system prompts to guide the LLM's behavior:

````python {4-9}
@mcp.tool
async def generate_code_example(concept: str, ctx: Context) -> str:
    """Generate a Python code example for a given concept."""
    response = await ctx.sample(
        messages=f"Write a simple Python code example demonstrating '{concept}'.",
        system_prompt="You are an expert Python programmer. Provide concise, working code examples without explanations.",
        temperature=0.7,
        max_tokens=300
    )
    
    code_example = response.text
    return f"```python\n{code_example}\n```"
````


------------------------------------------------------------

## Model Preferences
类型: api, 行数: 31

### Model Preferences

Specify model preferences for different use cases:

```python {4-8, 17-22}
@mcp.tool
async def creative_writing(topic: str, ctx: Context) -> str:
    """Generate creative content using a specific model."""
    response = await ctx.sample(
        messages=f"Write a creative short story about {topic}",
        model_preferences="claude-3-sonnet",  # Prefer a specific model
        include_context="thisServer",  # Use the server's context
        temperature=0.9,  # High creativity
        max_tokens=1000
    )
    
    return response.text

@mcp.tool
async def technical_analysis(data: str, ctx: Context) -> str:
    """Perform technical analysis with a reasoning-focused model."""
    response = await ctx.sample(
        messages=f"Analyze this technical data and provide insights: {data}",
        model_preferences=["claude-3-opus", "gpt-4"],  # Prefer reasoning models
        temperature=0.2,  # Low randomness for consistency
        max_tokens=800
    )
    
    return response.text
```


------------------------------------------------------------

## Complex Message Structures
类型: docs, 行数: 25

### Complex Message Structures

Use structured messages for more complex interactions:

```python {1, 6-10}
from fastmcp.client.sampling import SamplingMessage

@mcp.tool
async def multi_turn_analysis(user_query: str, context_data: str, ctx: Context) -> str:
    """Perform analysis using multi-turn conversation structure."""
    messages = [
        SamplingMessage(role="user", content=f"I have this data: {context_data}"),
        SamplingMessage(role="assistant", content="I can see your data. What would you like me to analyze?"),
        SamplingMessage(role="user", content=user_query)
    ]
    
    response = await ctx.sample(
        messages=messages,
        system_prompt="You are a data analyst. Provide detailed insights based on the conversation context.",
        temperature=0.3
    )
    
    return response.text
```


------------------------------------------------------------

## Client Requirements
类型: docs, 行数: 9

## Client Requirements

LLM sampling requires client support:

* Clients must implement sampling handlers to process requests
* If the client doesn't support sampling, calls to `ctx.sample()` will fail
* See [Client Sampling](/clients/sampling) for details on implementing client-side sampling handlers



------------------------------------------------------------

## The FastMCP Server
类型: docs, 行数: 16

# The FastMCP Server
Source: https://gofastmcp.com/servers/server

The core FastMCP server class for building MCP applications with tools, resources, and prompts.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

The central piece of a FastMCP application is the `FastMCP` server class. This class acts as the main container for your application's tools, resources, and prompts, and manages communication with MCP clients.


------------------------------------------------------------

## Creating a Server
类型: docs, 行数: 7

## Creating a Server

Instantiating a server is straightforward. You typically provide a name for your server, which helps identify it in client applications or logs.

```python
from fastmcp import FastMCP


------------------------------------------------------------

## Create a basic server instance
类型: docs, 行数: 3

# Create a basic server instance
mcp = FastMCP(name="MyAssistantServer")


------------------------------------------------------------

## You can also add instructions for how to interact with the server
类型: docs, 行数: 38

# You can also add instructions for how to interact with the server
mcp_with_instructions = FastMCP(
    name="HelpfulAssistant",
    instructions="""
        This server provides data analysis tools.
        Call get_average() to analyze numerical data.
    """,
)
```

The `FastMCP` constructor accepts several arguments:

<Card icon="code" title="FastMCP Constructor Parameters">
  <ParamField body="name" type="str" default="FastMCP">
    A human-readable name for your server
  </ParamField>

  <ParamField body="instructions" type="str | None">
    Description of how to interact with this server. These instructions help clients understand the server's purpose and available functionality
  </ParamField>

  <ParamField body="lifespan" type="AsyncContextManager | None">
    An async context manager function for server startup and shutdown logic
  </ParamField>

  <ParamField body="tags" type="set[str] | None">
    A set of strings to tag the server itself
  </ParamField>

  <ParamField body="tools" type="list[Tool | Callable] | None">
    A list of tools (or functions to convert to tools) to add to the server. In some cases, providing tools programmatically may be more convenient than using the `@mcp.tool` decorator
  </ParamField>

  <ParamField body="**settings" type="Any">
    Keyword arguments corresponding to additional `ServerSettings` configuration
  </ParamField>
</Card>


------------------------------------------------------------

## Components
类型: docs, 行数: 4

## Components

FastMCP servers expose several types of components to the client:


------------------------------------------------------------

## Tools
类型: docs, 行数: 13

### Tools

Tools are functions that the client can call to perform actions or access external systems.

```python
@mcp.tool
def multiply(a: float, b: float) -> float:
    """Multiplies two numbers together."""
    return a * b
```

See [Tools](/servers/tools) for detailed documentation.


------------------------------------------------------------

## Resources
类型: docs, 行数: 13

### Resources

Resources expose data sources that the client can read.

```python
@mcp.resource("data://config")
def get_config() -> dict:
    """Provides the application configuration."""
    return {"theme": "dark", "version": "1.0"}
```

See [Resources & Templates](/servers/resources) for detailed documentation.


------------------------------------------------------------

## Resource Templates
类型: docs, 行数: 8

### Resource Templates

Resource templates are parameterized resources that allow the client to request specific data.

```python
@mcp.resource("users://{user_id}/profile")
def get_user_profile(user_id: int) -> dict:
    """Retrieves a user's profile by ID."""

------------------------------------------------------------

## The {user_id} in the URI is extracted and passed to this function
类型: api, 行数: 6

    # The {user_id} in the URI is extracted and passed to this function
    return {"id": user_id, "name": f"User {user_id}", "status": "active"}
```

See [Resources & Templates](/servers/resources) for detailed documentation.


------------------------------------------------------------

## Prompts
类型: docs, 行数: 14

### Prompts

Prompts are reusable message templates for guiding the LLM.

```python
@mcp.prompt
def analyze_data(data_points: list[float]) -> str:
    """Creates a prompt asking for analysis of numerical data."""
    formatted_data = ", ".join(str(point) for point in data_points)
    return f"Please analyze these data points: {formatted_data}"
```

See [Prompts](/servers/prompts) for detailed documentation.


------------------------------------------------------------

## Tag-Based Filtering
类型: docs, 行数: 31

## Tag-Based Filtering

<VersionBadge version="2.8.0" />

FastMCP supports tag-based filtering to selectively expose components based on configurable include/exclude tag sets. This is useful for creating different views of your server for different environments or users.

Components can be tagged when defined using the `tags` parameter:

```python
@mcp.tool(tags={"public", "utility"})
def public_tool() -> str:
    return "This tool is public"

@mcp.tool(tags={"internal", "admin"})
def admin_tool() -> str:
    return "This tool is for admins only"
```

The filtering logic works as follows:

* **Include tags**: If specified, only components with at least one matching tag are exposed
* **Exclude tags**: Components with any matching tag are filtered out
* **Precedence**: Exclude tags always take priority over include tags

<Tip>
  To ensure a component is never exposed, you can set `enabled=False` on the component itself. To learn more, see the component-specific documentation.
</Tip>

You configure tag-based filtering when creating your server:

```python

------------------------------------------------------------

## Only expose components tagged with "public"
类型: docs, 行数: 3

# Only expose components tagged with "public"
mcp = FastMCP(include_tags={"public"})


------------------------------------------------------------

## Hide components tagged as "internal" or "deprecated"
类型: docs, 行数: 3

# Hide components tagged as "internal" or "deprecated"  
mcp = FastMCP(exclude_tags={"internal", "deprecated"})


------------------------------------------------------------

## Combine both: show admin tools but hide deprecated ones
类型: docs, 行数: 6

# Combine both: show admin tools but hide deprecated ones
mcp = FastMCP(include_tags={"admin"}, exclude_tags={"deprecated"})
```

This filtering applies to all component types (tools, resources, resource templates, and prompts) and affects both listing and access.


------------------------------------------------------------

## Running the Server
类型: docs, 行数: 5

## Running the Server

FastMCP servers need a transport mechanism to communicate with clients. You typically start your server by calling the `mcp.run()` method on your `FastMCP` instance, often within an `if __name__ == "__main__":` block in your main server script. This pattern ensures compatibility with various MCP clients.

```python

------------------------------------------------------------

## my_server.py
类型: docs, 行数: 11

# my_server.py
from fastmcp import FastMCP

mcp = FastMCP(name="MyServer")

@mcp.tool
def greet(name: str) -> str:
    """Greet a user by name."""
    return f"Hello, {name}!"

if __name__ == "__main__":

------------------------------------------------------------

## This runs the server, defaulting to STDIO transport
类型: docs, 行数: 3

    # This runs the server, defaulting to STDIO transport
    mcp.run()
    

------------------------------------------------------------

## To use a different transport, e.g., Streamable HTTP:
类型: docs, 行数: 1

    # To use a different transport, e.g., Streamable HTTP:

------------------------------------------------------------

