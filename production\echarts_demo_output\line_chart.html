
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
        <style>
            body { margin: 0; padding: 10px; font-family: Arial, sans-serif; }
            #chart { width: 100%; height: 600px; }
        </style>
    </head>
    <body>
        <div id="chart"></div>
        <script>
            var chartDom = document.getElementById('chart');
            var myChart = echarts.init(chartDom);
            var option = {
  "title": {
    "text": "温湿度时间序列图",
    "left": "center",
    "textStyle": {
      "fontSize": 16,
      "fontWeight": "bold"
    }
  },
  "tooltip": {
    "trigger": "axis",
    "axisPointer": {
      "type": "cross"
    }
  },
  "legend": {
    "top": "bottom",
    "data": [
      "temperature",
      "humidity"
    ]
  },
  "grid": {
    "left": "3%",
    "right": "4%",
    "bottom": "15%",
    "containLabel": true
  },
  "color": [
    "#5470c6",
    "#91cc75",
    "#fac858",
    "#ee6666",
    "#73c0de",
    "#3ba272",
    "#fc8452",
    "#9a60b4",
    "#ea7ccc",
    "#ff9f7f"
  ],
  "xAxis": {
    "type": "category",
    "data": [
      "2024-01-01 00:00:00",
      "2024-01-01 01:00:00",
      "2024-01-01 02:00:00",
      "2024-01-01 03:00:00",
      "2024-01-01 04:00:00",
      "2024-01-01 05:00:00",
      "2024-01-01 06:00:00",
      "2024-01-01 07:00:00",
      "2024-01-01 08:00:00",
      "2024-01-01 09:00:00",
      "2024-01-01 10:00:00",
      "2024-01-01 11:00:00",
      "2024-01-01 12:00:00",
      "2024-01-01 13:00:00",
      "2024-01-01 14:00:00",
      "2024-01-01 15:00:00",
      "2024-01-01 16:00:00",
      "2024-01-01 17:00:00",
      "2024-01-01 18:00:00",
      "2024-01-01 19:00:00",
      "2024-01-01 20:00:00",
      "2024-01-01 21:00:00",
      "2024-01-01 22:00:00",
      "2024-01-01 23:00:00",
      "2024-01-02 00:00:00",
      "2024-01-02 01:00:00",
      "2024-01-02 02:00:00",
      "2024-01-02 03:00:00",
      "2024-01-02 04:00:00",
      "2024-01-02 05:00:00",
      "2024-01-02 06:00:00",
      "2024-01-02 07:00:00",
      "2024-01-02 08:00:00",
      "2024-01-02 09:00:00",
      "2024-01-02 10:00:00",
      "2024-01-02 11:00:00",
      "2024-01-02 12:00:00",
      "2024-01-02 13:00:00",
      "2024-01-02 14:00:00",
      "2024-01-02 15:00:00",
      "2024-01-02 16:00:00",
      "2024-01-02 17:00:00",
      "2024-01-02 18:00:00",
      "2024-01-02 19:00:00",
      "2024-01-02 20:00:00",
      "2024-01-02 21:00:00",
      "2024-01-02 22:00:00",
      "2024-01-02 23:00:00",
      "2024-01-03 00:00:00",
      "2024-01-03 01:00:00",
      "2024-01-03 02:00:00",
      "2024-01-03 03:00:00",
      "2024-01-03 04:00:00",
      "2024-01-03 05:00:00",
      "2024-01-03 06:00:00",
      "2024-01-03 07:00:00",
      "2024-01-03 08:00:00",
      "2024-01-03 09:00:00",
      "2024-01-03 10:00:00",
      "2024-01-03 11:00:00",
      "2024-01-03 12:00:00",
      "2024-01-03 13:00:00",
      "2024-01-03 14:00:00",
      "2024-01-03 15:00:00",
      "2024-01-03 16:00:00",
      "2024-01-03 17:00:00",
      "2024-01-03 18:00:00",
      "2024-01-03 19:00:00",
      "2024-01-03 20:00:00",
      "2024-01-03 21:00:00",
      "2024-01-03 22:00:00",
      "2024-01-03 23:00:00",
      "2024-01-04 00:00:00",
      "2024-01-04 01:00:00",
      "2024-01-04 02:00:00",
      "2024-01-04 03:00:00",
      "2024-01-04 04:00:00",
      "2024-01-04 05:00:00",
      "2024-01-04 06:00:00",
      "2024-01-04 07:00:00",
      "2024-01-04 08:00:00",
      "2024-01-04 09:00:00",
      "2024-01-04 10:00:00",
      "2024-01-04 11:00:00",
      "2024-01-04 12:00:00",
      "2024-01-04 13:00:00",
      "2024-01-04 14:00:00",
      "2024-01-04 15:00:00",
      "2024-01-04 16:00:00",
      "2024-01-04 17:00:00",
      "2024-01-04 18:00:00",
      "2024-01-04 19:00:00",
      "2024-01-04 20:00:00",
      "2024-01-04 21:00:00",
      "2024-01-04 22:00:00",
      "2024-01-04 23:00:00",
      "2024-01-05 00:00:00",
      "2024-01-05 01:00:00",
      "2024-01-05 02:00:00",
      "2024-01-05 03:00:00"
    ],
    "axisLabel": {
      "rotate": 45
    }
  },
  "yAxis": {
    "type": "value",
    "axisLabel": {
      "formatter": "{value}"
    }
  },
  "series": [
    {
      "name": "temperature",
      "type": "line",
      "data": [
        19.472150464065326,
        18.9011124659412,
        24.586792950307164,
        21.57581418658225,
        23.803196443418262,
        26.53907363409662,
        26.514129741090404,
        27.49744213296412,
        30.671932284440505,
        25.375110191239806,
        29.361430700077918,
        29.742416219101628,
        32.448120244739826,
        33.569938971080894,
        29.08856601781639,
        33.50271816637747,
        31.678810772015943,
        27.577194586409448,
        28.526501579374983,
        29.872132665863365,
        30.47191572642346,
        31.46601532050871,
        25.723764103449277,
        28.523861505838873,
        30.172525893224783,
        25.60995454412434,
        26.122286640904317,
        25.31945358543065,
        25.417043605820286,
        24.950637901206136,
        23.387091392059098,
        19.588476336718752,
        20.74679666509557,
        18.67927586564066,
        16.972453435673188,
        16.36085232025877,
        12.899428377993347,
        13.972768130589396,
        15.19422985793146,
        10.555139108523814,
        13.272873095318982,
        11.094886883822683,
        12.40550795350805,
        12.260503401068753,
        11.280396681374842,
        10.835254040006246,
        9.876248675272203,
        9.580004953251802,
        11.667881041781285,
        8.507343362967877,
        11.227635924522303,
        8.997051813405434,
        11.824463358364515,
        13.857437963306127,
        10.452938719192895,
        10.008964997327386,
        9.796312917833527,
        17.64219320745461,
        11.885017010729195,
        18.902220664908853,
        15.377283152339135,
        17.337411066431898,
        14.001924780176568,
        25.475183511942692,
        23.641517414640816,
        22.3027423001856,
        22.52527496892163,
        23.487608715936688,
        24.743550604225486,
        28.78958245465161,
        28.36760557107739,
        27.620895378537355,
        27.453397650939586,
        29.44873718453869,
        28.450135975611367,
        25.124804787118503,
        24.46701793859731,
        29.32155933270834,
        29.109956931261,
        27.660299075728346,
        31.63549577580704,
        27.960909096719885,
        30.17130571057885,
        27.77727171185939,
        26.24806582014272,
        28.408823000319238,
        24.887373053278345,
        24.770554809200544,
        27.74739529221542,
        26.408658169461788,
        23.852907896040904,
        23.55825612006949,
        23.425857587136772,
        21.563261642344177,
        21.177833201922557,
        14.739728387320048,
        19.496893399772098,
        18.064083194806724,
        16.406321013165673,
        14.8934651141278
      ],
      "smooth": true,
      "symbol": "circle",
      "symbolSize": 4,
      "lineStyle": {
        "width": 2
      }
    },
    {
      "name": "humidity",
      "type": "line",
      "data": [
        73.60285159697746,
        72.53719177092914,
        78.35716023685582,
        60.59684573598768,
        72.7041568428011,
        62.008922672997684,
        63.89958225442634,
        68.9869123057261,
        59.77567692035017,
        55.10763516581696,
        54.28281106217974,
        47.9514087518618,
        49.744181713086164,
        43.70562252390077,
        55.02506754714962,
        39.47112911028372,
        38.72915338788026,
        25.652459371282973,
        24.274860542008454,
        32.070538194906504,
        33.960123695153655,
        24.599954047180102,
        31.783950532560194,
        27.38533731613876,
        37.120918295729965,
        29.40717571551299,
        40.43533156280875,
        33.61458442436571,
        38.159934359337825,
        36.4855830900403,
        50.79714440748223,
        39.48672592682816,
        52.28780182680075,
        52.617958782914286,
        59.89639623250313,
        53.78353991879932,
        52.573974498838254,
        65.42972578142027,
        71.40714445402095,
        67.45705217698203,
        65.50085928848156,
        71.95213017414686,
        63.30208701010351,
        59.31999343898261,
        76.07178401429896,
        73.69635994674326,
        64.54532635992796,
        60.908031092745624,
        65.11368364894447,
        52.09079575912526,
        52.71501566593336,
        49.11412645401661,
        51.29677852653387,
        52.464323651330446,
        39.156782445014805,
        43.4365468395774,
        39.697182864338906,
        43.482770932535075,
        36.603647070700674,
        32.73791590507269,
        28.084299412346482,
        28.922873433363133,
        28.299304592700686,
        27.55032346542155,
        25.73486538445562,
        34.693637237345676,
        32.96820035552717,
        34.733479369459175,
        34.97040875279212,
        47.74836413866996,
        42.870010469039855,
        50.115059938752445,
        53.82506086622463,
        55.23523830555408,
        52.65421665133068,
        52.626258501316954,
        62.40329799614833,
        60.71007931024943,
        60.43257991463247,
        63.25695208849526,
        73.16310388944312,
        69.6133711543594,
        74.3705023024287,
        75.85548035937842,
        66.4523888259301,
        67.7053454152849,
        70.08370002938751,
        62.14257851947912,
        69.86762442554296,
        70.82351245691936,
        66.13228087526065,
        53.89806896900179,
        57.662051018737884,
        57.27828947828576,
        49.65895110732164,
        44.02127853273319,
        49.35750938060816,
        38.780325563866676,
        35.086120805167056,
        31.52393019363236
      ],
      "smooth": true,
      "symbol": "circle",
      "symbolSize": 4,
      "lineStyle": {
        "width": 2
      }
    }
  ]
};
            
            myChart.setOption(option);
            
            // 响应式调整
            window.addEventListener('resize', function() {
                myChart.resize();
            });
            
            // 错误处理
            myChart.on('error', function(params) {
                console.error('ECharts 渲染错误:', params);
            });
        </script>
    </body>
    </html>
    