-- 创建数据库
CREATE DATABASE IF NOT EXISTS sensor_data 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE sensor_data;

-- 创建简化的传感器数据表
CREATE TABLE IF NOT EXISTS sensor_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    timestamp DATETIME NOT NULL,
    temperature DECIMAL(5,2),
    humidity DECIMAL(5,2),
    pressure DECIMAL(8,2),
    flow_rate DECIMAL(8,2),
    voltage DECIMAL(6,2),
    current DECIMAL(6,2),
    power DECIMAL(8,2),
    device_id VARCHAR(20),
    location VARCHAR(50),
    status VARCHAR(20) DEFAULT 'normal',
    
    INDEX idx_timestamp (timestamp),
    INDEX idx_temperature (temperature)
) ENGINE=InnoDB;

-- 插入简单的测试数据
INSERT INTO sensor_data (timestamp, temperature, humidity, pressure, flow_rate, voltage, current, power, device_id, location, status) VALUES
(DATE_SUB(NOW(), INTERVAL 24 HOUR), 25.5, 65.2, 101325.0, 150.0, 220.0, 5.5, 1210.0, 'TEMP_001', 'Workshop A', 'normal'),
(DATE_SUB(NOW(), INTERVAL 23 HOUR), 25.7, 65.0, 101320.0, 152.0, 220.5, 5.6, 1234.8, 'TEMP_001', 'Workshop A', 'normal'),
(DATE_SUB(NOW(), INTERVAL 22 HOUR), 25.6, 64.8, 101318.0, 151.5, 220.2, 5.5, 1211.1, 'TEMP_001', 'Workshop A', 'normal'),
(DATE_SUB(NOW(), INTERVAL 21 HOUR), 25.8, 65.1, 101322.0, 153.0, 220.8, 5.7, 1258.6, 'TEMP_001', 'Workshop A', 'normal'),
(DATE_SUB(NOW(), INTERVAL 20 HOUR), 25.9, 64.9, 101315.0, 154.0, 221.0, 5.8, 1281.8, 'TEMP_001', 'Workshop A', 'normal'),
(DATE_SUB(NOW(), INTERVAL 19 HOUR), 26.1, 66.0, 101330.0, 148.0, 219.5, 5.4, 1185.3, 'TEMP_001', 'Workshop A', 'normal'),
(DATE_SUB(NOW(), INTERVAL 18 HOUR), 26.3, 66.5, 101335.0, 149.0, 219.8, 5.3, 1164.9, 'TEMP_001', 'Workshop A', 'normal'),
(DATE_SUB(NOW(), INTERVAL 17 HOUR), 26.0, 65.8, 101325.0, 155.0, 220.3, 5.9, 1295.7, 'TEMP_001', 'Workshop A', 'normal'),
(DATE_SUB(NOW(), INTERVAL 16 HOUR), 25.4, 64.5, 101310.0, 147.0, 219.0, 5.2, 1138.8, 'TEMP_001', 'Workshop A', 'normal'),
(DATE_SUB(NOW(), INTERVAL 15 HOUR), 25.2, 64.0, 101305.0, 146.0, 218.5, 5.1, 1112.4, 'TEMP_001', 'Workshop A', 'normal'),
(DATE_SUB(NOW(), INTERVAL 10 HOUR), 27.5, 68.0, 101350.0, 160.0, 222.0, 6.0, 1332.0, 'TEMP_001', 'Workshop A', 'normal'),
(DATE_SUB(NOW(), INTERVAL 8 HOUR), 28.2, 69.0, 101360.0, 165.0, 223.0, 6.2, 1382.6, 'TEMP_001', 'Workshop A', 'normal'),
(DATE_SUB(NOW(), INTERVAL 6 HOUR), 29.1, 70.0, 101380.0, 170.0, 224.0, 6.5, 1456.0, 'TEMP_001', 'Workshop A', 'warning'),
(DATE_SUB(NOW(), INTERVAL 4 HOUR), 45.2, 75.0, 101400.0, 180.0, 225.0, 6.8, 1530.0, 'TEMP_001', 'Workshop A', 'warning'),
(DATE_SUB(NOW(), INTERVAL 3 HOUR), 52.8, 78.0, 101450.0, 185.0, 228.0, 7.2, 1641.6, 'TEMP_001', 'Workshop A', 'error'),
(DATE_SUB(NOW(), INTERVAL 2 HOUR), 28.0, 67.0, 101350.0, 160.0, 222.0, 6.0, 1332.0, 'TEMP_001', 'Workshop A', 'normal'),
(DATE_SUB(NOW(), INTERVAL 1 HOUR), 26.8, 65.5, 101340.0, 158.0, 221.5, 5.9, 1306.9, 'TEMP_001', 'Workshop A', 'normal'),
(NOW(), 26.2, 65.0, 101330.0, 155.0, 221.0, 5.8, 1281.8, 'TEMP_001', 'Workshop A', 'normal');

-- 显示结果
SELECT '数据库创建完成！' AS message;
SELECT COUNT(*) AS '数据行数' FROM sensor_data;
SELECT MIN(timestamp) AS '最早时间', MAX(timestamp) AS '最新时间' FROM sensor_data;
