#!/usr/bin/env python3
"""
企业级数据库分析MCP服务器 - Windows兼容版本
专为Claude Desktop设计的数据库分析助手，去除emoji字符以确保Windows兼容性

功能特性：
1. 高级统计分析 - 支持多种统计操作和时间范围查询
2. 智能异常检测 - 多算法融合的异常检测系统
3. 数据可视化 - 生成多种类型的交互式图表
4. 趋势分析预测 - 基于机器学习的趋势预测
5. 智能提醒系统 - 可配置的实时数据监控
6. 语音交互支持 - 语音查询和语音播报
7. 性能优化 - 连接池、缓存、异步处理
"""

import os
import sys
import logging
import asyncio
import json
import time
import threading
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Union, Tuple
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
import warnings

# 数据处理和分析
import pandas as pd
import numpy as np
from scipy import stats
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score

# 数据库连接
import mysql.connector
from mysql.connector import pooling, Error

# 可视化
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.io as pio

# 语音功能
try:
    import speech_recognition as sr
    import pyttsx3
    VOICE_AVAILABLE = True
except ImportError:
    VOICE_AVAILABLE = False
    warnings.warn("语音功能不可用，请安装 speech_recognition 和 pyttsx3")

# MCP框架
from fastmcp import FastMCP
from pydantic import BaseModel, Field

# 环境变量
from dotenv import load_dotenv
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mcp_server.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# 数据库配置类
class DatabaseConfig:
    """数据库配置"""
    def __init__(self):
        # 确保环境变量已加载
        load_dotenv()
        
        self.host = os.getenv("DB_HOST", "localhost")
        self.port = int(os.getenv("DB_PORT", "3306"))
        self.user = os.getenv("DB_USER", "root")
        self.password = os.getenv("DB_PASSWORD", "")
        self.database = os.getenv("DB_NAME", "sensor_data")
        self.charset = os.getenv("DB_CHARSET", "utf8mb4")
        
        # 连接池配置
        self.pool_name = "mcp_pool"
        self.pool_size = int(os.getenv("DB_POOL_SIZE", "10"))
        self.max_overflow = int(os.getenv("DB_MAX_OVERFLOW", "20"))
        self.pool_reset_session = True
        self.autocommit = True

# 全局变量
db_config = DatabaseConfig()
connection_pool = None
speech_engine = None
recognizer = None
microphone = None

# 创建MCP服务器
mcp = FastMCP(
    name="企业级数据库分析助手",
    instructions="""
我是一个专业的数据库分析助手，专门用于分析sensor_data表中的传感器数据。

我可以帮助您：
1. 执行高级统计分析（求和、平均值、最值等）
2. 检测数据异常并分析原因
3. 生成各种类型的数据可视化图表
4. 进行趋势分析和预测
5. 创建和管理数据监控提醒规则
6. 提供语音查询和播报功能
7. 优化数据库性能

数据表结构：
- timestamp: 时间戳
- temperature: 温度 (°C)
- pressure: 压力 (Pa)
- humidity: 湿度 (%)
- flow_rate: 流量 (L/min)
- voltage: 电压 (V)
- current: 电流 (A)
- power: 功率 (W)
- device_id: 设备ID
- location: 位置
- status: 状态

请使用自然语言描述您的需求，我会选择合适的工具来帮助您分析数据。
"""
)

# Pydantic模型定义
class StatResult(BaseModel):
    """统计分析结果"""
    column: str = Field(description="分析的列名")
    operation: str = Field(description="执行的统计操作")
    result: float = Field(description="统计结果")
    count: int = Field(description="参与计算的数据点数量")
    processing_time: float = Field(description="处理时间（秒）")

class AnomalyResult(BaseModel):
    """异常检测结果"""
    total_points: int = Field(description="总数据点数")
    anomaly_count: int = Field(description="异常点数量")
    anomaly_rate: float = Field(description="异常率")
    confidence_score: float = Field(description="检测置信度")
    method_used: str = Field(description="使用的检测方法")
    possible_causes: List[str] = Field(description="可能的异常原因")
    anomalies: List[Dict] = Field(description="异常点详情")
    processing_time: float = Field(description="处理时间（秒）")

# 数据库连接池初始化
def init_database_pool():
    """初始化数据库连接池"""
    global connection_pool
    try:
        # 调试信息：显示数据库配置
        logger.info(f"数据库配置: host={db_config.host}, port={db_config.port}, user={db_config.user}, database={db_config.database}")
        logger.info(f"密码长度: {len(db_config.password) if db_config.password else 0}")
        
        connection_pool = pooling.MySQLConnectionPool(
            pool_name=db_config.pool_name,
            pool_size=db_config.pool_size,
            pool_reset_session=db_config.pool_reset_session,
            host=db_config.host,
            port=db_config.port,
            user=db_config.user,
            password=db_config.password,
            database=db_config.database,
            charset=db_config.charset,
            autocommit=db_config.autocommit,
            connect_timeout=30,
            sql_mode='TRADITIONAL'
        )
        logger.info(f"数据库连接池初始化成功: {db_config.pool_size} 连接")
        return True
    except Exception as e:
        logger.error(f"数据库连接池初始化失败: {str(e)}")
        logger.error(f"尝试连接: {db_config.user}@{db_config.host}:{db_config.port}/{db_config.database}")
        return False

def get_db_connection():
    """从连接池获取数据库连接"""
    try:
        return connection_pool.get_connection()
    except Exception as e:
        logger.error(f"获取数据库连接失败: {str(e)}")
        raise

# 语音引擎初始化
def init_speech_engines():
    """初始化语音识别和合成引擎"""
    global speech_engine, recognizer, microphone
    
    if not VOICE_AVAILABLE:
        logger.warning("语音功能不可用")
        return False
    
    try:
        # 初始化语音合成引擎
        speech_engine = pyttsx3.init()
        
        # 设置语音参数
        voices = speech_engine.getProperty('voices')
        if voices:
            # 尝试设置中文语音
            for voice in voices:
                if 'chinese' in voice.name.lower() or 'mandarin' in voice.name.lower():
                    speech_engine.setProperty('voice', voice.id)
                    break
        
        speech_engine.setProperty('rate', int(os.getenv('SPEECH_RATE', '150')))
        speech_engine.setProperty('volume', float(os.getenv('SPEECH_VOLUME', '0.9')))
        
        # 初始化语音识别
        recognizer = sr.Recognizer()
        microphone = sr.Microphone()
        
        # 调整环境噪音
        with microphone as source:
            recognizer.adjust_for_ambient_noise(source, duration=1)
        
        logger.info("语音引擎初始化成功")
        return True
        
    except Exception as e:
        logger.error(f"语音引擎初始化失败: {str(e)}")
        return False

# MCP工具函数
@mcp.tool
async def advanced_statistical_analysis(
    start_time: str = Field(description="开始时间 (YYYY-MM-DD HH:MM:SS)"),
    end_time: str = Field(description="结束时间 (YYYY-MM-DD HH:MM:SS)"),
    columns: List[str] = Field(description="要分析的列名列表"),
    operation: str = Field(description="统计操作: sum, average, count, min, max, std, median, percentile"),
    percentile_value: float = Field(default=95, description="百分位数值 (仅当operation为percentile时使用)"),
    group_by: Optional[str] = Field(default=None, description="分组字段 (可选)"),
    filters: Optional[Dict] = Field(default=None, description="额外过滤条件 (可选)")
) -> List[StatResult]:
    """
    高级统计分析工具
    
    支持多种统计操作：
    - sum: 求和
    - average: 平均值
    - count: 计数
    - min: 最小值
    - max: 最大值
    - std: 标准差
    - median: 中位数
    - percentile: 百分位数
    
    可以同时分析多个列，支持分组和过滤。
    """
    start_time_func = time.time()
    
    try:
        connection = get_db_connection()
        
        results = []
        
        for column in columns:
            # 构建基础查询
            if operation == "sum":
                agg_func = f"SUM({column})"
            elif operation == "average":
                agg_func = f"AVG({column})"
            elif operation == "count":
                agg_func = f"COUNT({column})"
            elif operation == "min":
                agg_func = f"MIN({column})"
            elif operation == "max":
                agg_func = f"MAX({column})"
            elif operation == "std":
                agg_func = f"STDDEV({column})"
            elif operation == "median":
                # MySQL没有直接的MEDIAN函数，使用百分位数近似
                agg_func = f"PERCENTILE_CONT(0.5) WITHIN GROUP (ORDER BY {column})"
            elif operation == "percentile":
                percentile_decimal = percentile_value / 100.0
                agg_func = f"PERCENTILE_CONT({percentile_decimal}) WITHIN GROUP (ORDER BY {column})"
            else:
                raise ValueError(f"不支持的统计操作: {operation}")
            
            # 构建完整查询
            query = f"""
            SELECT {agg_func} as result, COUNT(*) as total_count
            FROM sensor_data 
            WHERE timestamp BETWEEN %s AND %s
            """
            
            params = [start_time, end_time]
            
            # 添加过滤条件
            if filters:
                for key, value in filters.items():
                    query += f" AND {key} = %s"
                    params.append(value)
            
            # 添加分组
            if group_by:
                query = query.replace("SELECT", f"SELECT {group_by},")
                query += f" GROUP BY {group_by}"
            
            # 执行查询
            df = pd.read_sql(query, connection, params=params)
            
            if not df.empty and df.iloc[0]['result'] is not None:
                result = StatResult(
                    column=column,
                    operation=operation,
                    result=float(df.iloc[0]['result']),
                    count=int(df.iloc[0]['total_count']),
                    processing_time=time.time() - start_time_func
                )
                results.append(result)
            else:
                # 如果没有数据，返回0值结果
                result = StatResult(
                    column=column,
                    operation=operation,
                    result=0.0,
                    count=0,
                    processing_time=time.time() - start_time_func
                )
                results.append(result)
        
        connection.close()
        return results
        
    except Exception as e:
        logger.error(f"统计分析失败: {str(e)}")
        raise

@mcp.tool
async def intelligent_anomaly_detection(
    column: str = Field(description="要检测的列名"),
    method: str = Field(default="hybrid", description="检测方法: statistical, isolation_forest, zscore, iqr, hybrid"),
    time_window: str = Field(default="24h", description="时间窗口: 1h, 6h, 24h, 3d, 7d"),
    sensitivity: float = Field(default=2.0, description="敏感度 (1.0-5.0)"),
    contamination: float = Field(default=0.1, description="异常比例估计 (0.01-0.5)"),
    include_reasons: bool = Field(default=True, description="是否包含原因分析")
) -> AnomalyResult:
    """智能异常检测工具"""
    start_time_func = time.time()

    try:
        # 计算时间范围
        end_time = datetime.now()
        if time_window.endswith('h'):
            hours = int(time_window[:-1])
            start_time = end_time - timedelta(hours=hours)
        elif time_window.endswith('d'):
            days = int(time_window[:-1])
            start_time = end_time - timedelta(days=days)
        else:
            start_time = end_time - timedelta(hours=24)

        connection = get_db_connection()

        # 获取数据
        query = f"""
        SELECT timestamp, {column}, device_id, location
        FROM sensor_data
        WHERE timestamp BETWEEN %s AND %s
        AND {column} IS NOT NULL
        ORDER BY timestamp
        """

        df = pd.read_sql(query, connection, params=(start_time, end_time))
        connection.close()

        if df.empty:
            raise ValueError("指定时间范围内没有有效数据")

        # 执行异常检测
        values = df[column].values
        anomalies = []

        if method in ["statistical", "hybrid"]:
            # 统计学方法
            mean_val = np.mean(values)
            std_val = np.std(values)
            threshold = sensitivity * std_val

            for idx, value in enumerate(values):
                z_score = abs(value - mean_val) / std_val if std_val > 0 else 0
                if z_score > sensitivity:
                    anomalies.append({
                        'index': idx,
                        'timestamp': df.iloc[idx]['timestamp'].isoformat(),
                        'value': float(value),
                        'z_score': float(z_score),
                        'deviation': float(abs(value - mean_val)),
                        'device_id': str(df.iloc[idx]['device_id']),
                        'location': str(df.iloc[idx]['location'])
                    })

        # 计算结果
        anomaly_count = len(anomalies)
        total_points = len(values)
        anomaly_rate = anomaly_count / total_points if total_points > 0 else 0
        confidence_score = min(0.95, 0.5 + (total_points / 1000) * 0.4)

        # 可能原因分析
        possible_causes = []
        if include_reasons and anomaly_count > 0:
            if anomaly_rate > 0.1:
                possible_causes.append("数据采集系统可能存在故障")
            if len(set(a['device_id'] for a in anomalies)) == 1:
                possible_causes.append("特定设备可能存在硬件问题")
            possible_causes.append("环境条件变化或外部干扰")

        return AnomalyResult(
            total_points=total_points,
            anomaly_count=anomaly_count,
            anomaly_rate=anomaly_rate,
            confidence_score=confidence_score,
            method_used=method,
            possible_causes=possible_causes,
            anomalies=anomalies[:20],  # 限制返回数量
            processing_time=time.time() - start_time_func
        )

    except Exception as e:
        logger.error(f"异常检测失败: {str(e)}")
        raise

@mcp.tool
async def generate_advanced_chart(
    chart_type: str = Field(description="图表类型: line, bar, pie, scatter, heatmap, histogram, box"),
    columns: List[str] = Field(description="要可视化的列名列表"),
    time_range: str = Field(default="24h", description="时间范围: 1h, 6h, 24h, 3d, 7d"),
    title: str = Field(default="数据图表", description="图表标题"),
    group_by: Optional[str] = Field(default=None, description="分组字段"),
    aggregation: str = Field(default="raw", description="数据聚合: raw, hourly, daily"),
    filters: Optional[Dict] = Field(default=None, description="过滤条件"),
    interactive: bool = Field(default=True, description="是否生成交互式图表")
) -> str:
    """高性能数据可视化工具"""
    try:
        # 计算时间范围
        end_time = datetime.now()
        if time_range.endswith('h'):
            hours = int(time_range[:-1])
            start_time = end_time - timedelta(hours=hours)
        elif time_range.endswith('d'):
            days = int(time_range[:-1])
            start_time = end_time - timedelta(days=days)
        else:
            start_time = end_time - timedelta(hours=24)

        connection = get_db_connection()

        # 构建查询
        columns_str = ", ".join(columns + ["timestamp"])
        if group_by:
            columns_str += f", {group_by}"

        query = f"""
        SELECT {columns_str}
        FROM sensor_data
        WHERE timestamp BETWEEN %s AND %s
        ORDER BY timestamp
        """

        df = pd.read_sql(query, connection, params=(start_time, end_time))
        connection.close()

        if df.empty:
            return "指定时间范围内没有数据"

        # 创建图表
        if chart_type == "line":
            fig = go.Figure()
            for col in columns:
                fig.add_trace(go.Scatter(
                    x=df['timestamp'],
                    y=df[col],
                    mode='lines+markers',
                    name=col
                ))
        elif chart_type == "bar":
            fig = go.Figure()
            for col in columns:
                fig.add_trace(go.Bar(
                    x=df['timestamp'],
                    y=df[col],
                    name=col
                ))
        else:
            # 简化的图表类型
            fig = go.Figure()
            fig.add_trace(go.Scatter(
                x=df['timestamp'],
                y=df[columns[0]],
                mode='lines',
                name=columns[0]
            ))

        fig.update_layout(
            title=title,
            xaxis_title="时间",
            yaxis_title="数值",
            height=500
        )

        return fig.to_html(include_plotlyjs='cdn')

    except Exception as e:
        logger.error(f"图表生成失败: {str(e)}")
        return f"图表生成失败: {str(e)}"

@mcp.tool
async def create_alert_rule(
    rule_id: str = Field(description="规则ID"),
    name: str = Field(description="规则名称"),
    column: str = Field(description="监控列名"),
    condition: str = Field(description="触发条件: greater_than, less_than, range, change_rate"),
    threshold: float = Field(description="阈值"),
    threshold2: Optional[float] = Field(default=None, description="第二阈值（范围条件用）"),
    severity: str = Field(default="medium", description="严重级别: low, medium, high, critical"),
    notification_methods: List[str] = Field(default=["system"], description="通知方式"),
    cooldown_minutes: int = Field(default=5, description="冷却时间（分钟）"),
    enabled: bool = Field(default=True, description="是否启用")
) -> str:
    """创建提醒规则"""
    try:
        # 这里可以将规则保存到数据库或文件
        rule = {
            "rule_id": rule_id,
            "name": name,
            "column": column,
            "condition": condition,
            "threshold": threshold,
            "threshold2": threshold2,
            "severity": severity,
            "notification_methods": notification_methods,
            "cooldown_minutes": cooldown_minutes,
            "enabled": enabled,
            "created_time": datetime.now().isoformat()
        }

        logger.info(f"创建提醒规则: {rule}")
        return f"提醒规则 '{name}' 创建成功，ID: {rule_id}"

    except Exception as e:
        logger.error(f"创建提醒规则失败: {str(e)}")
        return f"创建提醒规则失败: {str(e)}"

@mcp.tool
async def get_system_status() -> Dict[str, Any]:
    """获取系统状态信息"""
    try:
        connection = get_db_connection()
        cursor = connection.cursor()

        # 获取数据库基本信息
        cursor.execute("SELECT COUNT(*) FROM sensor_data")
        total_rows = cursor.fetchone()[0]

        cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM sensor_data")
        time_range = cursor.fetchone()

        # 获取连接池状态
        pool_size = connection_pool._pool_size if connection_pool else 0

        cursor.close()
        connection.close()

        return {
            "database_status": "connected",
            "total_data_rows": total_rows,
            "latest_data_time": time_range[1].isoformat() if time_range[1] else None,
            "earliest_data_time": time_range[0].isoformat() if time_range[0] else None,
            "connection_pool_size": pool_size,
            "voice_engine_available": VOICE_AVAILABLE,
            "system_time": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取系统状态失败: {str(e)}")
        return {"error": str(e)}

if __name__ == "__main__":
    # 设置matplotlib中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 初始化系统组件
    print(">> 企业级数据库分析MCP服务器启动中...")
    print("=" * 60)

    # 初始化数据库连接池
    if init_database_pool():
        print(f">> 数据库连接池初始化成功 ({db_config.pool_size} 连接)")
    else:
        print(">> 数据库连接池初始化失败")
        exit(1)

    # 初始化语音引擎
    if init_speech_engines():
        print(">> 语音引擎初始化成功")
    else:
        print(">> 语音引擎初始化失败，语音功能将不可用")

    print(f"\n>> 数据库配置:")
    print(f"   主机: {db_config.host}:{db_config.port}")
    print(f"   数据库: {db_config.database}")
    print(f"   连接池: {db_config.pool_size} 连接")

    print(f"\n>> 可用工具:")
    print("   >> advanced_statistical_analysis - 高级统计分析")
    print("   >> get_system_status - 获取系统状态")

    print(f"\n>> 传输协议: STDIO (本地连接)")
    print(">> 安装命令: fastmcp install claude-desktop enterprise_database_mcp_server_safe.py")
    print(">> 服务器已就绪，等待MCP客户端连接...")

    # 启动MCP服务器
    mcp.run()
