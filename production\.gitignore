# 用户指定排除的文件和文件夹
fastmcp_docs/
00.txt

# Python 缓存文件
__pycache__/
*.py[cod]
*$py.class
*.so

# 分发/打包
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
pip-wheel-metadata/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 虚拟环境
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDE 文件
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
logs/
*.log.*

# 临时文件和缓存
temp/
tmp/
cache/
.cache/
*.tmp
*.temp

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# 数据库文件（根据需要调整）
*.db
*.sqlite
*.sqlite3

# 配置文件（可能包含敏感信息）
.env.local
.env.development.local
.env.test.local
.env.production.local
config/secrets.py
secrets.json

# 导出文件目录（运行时生成的文件）
exports/

# 备份文件
*.bak
*.backup
*.old

# Jupyter Notebook
.ipynb_checkpoints

# pytest
.pytest_cache/
.coverage
htmlcov/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Node.js（如果项目中有前端组件）
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*