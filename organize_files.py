#!/usr/bin/env python3
"""
文件整理脚本 - 将项目文件按功能分类整理
"""

import os
import shutil
from pathlib import Path

def create_directories():
    """创建目录结构"""
    directories = [
        'production',           # 生产环境核心文件
        'production/mcp_client', # 客户端文件
        'tests',               # 测试文件
        'docs',                # 文档文件
        'fastmcp_docs',        # FastMCP文档
        'scripts',             # 启动脚本
        'database',            # 数据库相关文件
    ]
    
    for dir_path in directories:
        Path(dir_path).mkdir(parents=True, exist_ok=True)
        print(f"✅ 创建目录: {dir_path}")

def organize_files():
    """整理文件到对应目录"""
    
    # 生产环境核心文件
    production_files = [
        'enterprise_database_mcp_server.py',  # 主服务器
        'mcp_client/simple_http_server.py',   # HTTP服务器
        'mcp_client/enterprise_ai_frontend.py', # 前端界面
        'mcp_client/client.py',
        'mcp_client/config.py',
        'requirements.txt',
        'enterprise_requirements.txt',
    ]
    
    # 测试文件
    test_files = [
        'test_*.py',
        'final_test.py',
        'minimal_mcp_test.py',
        'mcp_client/test_*.py',
        'mcp_client/debug_*.py',
        'mcp_client/simple_debug.py',
    ]
    
    # 文档文件
    doc_files = [
        '*.md',
        '修复说明.md',
        '功能完善总结.md',
        '最终功能完成报告.md',
    ]
    
    # FastMCP文档
    fastmcp_doc_files = [
        '01-null.txt',
        '02-v224-2024-04-25.txt',
        '03-Response-Actions.txt',
        '04-Structured-Data-Access.txt',
        '05-Using-the-FastMCP-CLI.txt',
        '06-Dependencies.txt',
        '07-Server-Authentication.txt',
        '08-Specify-server-object.txt',
        '09-run-supa-hrefhttpsgithubcomjlowinfastmcpblobmainsr.txt',
        '10-add_template-supa-hrefhttpsgithubcomjlowinfastmcpb.txt',
        '11-has_resource_prefix-supa-hrefhttpsgithubcomjlowinf.txt',
        '12-PromptInfo-supa-hrefhttpsgithubcomjlowinfastmcpblo.txt',
        '13-Get-the-active-context-only-works-when-called-with.txt',
        '14-Timing-Middleware.txt',
        '15-Mirrored-Components.txt',
        '16-mcpruntransporthttp-host127001-port9000.txt',
        '17-Binary-Data.txt',
    ]
    
    # 启动脚本
    script_files = [
        'start_*.py',
        'start_*.bat',
        'quick_start.py',
        'clean_restart.py',
        'safe_restart.py',
        'restart_with_new_tool.py',
    ]
    
    # 数据库文件
    database_files = [
        'create_database*.sql',
        '数据库.*',
        'claude_desktop_config.json',
    ]
    
    print("\n📁 开始整理文件...")
    
    # 移动文件的函数
    def move_files(file_patterns, target_dir):
        import glob
        moved_count = 0
        for pattern in file_patterns:
            for file_path in glob.glob(pattern):
                if os.path.isfile(file_path):
                    target_path = os.path.join(target_dir, os.path.basename(file_path))
                    try:
                        shutil.copy2(file_path, target_path)
                        print(f"📄 复制: {file_path} -> {target_path}")
                        moved_count += 1
                    except Exception as e:
                        print(f"❌ 复制失败: {file_path} - {e}")
        return moved_count
    
    # 执行文件移动
    print("\n🔄 复制生产环境文件...")
    move_files(production_files, 'production')
    
    print("\n🧪 复制测试文件...")
    move_files(test_files, 'tests')
    
    print("\n📚 复制文档文件...")
    move_files(doc_files, 'docs')
    
    print("\n📖 复制FastMCP文档...")
    move_files(fastmcp_doc_files, 'fastmcp_docs')
    
    print("\n🚀 复制启动脚本...")
    move_files(script_files, 'scripts')
    
    print("\n🗄️ 复制数据库文件...")
    move_files(database_files, 'database')

def create_startup_scripts():
    """创建启动脚本"""
    
    # 创建主启动脚本
    startup_script = """#!/usr/bin/env python3
# -*- coding: utf-8 -*-
\"\"\"
企业级数据分析系统 - 快速启动脚本
\"\"\"

import subprocess
import sys
import time
import os

def start_mcp_server():
    \"\"\"启动MCP服务器\"\"\"
    print("🚀 启动MCP服务器...")
    cmd = [sys.executable, "enterprise_database_mcp_server.py"]
    return subprocess.Popen(cmd, cwd="production")

def start_http_server():
    \"\"\"启动HTTP服务器\"\"\"
    print("🌐 启动HTTP服务器...")
    cmd = [sys.executable, "mcp_client/simple_http_server.py"]
    return subprocess.Popen(cmd, cwd="production")

def start_frontend():
    \"\"\"启动前端界面\"\"\"
    print("🖥️ 启动前端界面...")
    cmd = ["streamlit", "run", "mcp_client/enterprise_ai_frontend.py", "--server.port=8501"]
    return subprocess.Popen(cmd, cwd="production")

def main():
    print("=" * 60)
    print("🤖 企业级数据分析系统 - 快速启动")
    print("=" * 60)
    
    try:
        # 启动MCP服务器
        mcp_process = start_mcp_server()
        time.sleep(3)
        
        # 启动HTTP服务器
        http_process = start_http_server()
        time.sleep(2)
        
        # 启动前端
        frontend_process = start_frontend()
        
        print("\\n✅ 所有服务已启动!")
        print("📍 前端地址: http://localhost:8501")
        print("📍 MCP服务器: http://127.0.0.1:8000/mcp/")
        print("\\n按 Ctrl+C 停止所有服务")
        
        # 等待用户中断
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\\n🛑 正在停止所有服务...")
            mcp_process.terminate()
            http_process.terminate()
            frontend_process.terminate()
            print("✅ 所有服务已停止")
            
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
"""
    
    with open('production/start_system.py', 'w', encoding='utf-8') as f:
        f.write(startup_script)
    
    # 创建简单的批处理文件
    batch_script = """@echo off
chcp 65001 > nul
echo 🤖 企业级数据分析系统 - 快速启动
echo =====================================

echo 第1步：启动MCP服务器
start "MCP服务器" cmd /k "cd /d production && python enterprise_database_mcp_server.py"

timeout /t 3 > nul

echo 第2步：启动HTTP服务器  
start "HTTP服务器" cmd /k "cd /d production && python mcp_client/simple_http_server.py"

timeout /t 2 > nul

echo 第3步：启动前端界面
start "前端界面" cmd /k "cd /d production && streamlit run mcp_client/enterprise_ai_frontend.py --server.port=8501"

echo.
echo ✅ 所有服务已启动!
echo 📍 前端地址: http://localhost:8501
echo.
pause
"""
    
    with open('production/start_system.bat', 'w', encoding='utf-8') as f:
        f.write(batch_script)
    
    print("✅ 创建启动脚本: production/start_system.py")
    print("✅ 创建启动脚本: production/start_system.bat")

def create_readme():
    """创建README文件"""
    readme_content = """# 企业级数据分析系统 - 生产环境

## 🚀 快速启动

### 方法1：使用Python脚本（推荐）
```bash
cd production
python start_system.py
```

### 方法2：使用批处理文件（Windows）
```bash
cd production
start_system.bat
```

### 方法3：手动启动
```bash
# 第1步：启动MCP服务器
cd production
python enterprise_database_mcp_server.py

# 第2步：启动HTTP服务器（新终端）
cd production
python mcp_client/simple_http_server.py

# 第3步：启动前端界面（新终端）
cd production
streamlit run mcp_client/enterprise_ai_frontend.py --server.port=8501
```

## 📁 文件结构

```
production/                          # 生产环境核心文件
├── enterprise_database_mcp_server.py # 主MCP服务器
├── mcp_client/
│   ├── simple_http_server.py        # HTTP服务器
│   ├── enterprise_ai_frontend.py    # 前端界面
│   └── ...
├── start_system.py                  # Python启动脚本
├── start_system.bat                 # Windows批处理启动脚本
└── requirements.txt                 # 依赖包

tests/                               # 测试文件
docs/                               # 文档文件
fastmcp_docs/                       # FastMCP文档
scripts/                            # 其他启动脚本
database/                           # 数据库相关文件
```

## 🌐 访问地址

- **前端界面**: http://localhost:8501
- **MCP服务器**: http://127.0.0.1:8000/mcp/

## 📋 系统要求

- Python 3.8+
- MySQL 5.7+ 或 8.0+
- 所需Python包（见requirements.txt）

## 🛠️ 功能特性

- ✅ AI数据分析
- ✅ 智能异常检测  
- ✅ 图表生成
- ✅ 智能提醒系统
- ✅ 数据走势分析
- ✅ 语音交互
- ✅ 实时监控

## 🔧 故障排除

如果遇到问题，请检查：
1. Python环境是否正确
2. 依赖包是否安装完整
3. MySQL数据库是否运行
4. 端口8501和8000是否被占用

更多详细信息请查看 docs/ 目录中的文档。
"""
    
    with open('production/README.md', 'w', encoding='utf-8') as f:
        f.write(readme_content)
    
    print("✅ 创建说明文件: production/README.md")

if __name__ == "__main__":
    print("🗂️ 开始整理项目文件...")
    
    create_directories()
    organize_files()
    create_startup_scripts()
    create_readme()
    
    print("\n" + "=" * 60)
    print("✅ 文件整理完成!")
    print("=" * 60)
    print("📁 生产环境文件: production/")
    print("🧪 测试文件: tests/")
    print("📚 文档文件: docs/")
    print("📖 FastMCP文档: fastmcp_docs/")
    print("🚀 启动脚本: scripts/")
    print("🗄️ 数据库文件: database/")
    print("\n🚀 快速启动命令:")
    print("   cd production")
    print("   python start_system.py")
    print("   或者: start_system.bat")
