# Streamlit混合架构部署指南

## 🎯 架构概览

现在您的系统支持**三种使用方式**：

```
┌─────────────────────────────────────────────────────────────────┐
│                    混合架构 - 三种前端选择                        │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    MCP协议    ┌─────────────────────────┐   │
│  │   AI对话前端     │ ←----------→ │                         │   │
│  │                │              │                         │   │
│  │ • Ollama       │              │  企业级MCP数据分析服务器   │   │
│  │ • Open WebUI   │              │                         │   │
│  │ • 语音识别      │              │ • MCP工具接口           │   │
│  │ • 自然语言交互   │              │ • HTTP API接口          │   │
│  └─────────────────┘              │ • 数据分析引擎           │   │
│                                   │ • 异常检测算法           │   │
│  ┌─────────────────┐    HTTP API   │ • 可视化生成器           │   │
│  │ 专业数据前端     │ ←----------→ │ • 趋势预测模型           │   │
│  │                │              │ • 语音交互支持           │   │
│  │ • Streamlit    │              └─────────────────────────┘   │
│  │ • 实时仪表板    │                        │                   │
│  │ • 交互式图表    │                        ▼                   │
│  │ • 报告生成      │              ┌─────────────────────────┐   │
│  │ • 数据导出      │              │      MySQL数据库         │   │
│  │ • 用户管理      │              │                         │   │
│  └─────────────────┘              │ • 大数据量优化           │   │
│                                   │ • 实时数据流             │   │
│                                   └─────────────────────────┘   │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 🚀 快速部署

### 方法1：一键启动（推荐）

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 配置数据库（如果还没有）
mysql -u root -p12369874b -e "source create_db_minimal.sql"

# 3. 一键启动混合系统
python start_hybrid_system.py
```

启动后访问：
- **Streamlit前端**: http://localhost:8501
- **API文档**: http://localhost:8000/docs
- **API接口**: http://localhost:8000/api/

### 方法2：分别启动

#### 启动MCP服务器（HTTP API模式）
```bash
# 设置环境变量
export HTTP_API_MODE=true
export HTTP_API_PORT=8000

# 启动服务器
python enterprise_database_mcp_server.py
```

#### 启动Streamlit前端
```bash
# 设置环境变量
export API_BASE_URL=http://localhost:8000
export REFRESH_INTERVAL=30

# 启动Streamlit
streamlit run streamlit_dashboard.py --server.port 8501
```

## 📊 Streamlit前端功能

### 1. 🏠 实时监控仪表板
- **系统状态监控**：数据总量、连接池、活跃规则、缓存状态
- **最新数据展示**：实时数据表格，支持设备和状态筛选
- **实时趋势图**：可选择多个指标的实时趋势
- **当前警报**：分级显示当前触发的警报

### 2. 📈 统计分析
- **时间范围设置**：支持自定义时间范围或预设范围
- **多列分析**：同时分析多个数据列
- **统计操作**：求和、平均、计数、最值、标准差、中位数、百分位数
- **结果可视化**：自动生成对比图表

### 3. 🚨 异常检测
- **多种检测方法**：
  - 统计学方法（均值±N倍标准差）
  - 孤立森林算法
  - Z-Score标准化
  - 四分位距方法
  - 混合方法（推荐）
- **参数调节**：敏感度、污染率、时间窗口
- **原因分析**：自动分析异常可能原因
- **详细报告**：异常点详情、统计摘要

### 4. 📊 数据可视化
- **丰富图表类型**：
  - 折线图（时间序列）
  - 柱状图（对比分析）
  - 饼状图（比例分析）
  - 散点图（相关性）
  - 热力图（相关性矩阵）
  - 直方图（分布分析）
  - 箱线图（统计分布）
- **数据聚合**：原始数据、按小时、按天聚合
- **交互式图表**：基于Plotly的高质量可视化
- **图表导出**：支持HTML格式下载

### 5. 📉 趋势分析
- **多种预测方法**：
  - 线性回归
  - 多项式回归
  - ARIMA时间序列
  - LSTM神经网络
  - 集成方法（推荐）
- **预测配置**：历史数据范围、预测时长、置信区间
- **趋势指标**：当前值、预测值、变化率、模型准确度
- **可视化预测**：趋势图表展示

### 6. ⚠️ 提醒管理
- **规则创建**：
  - 监控列选择
  - 触发条件（大于、小于、范围、变化率）
  - 阈值设置
  - 严重级别
  - 通知方式
  - 冷却时间
- **规则管理**：查看、编辑现有规则
- **实时警报**：当前触发的警报展示

### 7. ⚙️ 系统管理
- **系统状态**：数据库状态、性能指标
- **数据库优化**：一键优化数据库性能
- **API配置**：API地址、健康检查
- **数据导出**：支持CSV、JSON、Excel格式

## 🎯 使用场景对比

### AI对话前端（Ollama + Open WebUI）
**适合场景**：
- 快速查询和分析
- 自然语言交互
- 语音命令操作
- 临时性分析需求

**使用示例**：
```
"分析过去24小时的温度趋势"
"检测异常数据并告诉我原因"
"语音查询设备运行状态"
"预测未来3天的数据变化"
```

### Streamlit专业前端
**适合场景**：
- 深度数据分析
- 报告生成和导出
- 实时监控仪表板
- 批量数据处理
- 规则管理和配置

**使用示例**：
- 创建实时监控仪表板
- 生成专业分析报告
- 配置复杂的提醒规则
- 导出分析结果数据

## 🔧 高级配置

### Streamlit自定义配置

创建 `.streamlit/config.toml` 文件：
```toml
[server]
port = 8501
address = "0.0.0.0"
headless = true

[browser]
gatherUsageStats = false

[theme]
primaryColor = "#1f77b4"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f0f2f6"
textColor = "#262730"
```

### 环境变量配置

在 `.env` 文件中添加：
```env
# Streamlit前端配置
HTTP_API_MODE=true
HTTP_API_PORT=8000
STREAMLIT_PORT=8501
API_BASE_URL=http://localhost:8000
REFRESH_INTERVAL=30
```

### 生产环境部署

#### 使用Docker部署
```dockerfile
# Dockerfile
FROM python:3.9-slim

WORKDIR /app

COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000 8501

CMD ["python", "start_hybrid_system.py"]
```

#### 使用Nginx反向代理
```nginx
# nginx.conf
server {
    listen 80;
    server_name your-domain.com;

    location /api/ {
        proxy_pass http://localhost:8000/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }

    location / {
        proxy_pass http://localhost:8501/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

## 📱 移动端适配

Streamlit自动适配移动设备，但可以通过CSS优化：

```python
# 在streamlit_dashboard.py中添加
st.markdown("""
<style>
    @media (max-width: 768px) {
        .main-header {
            font-size: 1.5rem;
        }
        .metric-card {
            margin-bottom: 1rem;
        }
    }
</style>
""", unsafe_allow_html=True)
```

## 🔍 故障排除

### 常见问题

1. **Streamlit无法连接API**
   ```bash
   # 检查API服务状态
   curl http://localhost:8000/api/health
   
   # 检查环境变量
   echo $API_BASE_URL
   ```

2. **端口冲突**
   ```bash
   # 检查端口占用
   netstat -an | grep 8501
   netstat -an | grep 8000
   
   # 修改端口
   export STREAMLIT_PORT=8502
   export HTTP_API_PORT=8001
   ```

3. **依赖包问题**
   ```bash
   # 重新安装依赖
   pip install -r requirements.txt --upgrade
   
   # 检查Streamlit版本
   streamlit version
   ```

4. **数据显示异常**
   ```bash
   # 清除Streamlit缓存
   streamlit cache clear
   
   # 重启服务
   python start_hybrid_system.py
   ```

### 性能优化

1. **启用缓存**
   ```python
   @st.cache_data(ttl=300)  # 5分钟缓存
   def get_cached_data():
       return call_api("data/latest")
   ```

2. **分页显示**
   ```python
   # 大数据集分页
   page_size = 100
   page_number = st.number_input("页码", min_value=1, value=1)
   start_idx = (page_number - 1) * page_size
   end_idx = start_idx + page_size
   ```

3. **异步加载**
   ```python
   # 使用占位符异步加载
   placeholder = st.empty()
   with placeholder.container():
       st.info("正在加载数据...")
   
   # 加载完成后更新
   data = load_data()
   placeholder.empty()
   st.dataframe(data)
   ```

## 🎉 总结

现在您拥有了一个完整的**三合一**数据库分析系统：

1. **AI对话分析**：自然语言+语音交互
2. **专业Web界面**：Streamlit仪表板
3. **API接口**：支持第三方集成

用户可以根据不同需求选择最适合的交互方式，既有AI的智能性，又有专业界面的完整性！

---

**🚀 立即开始使用：`python start_hybrid_system.py`** 🎯
