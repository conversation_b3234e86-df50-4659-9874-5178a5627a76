
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
        <style>
            body { margin: 0; padding: 10px; font-family: Arial, sans-serif; }
            #chart { width: 100%; height: 600px; }
        </style>
    </head>
    <body>
        <div id="chart"></div>
        <script>
            var chartDom = document.getElementById('chart');
            var myChart = echarts.init(chartDom);
            var option = {
  "title": {
    "text": "传感器数据相关性热力图",
    "left": "center",
    "textStyle": {
      "fontSize": 16,
      "fontWeight": "bold"
    }
  },
  "tooltip": {
    "trigger": "item",
    "axisPointer": {
      "type": "shadow"
    }
  },
  "legend": {
    "top": "bottom",
    "data": [
      "temperature",
      "humidity",
      "pressure",
      "voltage"
    ]
  },
  "grid": {
    "left": "3%",
    "right": "4%",
    "bottom": "15%",
    "containLabel": true
  },
  "color": [
    "#5470c6",
    "#91cc75",
    "#fac858",
    "#ee6666",
    "#73c0de",
    "#3ba272",
    "#fc8452",
    "#9a60b4",
    "#ea7ccc",
    "#ff9f7f"
  ],
  "xAxis": {
    "type": "category",
    "data": [
      "temperature",
      "humidity",
      "pressure",
      "voltage"
    ],
    "axisLabel": {
      "rotate": 45
    }
  },
  "yAxis": {
    "type": "category",
    "data": [
      "temperature",
      "humidity",
      "pressure",
      "voltage"
    ]
  },
  "visualMap": {
    "min": -1,
    "max": 1,
    "calculable": true,
    "orient": "horizontal",
    "left": "center",
    "bottom": "5%",
    "inRange": {
      "color": [
        "#313695",
        "#4575b4",
        "#74add1",
        "#abd9e9",
        "#e0f3f8",
        "#ffffcc",
        "#fee090",
        "#fdae61",
        "#f46d43",
        "#d73027",
        "#a50026"
      ]
    }
  },
  "series": [
    {
      "name": "相关性",
      "type": "heatmap",
      "data": [
        [
          0,
          0,
          1.0
        ],
        [
          0,
          1,
          -0.095
        ],
        [
          0,
          2,
          -0.223
        ],
        [
          0,
          3,
          -0.026
        ],
        [
          1,
          0,
          -0.095
        ],
        [
          1,
          1,
          1.0
        ],
        [
          1,
          2,
          -0.288
        ],
        [
          1,
          3,
          0.212
        ],
        [
          2,
          0,
          -0.223
        ],
        [
          2,
          1,
          -0.288
        ],
        [
          2,
          2,
          1.0
        ],
        [
          2,
          3,
          0.044
        ],
        [
          3,
          0,
          -0.026
        ],
        [
          3,
          1,
          0.212
        ],
        [
          3,
          2,
          0.044
        ],
        [
          3,
          3,
          1.0
        ]
      ],
      "label": {
        "show": true,
        "formatter": "{c}"
      },
      "emphasis": {
        "itemStyle": {
          "shadowBlur": 10,
          "shadowColor": "rgba(0, 0, 0, 0.5)"
        }
      }
    }
  ]
};
            
            myChart.setOption(option);
            
            // 响应式调整
            window.addEventListener('resize', function() {
                myChart.resize();
            });
            
            // 错误处理
            myChart.on('error', function(params) {
                console.error('ECharts 渲染错误:', params);
            });
        </script>
    </body>
    </html>
    