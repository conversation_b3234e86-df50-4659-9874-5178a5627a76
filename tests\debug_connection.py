#!/usr/bin/env python3
"""
调试连接脚本 - 逐步测试每个环节
"""

import asyncio
import sys
import time
import subprocess
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

def test_server_file():
    """测试服务器文件"""
    print("📁 测试服务器文件")
    print("-" * 30)
    
    server_path = Path(__file__).parent.parent / "enterprise_database_mcp_server.py"
    print(f"服务器路径: {server_path}")
    
    if not server_path.exists():
        print("❌ 服务器文件不存在")
        return False
    
    print("✅ 服务器文件存在")
    
    # 检查文件大小
    size = server_path.stat().st_size
    print(f"文件大小: {size} 字节")
    
    # 尝试直接运行服务器
    print("\n🔍 测试直接运行服务器...")
    try:
        result = subprocess.run([
            sys.executable, str(server_path), "--help"
        ], capture_output=True, text=True, timeout=10)
        
        if result.returncode == 0:
            print("✅ 服务器可以直接运行")
        else:
            print(f"⚠️ 服务器运行有问题: {result.stderr}")
            
    except subprocess.TimeoutExpired:
        print("⏰ 服务器运行超时")
    except Exception as e:
        print(f"❌ 运行服务器失败: {e}")
    
    return True

def test_imports():
    """测试导入"""
    print("\n📦 测试导入")
    print("-" * 30)
    
    imports = [
        ("fastmcp", "from fastmcp import Client"),
        ("fastmcp.client.transports", "from fastmcp.client.transports import StdioTransport"),
        ("asyncio", "import asyncio"),
        ("pathlib", "from pathlib import Path")
    ]
    
    for name, import_stmt in imports:
        try:
            exec(import_stmt)
            print(f"✅ {name}")
        except Exception as e:
            print(f"❌ {name}: {e}")
            return False
    
    return True

async def test_basic_client():
    """测试基础客户端创建"""
    print("\n🤖 测试基础客户端")
    print("-" * 30)
    
    try:
        from fastmcp import Client
        
        server_path = str(Path(__file__).parent.parent / "enterprise_database_mcp_server.py")
        
        print("正在创建客户端...")
        start_time = time.time()
        
        client = Client(server_path)
        
        print(f"✅ 客户端创建成功 ({time.time() - start_time:.2f}秒)")
        print(f"客户端类型: {type(client)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 客户端创建失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

async def test_connection_step_by_step():
    """逐步测试连接"""
    print("\n🔗 逐步测试连接")
    print("-" * 30)
    
    try:
        from fastmcp import Client
        
        server_path = str(Path(__file__).parent.parent / "enterprise_database_mcp_server.py")
        client = Client(server_path)
        
        print("步骤1: 进入客户端上下文...")
        start_time = time.time()
        
        # 设置较短的超时时间进行测试
        try:
            async with asyncio.timeout(15):  # 15秒超时
                async with client:
                    print(f"✅ 步骤1完成 ({time.time() - start_time:.2f}秒)")
                    
                    print("步骤2: 发送ping...")
                    ping_start = time.time()
                    
                    await client.ping()
                    print(f"✅ 步骤2完成 ({time.time() - ping_start:.2f}秒)")
                    
                    print("步骤3: 获取工具列表...")
                    tools_start = time.time()
                    
                    tools = await client.list_tools()
                    print(f"✅ 步骤3完成 ({time.time() - tools_start:.2f}秒)")
                    print(f"找到 {len(tools)} 个工具")
                    
                    return True
                    
        except asyncio.TimeoutError:
            print(f"⏰ 连接超时 (15秒)")
            print("可能的问题:")
            print("1. 服务器启动时间过长")
            print("2. 数据库连接问题") 
            print("3. 环境变量配置问题")
            return False
            
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

async def test_with_environment():
    """测试环境变量配置"""
    print("\n🌍 测试环境变量配置")
    print("-" * 30)
    
    import os
    
    # 设置基本的数据库环境变量
    env_vars = {
        "DB_HOST": "localhost",
        "DB_PORT": "3306",
        "DB_USER": "root", 
        "DB_PASSWORD": "123456",
        "DB_NAME": "sensor_data",
        "DB_CHARSET": "utf8mb4",
        "PYTHONIOENCODING": "utf-8"
    }
    
    print("设置环境变量:")
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  {key} = {value}")
    
    # 现在测试连接
    try:
        from fastmcp import Client
        from fastmcp.client.transports import StdioTransport
        
        server_path = str(Path(__file__).parent.parent / "enterprise_database_mcp_server.py")
        
        # 使用显式的传输配置
        transport = StdioTransport(
            command="python",
            args=[server_path],
            env=env_vars
        )
        
        client = Client(transport)
        
        print("\n使用显式传输配置测试连接...")
        
        try:
            async with asyncio.timeout(20):
                async with client:
                    await client.ping()
                    print("✅ 使用环境变量连接成功!")
                    return True
                    
        except asyncio.TimeoutError:
            print("⏰ 连接超时")
            return False
            
    except Exception as e:
        print(f"❌ 环境变量测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🔍 MCP连接调试工具")
    print("=" * 50)
    
    tests = [
        ("服务器文件检查", test_server_file),
        ("导入测试", test_imports),
        ("基础客户端测试", test_basic_client),
        ("逐步连接测试", test_connection_step_by_step),
        ("环境变量测试", test_with_environment)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
                # 如果某个测试失败，询问是否继续
                continue_test = input("\n是否继续下一个测试? (y/n): ").lower()
                if continue_test != 'y':
                    break
                    
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 测试结果总结:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 通过")
    
    if passed == len(results):
        print("\n🎉 所有测试通过！MCP客户端应该可以正常工作")
    else:
        print("\n⚠️ 部分测试失败，请根据错误信息进行修复")

if __name__ == "__main__":
    asyncio.run(main())
