#!/usr/bin/env python3
"""测试AI智能分析工具"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

async def test_ai_analysis():
    """测试AI智能分析工具"""
    try:
        # 创建客户端
        transport = StreamableHttpTransport("http://127.0.0.1:8000/mcp")
        client = Client(transport)
        
        print("🔗 连接到MCP服务器...")
        
        async with client:
            print("✅ 连接成功")
            
            # 测试AI智能分析工具
            print("\n🧪 测试AI智能分析工具")
            result = await client.call_tool("ai_intelligent_analysis", {
                "data_query": "分析最近7天的温度变化趋势",
                "analysis_type": "trend",
                "context": "工业传感器数据",
                "language": "zh",
                "include_visualization": True
            })
            
            if hasattr(result, 'data') and result.data:
                print("✅ AI智能分析成功")
                print(f"📊 分析结果:")
                print(result.data)
            else:
                print("❌ AI智能分析失败")
                print(f"结果: {result}")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_ai_analysis())
