#!/usr/bin/env python3
"""
数据格式化工具
"""

import json
from datetime import datetime
from typing import Any, Dict, List, Union
from decimal import Decimal

class DataFormatter:
    """数据格式化器"""
    
    @staticmethod
    def format_number(value: Union[int, float, Decimal], precision: int = 2) -> str:
        """格式化数字"""
        if isinstance(value, (int, float, Decimal)):
            if isinstance(value, float) and value.is_integer():
                return str(int(value))
            return f"{value:.{precision}f}"
        return str(value)
    
    @staticmethod
    def format_percentage(value: Union[int, float], precision: int = 1) -> str:
        """格式化百分比"""
        return f"{value * 100:.{precision}f}%"
    
    @staticmethod
    def format_size(bytes_size: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
            if bytes_size < 1024.0:
                return f"{bytes_size:.1f} {unit}"
            bytes_size /= 1024.0
        return f"{bytes_size:.1f} PB"
    
    @staticmethod
    def format_duration(seconds: float) -> str:
        """格式化时间间隔"""
        if seconds < 60:
            return f"{seconds:.1f}秒"
        elif seconds < 3600:
            return f"{seconds/60:.1f}分钟"
        elif seconds < 86400:
            return f"{seconds/3600:.1f}小时"
        else:
            return f"{seconds/86400:.1f}天"

def format_data(data: Any) -> str:
    """格式化数据为可读字符串"""
    if isinstance(data, dict):
        return json.dumps(data, ensure_ascii=False, indent=2)
    elif isinstance(data, list):
        return json.dumps(data, ensure_ascii=False, indent=2)
    elif isinstance(data, (int, float)):
        return DataFormatter.format_number(data)
    else:
        return str(data)

def format_time(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
    """格式化时间"""
    return dt.strftime(format_str)

__all__ = ["DataFormatter", "format_data", "format_time"]
