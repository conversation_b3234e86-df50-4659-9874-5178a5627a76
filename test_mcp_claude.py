#!/usr/bin/env python3
"""
测试MCP服务器 - 专为Claude Desktop设计
简化版本，确保Windows兼容性
"""

import os
import logging
from datetime import datetime
import mysql.connector
import pandas as pd
from dotenv import load_dotenv
from fastmcp import FastMCP
from pydantic import BaseModel, Field

# 加载环境变量
load_dotenv()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据库配置
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', '3306')),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'sensor_data'),
    'charset': os.getenv('DB_CHARSET', 'utf8mb4')
}

# 创建MCP服务器
mcp = FastMCP(
    name="数据库分析助手",
    instructions="我是一个数据库分析助手，可以执行基本的数据查询和分析。"
)

class StatResult(BaseModel):
    """统计结果"""
    column: str = Field(description="列名")
    operation: str = Field(description="操作")
    result: float = Field(description="结果")
    count: int = Field(description="数据点数")

def get_db_connection():
    """获取数据库连接"""
    try:
        logger.info(f"连接数据库: {DB_CONFIG['user']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Exception as e:
        logger.error(f"数据库连接失败: {e}")
        raise

@mcp.tool
async def get_table_info() -> dict:
    """获取数据库表信息"""
    logger.info("获取表信息")
    
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        # 获取表结构
        cursor.execute("DESCRIBE sensor_data")
        columns = cursor.fetchall()
        
        # 获取数据统计
        cursor.execute("SELECT COUNT(*) FROM sensor_data")
        total_rows = cursor.fetchone()[0]
        
        # 获取时间范围
        cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM sensor_data")
        time_range = cursor.fetchone()
        
        cursor.close()
        connection.close()
        
        return {
            "table_name": "sensor_data",
            "columns": [{"field": col[0], "type": col[1]} for col in columns],
            "total_rows": total_rows,
            "time_range": {
                "earliest": time_range[0].isoformat() if time_range[0] else None,
                "latest": time_range[1].isoformat() if time_range[1] else None
            }
        }
        
    except Exception as e:
        logger.error(f"获取表信息失败: {e}")
        return {"error": str(e)}

@mcp.tool
async def simple_statistical_analysis(
    start_time: str = Field(description="开始时间 (YYYY-MM-DD HH:MM:SS)"),
    end_time: str = Field(description="结束时间 (YYYY-MM-DD HH:MM:SS)"),
    column: str = Field(description="要分析的列名"),
    operation: str = Field(description="统计操作: sum, avg, count, min, max")
) -> StatResult:
    """简单统计分析"""
    logger.info(f"执行统计分析: {operation} on {column}")
    
    try:
        connection = get_db_connection()
        
        # 构建查询
        if operation == "sum":
            query = f"SELECT SUM({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
        elif operation == "avg":
            query = f"SELECT AVG({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
        elif operation == "count":
            query = f"SELECT COUNT({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
        elif operation == "min":
            query = f"SELECT MIN({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
        elif operation == "max":
            query = f"SELECT MAX({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
        else:
            raise ValueError(f"不支持的操作: {operation}")
        
        df = pd.read_sql(query, connection, params=(start_time, end_time))
        connection.close()
        
        if not df.empty and df.iloc[0]['result'] is not None:
            return StatResult(
                column=column,
                operation=operation,
                result=float(df.iloc[0]['result']),
                count=int(df.iloc[0]['count'])
            )
        else:
            raise ValueError("没有找到有效数据")
            
    except Exception as e:
        logger.error(f"统计分析失败: {e}")
        raise

@mcp.tool
async def get_latest_data(limit: int = Field(default=10, description="返回的数据行数")) -> dict:
    """获取最新数据"""
    logger.info(f"获取最新 {limit} 条数据")
    
    try:
        connection = get_db_connection()
        
        query = f"""
        SELECT timestamp, temperature, pressure, humidity, device_id, location, status
        FROM sensor_data 
        ORDER BY timestamp DESC 
        LIMIT {limit}
        """
        
        df = pd.read_sql(query, connection)
        connection.close()
        
        # 转换为字典列表
        data = []
        for _, row in df.iterrows():
            record = {}
            for col in df.columns:
                if pd.isna(row[col]):
                    record[col] = None
                elif col == 'timestamp':
                    record[col] = row[col].isoformat()
                else:
                    record[col] = float(row[col]) if isinstance(row[col], (int, float)) else str(row[col])
            data.append(record)
        
        return {
            "data": data,
            "count": len(data)
        }
        
    except Exception as e:
        logger.error(f"获取最新数据失败: {e}")
        return {"error": str(e)}

@mcp.tool
async def get_system_status() -> dict:
    """获取系统状态"""
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM sensor_data")
        total_rows = cursor.fetchone()[0]
        
        cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM sensor_data")
        time_range = cursor.fetchone()
        
        cursor.close()
        connection.close()
        
        return {
            "database_status": "connected",
            "total_data_rows": total_rows,
            "time_range": {
                "earliest": time_range[0].isoformat() if time_range[0] else None,
                "latest": time_range[1].isoformat() if time_range[1] else None
            },
            "system_time": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取系统状态失败: {e}")
        return {"error": str(e)}

if __name__ == "__main__":
    print(">> 数据库分析MCP服务器启动中...")
    print("=" * 50)
    
    # 测试数据库连接
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM sensor_data")
        count = cursor.fetchone()[0]
        cursor.close()
        connection.close()
        
        print(f">> 数据库连接成功，数据行数: {count}")
    except Exception as e:
        print(f">> 数据库连接失败: {e}")
        print("请检查数据库配置和服务状态")
        exit(1)
    
    print(f"\n>> 数据库配置:")
    print(f"   主机: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"   用户: {DB_CONFIG['user']}")
    print(f"   数据库: {DB_CONFIG['database']}")
    
    print(f"\n>> 可用工具:")
    print("   >> get_table_info - 获取表信息")
    print("   >> simple_statistical_analysis - 简单统计分析")
    print("   >> get_latest_data - 获取最新数据")
    print("   >> get_system_status - 获取系统状态")
    
    print(f"\n>> 使用STDIO传输，适合Claude Desktop连接")
    print(">> 安装命令: fastmcp install claude-desktop test_mcp_claude.py")
    print(">> 服务器已就绪，等待MCP客户端连接...")
    
    # 启动MCP服务器
    mcp.run()
