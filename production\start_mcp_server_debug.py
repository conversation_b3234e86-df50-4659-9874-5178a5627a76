#!/usr/bin/env python3
"""
启动MCP服务器 - 调试版本
包含详细的初始化和调试信息
"""

import os
import sys
import time
from dotenv import load_dotenv

def main():
    """主函数"""
    print("🚀 启动MCP服务器 - 调试版本")
    print("=" * 60)
    
    # 加载环境变量
    print("📋 加载环境变量...")
    load_dotenv()
    
    # 显示关键配置
    print("\n🔧 关键配置信息:")
    print(f"   数据库主机: {os.getenv('DB_HOST', '未配置')}")
    print(f"   数据库端口: {os.getenv('DB_PORT', '未配置')}")
    print(f"   数据库名称: {os.getenv('DB_NAME', '未配置')}")
    print(f"   OpenAI模型: {os.getenv('OPENAI_MODEL', '未配置')}")
    print(f"   API密钥: {'已配置' if os.getenv('OPENAI_API_KEY') else '未配置'}")
    print(f"   HTTP API模式: {os.getenv('HTTP_API_MODE', 'false')}")
    print(f"   HTTP API端口: {os.getenv('HTTP_API_PORT', '8000')}")
    
    # 设置HTTP API模式
    os.environ["HTTP_API_MODE"] = "true"
    os.environ["HTTP_API_PORT"] = "8000"
    
    print("\n🌐 启用HTTP API模式")
    print("   地址: http://127.0.0.1:8000/mcp/")
    print("   文档: http://127.0.0.1:8000/docs")
    
    # 导入并启动服务器
    print("\n📦 导入服务器模块...")
    try:
        # 添加当前目录到Python路径
        sys.path.insert(0, os.getcwd())
        
        # 导入服务器模块
        import enterprise_database_mcp_server
        print("✅ 服务器模块导入成功")
        
        print("\n🎯 服务器将在第一次请求时初始化所有组件")
        print("   - 数据库连接池")
        print("   - OpenAI客户端")
        print("   - 语音引擎")
        
        print("\n" + "=" * 60)
        print("🌟 服务器启动中...")
        print("📍 请在浏览器中访问: http://127.0.0.1:8000/docs")
        print("🔄 或启动Streamlit前端: streamlit run mcp_client/enterprise_ai_frontend.py --server.port=8501")
        print("⏹️ 按 Ctrl+C 停止服务器")
        print("=" * 60)
        
        # 这里会启动服务器（在模块导入时自动执行）
        
    except KeyboardInterrupt:
        print("\n\n⏹️ 服务器已停止")
    except Exception as e:
        print(f"\n❌ 服务器启动失败: {e}")
        print(f"🔍 错误类型: {type(e).__name__}")
        print(f"📍 错误详情: {repr(e)}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
