#!/usr/bin/env python3
"""
Database Analysis MCP Server for Claude Desktop
Simple version with Windows compatibility
"""

import os
import logging
from datetime import datetime
import mysql.connector
import pandas as pd
from dotenv import load_dotenv
from fastmcp import FastMCP
from pydantic import BaseModel, Field

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Database configuration
DB_CONFIG = {
    'host': os.getenv('DB_HOST', 'localhost'),
    'port': int(os.getenv('DB_PORT', '3306')),
    'user': os.getenv('DB_USER', 'root'),
    'password': os.getenv('DB_PASSWORD', ''),
    'database': os.getenv('DB_NAME', 'sensor_data'),
    'charset': os.getenv('DB_CHARSET', 'utf8mb4')
}

# Create MCP server
mcp = FastMCP(
    name="Database Analyzer",
    instructions="I am a database analysis assistant that can perform data queries and analysis on sensor_data table."
)

class StatResult(BaseModel):
    """Statistical analysis result"""
    column: str = Field(description="Column name")
    operation: str = Field(description="Operation performed")
    result: float = Field(description="Result value")
    count: int = Field(description="Number of data points")

def get_db_connection():
    """Get database connection"""
    try:
        logger.info(f"Connecting to database: {DB_CONFIG['user']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
        connection = mysql.connector.connect(**DB_CONFIG)
        return connection
    except Exception as e:
        logger.error(f"Database connection failed: {e}")
        raise

@mcp.tool
async def get_table_info() -> dict:
    """Get database table information"""
    logger.info("Getting table information")
    
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        # Get table structure
        cursor.execute("DESCRIBE sensor_data")
        columns = cursor.fetchall()
        
        # Get data statistics
        cursor.execute("SELECT COUNT(*) FROM sensor_data")
        total_rows = cursor.fetchone()[0]
        
        # Get time range
        cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM sensor_data")
        time_range = cursor.fetchone()
        
        cursor.close()
        connection.close()
        
        return {
            "table_name": "sensor_data",
            "columns": [{"field": col[0], "type": col[1]} for col in columns],
            "total_rows": total_rows,
            "time_range": {
                "earliest": time_range[0].isoformat() if time_range[0] else None,
                "latest": time_range[1].isoformat() if time_range[1] else None
            }
        }
        
    except Exception as e:
        logger.error(f"Failed to get table info: {e}")
        return {"error": str(e)}

@mcp.tool
async def statistical_analysis(
    start_time: str = Field(description="Start time (YYYY-MM-DD HH:MM:SS)"),
    end_time: str = Field(description="End time (YYYY-MM-DD HH:MM:SS)"),
    column: str = Field(description="Column name to analyze"),
    operation: str = Field(description="Statistical operation: sum, avg, count, min, max")
) -> StatResult:
    """Perform statistical analysis"""
    logger.info(f"Performing statistical analysis: {operation} on {column}")
    
    try:
        connection = get_db_connection()
        
        # Build query
        if operation == "sum":
            query = f"SELECT SUM({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
        elif operation == "avg":
            query = f"SELECT AVG({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
        elif operation == "count":
            query = f"SELECT COUNT({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
        elif operation == "min":
            query = f"SELECT MIN({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
        elif operation == "max":
            query = f"SELECT MAX({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
        else:
            raise ValueError(f"Unsupported operation: {operation}")
        
        df = pd.read_sql(query, connection, params=(start_time, end_time))
        connection.close()
        
        if not df.empty and df.iloc[0]['result'] is not None:
            return StatResult(
                column=column,
                operation=operation,
                result=float(df.iloc[0]['result']),
                count=int(df.iloc[0]['count'])
            )
        else:
            raise ValueError("No valid data found")
            
    except Exception as e:
        logger.error(f"Statistical analysis failed: {e}")
        raise

@mcp.tool
async def get_latest_data(limit: int = Field(default=10, description="Number of rows to return")) -> dict:
    """Get latest data from sensor_data table"""
    logger.info(f"Getting latest {limit} records")
    
    try:
        connection = get_db_connection()
        
        query = f"""
        SELECT timestamp, temperature, pressure, humidity, device_id, location, status
        FROM sensor_data 
        ORDER BY timestamp DESC 
        LIMIT {limit}
        """
        
        df = pd.read_sql(query, connection)
        connection.close()
        
        # Convert to dictionary list
        data = []
        for _, row in df.iterrows():
            record = {}
            for col in df.columns:
                if pd.isna(row[col]):
                    record[col] = None
                elif col == 'timestamp':
                    record[col] = row[col].isoformat()
                else:
                    record[col] = float(row[col]) if isinstance(row[col], (int, float)) else str(row[col])
            data.append(record)
        
        return {
            "data": data,
            "count": len(data)
        }
        
    except Exception as e:
        logger.error(f"Failed to get latest data: {e}")
        return {"error": str(e)}

@mcp.tool
async def get_system_status() -> dict:
    """Get system status information"""
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        
        cursor.execute("SELECT COUNT(*) FROM sensor_data")
        total_rows = cursor.fetchone()[0]
        
        cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM sensor_data")
        time_range = cursor.fetchone()
        
        cursor.close()
        connection.close()
        
        return {
            "database_status": "connected",
            "total_data_rows": total_rows,
            "time_range": {
                "earliest": time_range[0].isoformat() if time_range[0] else None,
                "latest": time_range[1].isoformat() if time_range[1] else None
            },
            "system_time": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"Failed to get system status: {e}")
        return {"error": str(e)}

@mcp.tool
async def analyze_temperature_trends(hours: int = Field(default=24, description="Number of hours to analyze")) -> dict:
    """Analyze temperature trends over specified hours"""
    try:
        connection = get_db_connection()
        
        # Calculate start time
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=hours)
        
        query = """
        SELECT 
            AVG(temperature) as avg_temp,
            MIN(temperature) as min_temp,
            MAX(temperature) as max_temp,
            COUNT(*) as data_points
        FROM sensor_data 
        WHERE timestamp BETWEEN %s AND %s
        """
        
        df = pd.read_sql(query, connection, params=(start_time, end_time))
        connection.close()
        
        if not df.empty:
            return {
                "time_period": f"Last {hours} hours",
                "average_temperature": float(df.iloc[0]['avg_temp']) if df.iloc[0]['avg_temp'] else 0,
                "minimum_temperature": float(df.iloc[0]['min_temp']) if df.iloc[0]['min_temp'] else 0,
                "maximum_temperature": float(df.iloc[0]['max_temp']) if df.iloc[0]['max_temp'] else 0,
                "data_points": int(df.iloc[0]['data_points']),
                "analysis_time": datetime.now().isoformat()
            }
        else:
            return {"error": "No data found for the specified time period"}
            
    except Exception as e:
        logger.error(f"Temperature trend analysis failed: {e}")
        return {"error": str(e)}

if __name__ == "__main__":
    print(">> Database Analysis MCP Server starting...")
    print("=" * 50)
    
    # Test database connection
    try:
        connection = get_db_connection()
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM sensor_data")
        count = cursor.fetchone()[0]
        cursor.close()
        connection.close()
        
        print(f">> Database connection successful, data rows: {count}")
    except Exception as e:
        print(f">> Database connection failed: {e}")
        print("Please check database configuration and service status")
        exit(1)
    
    print(f"\n>> Database configuration:")
    print(f"   Host: {DB_CONFIG['host']}:{DB_CONFIG['port']}")
    print(f"   User: {DB_CONFIG['user']}")
    print(f"   Database: {DB_CONFIG['database']}")
    
    print(f"\n>> Available tools:")
    print("   >> get_table_info - Get table information")
    print("   >> statistical_analysis - Perform statistical analysis")
    print("   >> get_latest_data - Get latest data")
    print("   >> get_system_status - Get system status")
    print("   >> analyze_temperature_trends - Analyze temperature trends")
    
    print(f"\n>> Using STDIO transport, suitable for Claude Desktop connection")
    print(">> Install command: fastmcp install claude-desktop database_analyzer.py")
    print(">> Server ready, waiting for MCP client connection...")
    
    # Start MCP server
    mcp.run()
