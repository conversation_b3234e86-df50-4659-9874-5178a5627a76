#!/usr/bin/env python3
"""
企业级数据库分析系统 - 完整启动脚本
自动启动MCP服务器、配置Claude Desktop、启动Streamlit前端
"""

import os
import sys
import subprocess
import time
import threading
import webbrowser
from pathlib import Path

def print_header():
    """打印启动标题"""
    print("=" * 60)
    print("🚀 企业级数据库分析系统 - 完整启动")
    print("=" * 60)

def check_database():
    """检查数据库连接"""
    print("\n📊 第一步：检查数据库连接...")
    
    try:
        result = subprocess.run([sys.executable, "test_direct.py"], 
                              capture_output=True, text=True, timeout=30)
        
        if result.returncode == 0:
            print("✅ 数据库连接正常")
            return True
        else:
            print("❌ 数据库连接失败")
            print("错误信息:", result.stderr)
            return False
    except Exception as e:
        print(f"❌ 数据库检查失败: {e}")
        return False

def install_mcp_server():
    """安装MCP服务器到Claude Desktop"""
    print("\n🔧 第二步：配置Claude Desktop...")
    
    try:
        # 获取当前目录
        current_dir = Path.cwd()
        server_path = current_dir / "enterprise_database_mcp_server.py"
        env_path = current_dir / ".env"
        
        # 安装MCP服务器
        cmd = [
            "fastmcp", "install", "claude-desktop", str(server_path),
            "--server-name", "数据库分析助手",
            "--env-file", str(env_path)
        ]
        
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode == 0:
            print("✅ MCP服务器已安装到Claude Desktop")
            print("🔄 请重启Claude Desktop以加载服务器")
            return True
        else:
            print("⚠️ MCP服务器自动安装失败，需要手动配置")
            print_manual_config()
            return False
            
    except FileNotFoundError:
        print("⚠️ FastMCP未安装，需要手动配置Claude Desktop")
        print_manual_config()
        return False
    except Exception as e:
        print(f"⚠️ MCP安装过程出错: {e}")
        print_manual_config()
        return False

def print_manual_config():
    """打印手动配置说明"""
    current_dir = Path.cwd()
    
    print("\n📋 手动配置Claude Desktop:")
    print("1. 打开Claude Desktop设置")
    print("2. 找到MCP服务器配置")
    print("3. 添加新服务器:")
    print(f"   名称: 数据库分析助手")
    print(f"   命令: python")
    print(f"   参数: {current_dir / 'enterprise_database_mcp_server.py'}")
    print(f"   环境文件: {current_dir / '.env'}")

def start_api_server():
    """启动HTTP API服务器"""
    print("\n🌐 第三步：启动HTTP API服务器...")
    
    # 设置环境变量
    env = os.environ.copy()
    env["HTTP_API_MODE"] = "true"
    env["HTTP_API_PORT"] = "8000"
    
    try:
        # 启动API服务器
        process = subprocess.Popen(
            [sys.executable, "enterprise_database_mcp_server.py"],
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        print("✅ HTTP API服务器启动中...")
        print("📍 API地址: http://localhost:8000")
        print("📚 API文档: http://localhost:8000/docs")
        
        # 等待服务器启动
        time.sleep(5)
        
        return process
        
    except Exception as e:
        print(f"❌ API服务器启动失败: {e}")
        return None

def start_streamlit():
    """启动Streamlit前端"""
    print("\n🎨 第四步：启动Streamlit前端...")
    
    # 设置环境变量
    env = os.environ.copy()
    env["API_BASE_URL"] = "http://localhost:8000"
    env["REFRESH_INTERVAL"] = "30"
    
    try:
        # 启动Streamlit
        process = subprocess.Popen(
            [
                sys.executable, "-m", "streamlit", "run",
                "streamlit_dashboard.py",
                "--server.port", "8501",
                "--server.headless", "true",
                "--browser.gatherUsageStats", "false"
            ],
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        print("✅ Streamlit前端启动中...")
        print("🌐 前端地址: http://localhost:8501")
        
        # 等待Streamlit启动
        time.sleep(8)
        
        return process
        
    except Exception as e:
        print(f"❌ Streamlit前端启动失败: {e}")
        return None

def print_success_info():
    """打印成功信息"""
    print("\n" + "=" * 60)
    print("🎉 系统启动完成！")
    print("=" * 60)
    
    print("\n📋 访问地址:")
    print("   🌐 Streamlit前端: http://localhost:8501")
    print("   📚 API文档: http://localhost:8000/docs")
    print("   🤖 Claude Desktop: 重启后可用")
    
    print("\n📝 使用说明:")
    print("   1. 重启Claude Desktop以加载MCP服务器")
    print("   2. 在Claude Desktop中测试: '获取数据库表信息'")
    print("   3. 访问Streamlit前端进行专业数据分析")
    print("   4. 查看API文档了解接口详情")
    
    print("\n🎯 测试命令（Claude Desktop）:")
    print("   • '分析过去24小时的温度平均值'")
    print("   • '检测异常数据，使用混合算法'")
    print("   • '生成温度趋势图表'")
    print("   • '创建温度超过40度的提醒规则'")
    
    print("\n🛑 停止服务: 按 Ctrl+C")
    print("=" * 60)

def open_browser():
    """打开浏览器"""
    print("\n🌐 正在打开浏览器...")
    
    try:
        # 等待服务完全启动
        time.sleep(3)
        
        # 打开Streamlit前端
        webbrowser.open("http://localhost:8501")
        time.sleep(2)
        
        # 打开API文档
        webbrowser.open("http://localhost:8000/docs")
        
    except Exception as e:
        print(f"⚠️ 自动打开浏览器失败: {e}")
        print("请手动访问:")
        print("  http://localhost:8501 (Streamlit前端)")
        print("  http://localhost:8000/docs (API文档)")

def main():
    """主函数"""
    print_header()
    
    # 检查数据库
    if not check_database():
        print("\n❌ 数据库连接失败，请检查MySQL服务和配置")
        input("按回车键退出...")
        return
    
    # 配置Claude Desktop
    install_mcp_server()
    
    # 启动API服务器
    api_process = start_api_server()
    if not api_process:
        print("\n❌ API服务器启动失败")
        input("按回车键退出...")
        return
    
    # 启动Streamlit前端
    streamlit_process = start_streamlit()
    if not streamlit_process:
        print("\n❌ Streamlit前端启动失败")
        if api_process:
            api_process.terminate()
        input("按回车键退出...")
        return
    
    # 打印成功信息
    print_success_info()
    
    # 在后台打开浏览器
    browser_thread = threading.Thread(target=open_browser, daemon=True)
    browser_thread.start()
    
    try:
        # 保持程序运行
        print("\n⏳ 系统运行中，按 Ctrl+C 停止...")
        while True:
            time.sleep(1)
            
            # 检查进程是否还在运行
            if api_process.poll() is not None:
                print("\n⚠️ API服务器意外停止")
                break
                
            if streamlit_process.poll() is not None:
                print("\n⚠️ Streamlit前端意外停止")
                break
                
    except KeyboardInterrupt:
        print("\n\n🛑 正在停止服务...")
        
        # 停止进程
        if api_process:
            api_process.terminate()
            print("✅ API服务器已停止")
            
        if streamlit_process:
            streamlit_process.terminate()
            print("✅ Streamlit前端已停止")
        
        print("👋 系统已完全停止")

if __name__ == "__main__":
    main()
