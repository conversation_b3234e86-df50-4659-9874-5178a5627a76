#!/usr/bin/env python3
"""测试新增功能"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

async def test_new_features():
    """测试新增的MCP工具"""
    try:
        # 创建客户端 - 根据FastMCP文档使用正确的URL格式
        transport = StreamableHttpTransport("http://127.0.0.1:8000/mcp")
        client = Client(transport)
        
        print("🔗 连接到MCP服务器...")
        
        async with client:
            print("✅ 连接成功")
            
            # 测试1: 创建时间提醒
            print("\n🧪 测试1: 创建时间提醒")
            result = await client.call_tool("create_time_alert", {
                "alert_time": "09:00",
                "frequency": "daily",
                "message": "每日数据报告"
            })
            
            if hasattr(result, 'data') and result.data:
                print("✅ 时间提醒创建成功")
                print(f"📊 结果: {result.data}")
            else:
                print("❌ 时间提醒创建失败")
            
            # 测试2: 创建数值提醒
            print("\n🧪 测试2: 创建数值提醒")
            result = await client.call_tool("create_value_alert", {
                "column": "temperature",
                "condition": ">",
                "value": 35.0,
                "action": "notification"
            })
            
            if hasattr(result, 'data') and result.data:
                print("✅ 数值提醒创建成功")
                print(f"📊 结果: {result.data}")
            else:
                print("❌ 数值提醒创建失败")
            
            # 测试3: 获取提醒列表
            print("\n🧪 测试3: 获取提醒列表")
            result = await client.call_tool("list_active_alerts")
            
            if hasattr(result, 'data') and result.data:
                print("✅ 获取提醒列表成功")
                print(f"📊 活动提醒数量: {len(result.data)}")
                for i, alert in enumerate(result.data):
                    if isinstance(alert, dict):
                        print(f"   {i+1}. {alert.get('description', 'No description')}")
                    else:
                        # 处理Pydantic模型对象
                        if hasattr(alert, 'description'):
                            print(f"   {i+1}. {alert.description}")
                        elif hasattr(alert, '__dict__'):
                            print(f"   {i+1}. {alert.__dict__}")
                        else:
                            print(f"   {i+1}. {str(alert)}")
            else:
                print("❌ 获取提醒列表失败")
            
            # 测试4: 综合走势分析
            print("\n🧪 测试4: 综合走势分析")
            result = await client.call_tool("comprehensive_trend_analysis", {
                "columns": ["temperature", "humidity"],
                "time_period": "7d",
                "analysis_types": ["trend", "correlation"],
                "confidence_level": 0.95,
                "include_statistics": True,
                "generate_report": True
            })
            
            if hasattr(result, 'data') and result.data:
                print("✅ 综合走势分析成功")
                print(f"📊 分析结果数量: {len(result.data)}")
                for analysis in result.data:
                    if isinstance(analysis, dict):
                        column = analysis.get('column', 'unknown')
                        data_points = analysis.get('data_points', 0)
                        print(f"   • {column}: {data_points} 个数据点")
            else:
                print("❌ 综合走势分析失败")
            
            # 测试5: 图表生成
            print("\n🧪 测试5: 图表生成")
            result = await client.call_tool("generate_advanced_chart", {
                "chart_type": "line",
                "columns": ["temperature"],
                "time_range": "24h",
                "title": "温度趋势图",
                "aggregation": "hourly",
                "interactive": True
            })
            
            if hasattr(result, 'data') and result.data:
                print("✅ 图表生成成功")
                print(f"📊 结果: {result.data}")
            else:
                print("❌ 图表生成失败")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_new_features())
