#!/usr/bin/env python3
"""
MCP客户端启动脚本
提供多种启动方式：命令行、Web界面、聊天界面
"""

import sys
import asyncio
import argparse
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

def run_cli():
    """运行命令行界面"""
    from client import main
    print("🖥️ 启动命令行界面...")
    asyncio.run(main())

def run_chat():
    """运行聊天界面"""
    try:
        import streamlit.web.cli as stcli
        import sys
        
        chat_script = Path(__file__).parent / "ui" / "chat_interface.py"
        
        print("💬 启动聊天界面...")
        print("🌐 浏览器将自动打开 http://localhost:8501")
        
        sys.argv = [
            "streamlit",
            "run",
            str(chat_script),
            "--server.port=8501",
            "--server.address=localhost",
            "--browser.gatherUsageStats=false"
        ]
        
        stcli.main()
        
    except ImportError:
        print("❌ 未安装streamlit，请运行: pip install streamlit")
    except Exception as e:
        print(f"❌ 启动聊天界面失败: {e}")

def run_dashboard():
    """运行仪表板界面"""
    try:
        import streamlit.web.cli as stcli
        import sys
        
        dashboard_script = Path(__file__).parent / "ui" / "dashboard.py"
        
        print("📊 启动仪表板界面...")
        print("🌐 浏览器将自动打开 http://localhost:8502")
        
        sys.argv = [
            "streamlit",
            "run", 
            str(dashboard_script),
            "--server.port=8502",
            "--server.address=localhost",
            "--browser.gatherUsageStats=false"
        ]
        
        stcli.main()
        
    except ImportError:
        print("❌ 未安装streamlit，请运行: pip install streamlit")
    except Exception as e:
        print(f"❌ 启动仪表板失败: {e}")

def run_examples():
    """运行示例"""
    from examples.basic_usage import main
    print("📚 运行基础使用示例...")
    asyncio.run(main())

def show_info():
    """显示项目信息"""
    from config import config
    
    print("🤖 企业级数据库分析MCP客户端")
    print("=" * 50)
    print(f"版本: {config.app['version']}")
    print(f"描述: {config.app['description']}")
    print(f"作者: {config.app['author']}")
    print()
    print("📋 可用命令:")
    print("  cli      - 命令行界面")
    print("  chat     - 聊天界面 (Web)")
    print("  dashboard- 仪表板界面 (Web)")
    print("  examples - 运行示例")
    print("  info     - 显示项目信息")
    print()
    print("🔧 使用方法:")
    print("  python run.py cli")
    print("  python run.py chat")
    print("  python run.py dashboard")
    print("  python run.py examples")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="企业级数据库分析MCP客户端",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python run.py cli        # 启动命令行界面
  python run.py chat       # 启动聊天界面
  python run.py dashboard  # 启动仪表板界面
  python run.py examples   # 运行示例
        """
    )
    
    parser.add_argument(
        "mode",
        choices=["cli", "chat", "dashboard", "examples", "info"],
        help="启动模式"
    )
    
    parser.add_argument(
        "--debug",
        action="store_true",
        help="启用调试模式"
    )
    
    args = parser.parse_args()
    
    # 设置调试模式
    if args.debug:
        import os
        os.environ["DEBUG"] = "true"
        print("🐛 调试模式已启用")
    
    # 根据模式启动
    try:
        if args.mode == "cli":
            run_cli()
        elif args.mode == "chat":
            run_chat()
        elif args.mode == "dashboard":
            run_dashboard()
        elif args.mode == "examples":
            run_examples()
        elif args.mode == "info":
            show_info()
    
    except KeyboardInterrupt:
        print("\n⏹️ 用户中断")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        if args.debug:
            import traceback
            traceback.print_exc()

if __name__ == "__main__":
    main()
