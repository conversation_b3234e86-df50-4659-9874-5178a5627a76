CREATE TABLE IF NOT EXISTS `sensor_data` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `datetime` DATETIME,
  `1压力mpa` DECIMAL(10,2),
  `温度1_` DECIMAL(10,2),
  `压力2mpa` DECIMAL(10,2),
  `温度2_` DECIMAL(10,2),
  `压力3mpa` DECIMAL(10,2),
  `温度3_` DECIMAL(10,2),
  `压力4mpa` DECIMAL(10,2),
  `压力5mpa` DECIMAL(10,2),
  `温度4_` DECIMAL(10,2),
  `温度5_` DECIMAL(10,2),
  `温度6_` DECIMAL(10,2),
  `含氧_` DECIMAL(10,2),
  `流量m3_h` DECIMAL(10,2),
  `负荷1_` DECIMAL(10,2),
  `负荷2_` DECIMAL(10,2),
  `负荷3_` DECIMAL(10,2),
  `压力7mpa` DECIMAL(10,2),
  `流量2m3_h` INT,
  `created_at` TIMES<PERSON><PERSON> DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTA<PERSON> DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='传感器数据表';