#!/usr/bin/env python3
"""
ECharts 功能测试脚本
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from echarts_utils import EChartsConfigGenerator, create_echarts_html, EChartsDataProcessor

def test_echarts_generation():
    """测试 ECharts 图表生成"""
    print("🧪 开始测试 ECharts 图表生成...")
    
    # 生成测试数据
    dates = pd.date_range(start='2024-01-01', periods=100, freq='h')
    data = {
        'timestamp': dates,
        'temperature': 20 + 10 * np.sin(np.arange(100) * 0.1) + np.random.normal(0, 2, 100),
        'humidity': 50 + 20 * np.cos(np.arange(100) * 0.15) + np.random.normal(0, 5, 100),
        'pressure': 1013 + 10 * np.sin(np.arange(100) * 0.05) + np.random.normal(0, 3, 100)
    }
    df = pd.DataFrame(data)
    
    # 初始化生成器
    generator = EChartsConfigGenerator()
    
    # 测试不同类型的图表
    chart_types = ["line", "bar", "pie", "scatter", "heatmap", "histogram", "box"]
    columns = ["temperature", "humidity", "pressure"]
    
    for chart_type in chart_types:
        try:
            print(f"  📊 测试 {chart_type} 图表...")
            
            # 生成配置
            config = generator.generate_config(
                chart_type=chart_type,
                df=df,
                columns=columns[:2] if chart_type == "scatter" else columns,
                title=f"{chart_type.title()} 测试图表"
            )
            
            # 验证配置
            assert isinstance(config, dict), f"{chart_type} 配置不是字典"
            assert "title" in config, f"{chart_type} 配置缺少标题"
            assert "series" in config, f"{chart_type} 配置缺少系列数据"
            
            # 生成 HTML
            html = create_echarts_html(config)
            assert isinstance(html, str), f"{chart_type} HTML 不是字符串"
            assert "echarts" in html.lower(), f"{chart_type} HTML 缺少 ECharts 引用"
            
            print(f"    ✅ {chart_type} 图表生成成功")
            
        except Exception as e:
            print(f"    ❌ {chart_type} 图表生成失败: {str(e)}")
    
    print("✅ ECharts 图表生成测试完成")

def test_data_processing():
    """测试数据处理功能"""
    print("🧪 开始测试数据处理功能...")
    
    # 生成测试数据
    dates = pd.date_range(start='2024-01-01', periods=50, freq='h')
    data = {
        'timestamp': dates,
        'value1': np.random.normal(50, 10, 50),
        'value2': np.random.normal(30, 5, 50)
    }
    df = pd.DataFrame(data)
    
    # 添加一些缺失值
    df.loc[10:15, 'value1'] = np.nan
    df.loc[20:25, 'value2'] = np.nan
    
    processor = EChartsDataProcessor()
    
    try:
        # 测试时间序列处理
        df_processed = processor.process_time_series(df)
        assert 'timestamp' in df_processed.columns, "时间序列处理失败"
        print("    ✅ 时间序列处理成功")
        
        # 测试数据聚合
        df_aggregated = processor.aggregate_data(df_processed, 'timestamp', 'hourly', ['value1', 'value2'])
        assert len(df_aggregated) <= len(df_processed), "数据聚合失败"
        print("    ✅ 数据聚合成功")
        
        # 测试缺失值处理
        df_filled = processor.handle_missing_values(df_processed)
        assert df_filled['value1'].isna().sum() == 0, "缺失值处理失败"
        print("    ✅ 缺失值处理成功")
        
        # 测试异常值过滤
        df_filtered = processor.filter_outliers(df_filled, ['value1', 'value2'])
        assert len(df_filtered) <= len(df_filled), "异常值过滤失败"
        print("    ✅ 异常值过滤成功")
        
    except Exception as e:
        print(f"    ❌ 数据处理测试失败: {str(e)}")
    
    print("✅ 数据处理功能测试完成")

def test_config_validation():
    """测试配置验证"""
    print("🧪 开始测试配置验证...")
    
    from echarts_utils import validate_echarts_config
    
    # 测试有效配置
    valid_config = {
        "title": {"text": "测试图表"},
        "xAxis": {"type": "category", "data": ["A", "B", "C"]},
        "yAxis": {"type": "value"},
        "series": [{"type": "bar", "data": [1, 2, 3]}]
    }
    
    assert validate_echarts_config(valid_config), "有效配置验证失败"
    print("    ✅ 有效配置验证成功")
    
    # 测试无效配置
    invalid_config = {
        "title": "错误的标题格式",
        "series": "错误的系列格式"
    }
    
    assert not validate_echarts_config(invalid_config), "无效配置验证失败"
    print("    ✅ 无效配置验证成功")
    
    print("✅ 配置验证测试完成")

def test_theme_config():
    """测试主题配置"""
    print("🧪 开始测试主题配置...")
    
    from echarts_utils import get_echarts_theme_config
    
    themes = ["default", "dark", "light"]
    
    for theme in themes:
        try:
            config = get_echarts_theme_config(theme)
            assert isinstance(config, dict), f"{theme} 主题配置不是字典"
            assert "color" in config, f"{theme} 主题配置缺少颜色"
            print(f"    ✅ {theme} 主题配置成功")
        except Exception as e:
            print(f"    ❌ {theme} 主题配置失败: {str(e)}")
    
    print("✅ 主题配置测试完成")

def main():
    """主测试函数"""
    print("🚀 开始 ECharts 功能全面测试")
    print("=" * 50)
    
    try:
        test_echarts_generation()
        print()
        test_data_processing()
        print()
        test_config_validation()
        print()
        test_theme_config()
        print()
        print("🎉 所有测试完成！ECharts 功能正常工作")
        
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
