CREATE TABLE IF NOT EXISTS `sensor_data` (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `日期` VARCHAR(255),
  `1压力
MPa` DECIMAL(10,2),
  `温度1
℃` DECIMAL(10,2),
  `压力2
MPa` DECIMAL(10,2),
  `温度2
℃` DECIMAL(10,2),
  `压力3
MPa` DECIMAL(10,2),
  `温度3
℃` DECIMAL(10,2),
  `压力4
MPa` DECIMAL(10,2),
  `压力5
MPa` DECIMAL(10,2),
  `温度4
℃` DECIMAL(10,2),
  `温度5
℃` DECIMAL(10,2),
  `温度6
℃` DECIMAL(10,2),
  `含氧
%` DECIMAL(10,2),
  `流量
m3_h` DECIMAL(10,2),
  `负荷1
%` DECIMAL(10,2),
  `负荷2
%` DECIMAL(10,2),
  `负荷3
%` DECIMAL(10,2),
  `压力7
MPa` DECIMAL(10,2),
  `流量2
m3_h` INT,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;