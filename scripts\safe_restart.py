#!/usr/bin/env python3
"""
安全重启MCP服务器
"""

import os
import sys
import time
import subprocess

def setup_environment():
    """设置环境变量"""
    env_vars = {
        "HTTP_API_MODE": "true",
        "HTTP_API_PORT": "8000",
        "DB_HOST": "localhost",
        "DB_PORT": "3306",
        "DB_USER": "root",
        "DB_PASSWORD": "123456",
        "DB_NAME": "sensor_data",
        "DB_CHARSET": "utf8mb4",
        "PYTHONIOENCODING": "utf-8"
    }
    
    print("🌍 设置环境变量:")
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  {key} = {value}")

def start_server():
    """启动MCP服务器"""
    print("\n🚀 启动MCP服务器...")
    
    try:
        # 启动服务器
        cmd = [sys.executable, "enterprise_database_mcp_server.py"]
        
        process = subprocess.Popen(
            cmd,
            cwd=os.getcwd(),
            env=os.environ.copy()
        )
        
        print("✅ MCP服务器启动中...")
        print("📍 服务器地址: http://127.0.0.1:8000/mcp")
        print("🆕 包含新工具: ai_intelligent_analysis")
        
        return process
        
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        return None

def main():
    """主函数"""
    print("🔄 安全启动MCP服务器")
    print("=" * 40)
    
    # 设置环境
    setup_environment()
    
    # 启动服务器
    process = start_server()
    if not process:
        print("❌ 服务器启动失败")
        return
    
    print("\n🎉 服务器启动成功！")
    print("现在可以在Streamlit前端中使用AI智能分析功能了！")
    print("\n⏹️ 按 Ctrl+C 停止服务器")
    
    try:
        # 等待用户中断
        process.wait()
    except KeyboardInterrupt:
        print("\n👋 正在停止服务器...")
        process.terminate()
        process.wait()
        print("✅ 服务器已停止")

if __name__ == "__main__":
    main()
