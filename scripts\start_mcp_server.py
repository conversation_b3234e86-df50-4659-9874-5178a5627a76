#!/usr/bin/env python3
"""
启动MCP服务器的简单脚本
"""

import os
import sys

# 设置环境变量
os.environ["HTTP_API_MODE"] = "true"
os.environ["HTTP_API_PORT"] = "8000"
os.environ["DB_HOST"] = "localhost"
os.environ["DB_PORT"] = "3306"
os.environ["DB_USER"] = "root"
os.environ["DB_PASSWORD"] = "123456"
os.environ["DB_NAME"] = "sensor_data"
os.environ["DB_CHARSET"] = "utf8mb4"
os.environ["PYTHONIOENCODING"] = "utf-8"

print("🚀 启动企业级MCP服务器...")
print("🌍 环境变量已设置")

# 导入并启动服务器
try:
    import enterprise_database_mcp_server
    print("✅ 服务器启动成功")
except Exception as e:
    print(f"❌ 服务器启动失败: {e}")
    sys.exit(1)
