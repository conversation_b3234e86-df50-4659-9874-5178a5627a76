
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>ECharts 演示图表</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 {
                color: #333;
                text-align: center;
                margin-bottom: 30px;
            }
            .chart-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-top: 30px;
            }
            .chart-card {
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 20px;
                background-color: #fafafa;
                transition: transform 0.2s;
            }
            .chart-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            }
            .chart-card h3 {
                margin-top: 0;
                color: #555;
            }
            .chart-card a {
                display: inline-block;
                background-color: #007bff;
                color: white;
                padding: 10px 20px;
                text-decoration: none;
                border-radius: 5px;
                margin-top: 10px;
            }
            .chart-card a:hover {
                background-color: #0056b3;
            }
            .info {
                background-color: #e7f3ff;
                border-left: 4px solid #007bff;
                padding: 15px;
                margin-bottom: 20px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎨 ECharts 演示图表集合</h1>
            
            <div class="info">
                <strong>📋 说明：</strong>这是使用 ECharts 替代 Plotly/Matplotlib 后生成的演示图表。
                所有图表都是基于模拟的传感器数据生成的，展示了不同类型图表的渲染效果。
            </div>
            
            <div class="chart-grid">
    
                <div class="chart-card">
                    <h3>📊 温湿度时间序列图</h3>
                    <p><strong>类型：</strong>Line</p>
                    <p><strong>数据列：</strong>temperature, humidity</p>
                    <a href="line_chart.html" target="_blank">查看图表</a>
                </div>
        
                <div class="chart-card">
                    <h3>📊 传感器数据柱状图</h3>
                    <p><strong>类型：</strong>Bar</p>
                    <p><strong>数据列：</strong>temperature, humidity, pressure</p>
                    <a href="bar_chart.html" target="_blank">查看图表</a>
                </div>
        
                <div class="chart-card">
                    <h3>📊 传感器数据分布饼图</h3>
                    <p><strong>类型：</strong>Pie</p>
                    <p><strong>数据列：</strong>temperature, humidity, pressure, voltage, current</p>
                    <a href="pie_chart.html" target="_blank">查看图表</a>
                </div>
        
                <div class="chart-card">
                    <h3>📊 温度与湿度散点图</h3>
                    <p><strong>类型：</strong>Scatter</p>
                    <p><strong>数据列：</strong>temperature, humidity</p>
                    <a href="scatter_chart.html" target="_blank">查看图表</a>
                </div>
        
                <div class="chart-card">
                    <h3>📊 传感器数据相关性热力图</h3>
                    <p><strong>类型：</strong>Heatmap</p>
                    <p><strong>数据列：</strong>temperature, humidity, pressure, voltage</p>
                    <a href="heatmap_chart.html" target="_blank">查看图表</a>
                </div>
        
                <div class="chart-card">
                    <h3>📊 温度分布直方图</h3>
                    <p><strong>类型：</strong>Histogram</p>
                    <p><strong>数据列：</strong>temperature</p>
                    <a href="histogram_chart.html" target="_blank">查看图表</a>
                </div>
        
                <div class="chart-card">
                    <h3>📊 传感器数据箱线图</h3>
                    <p><strong>类型：</strong>Box</p>
                    <p><strong>数据列：</strong>temperature, humidity, pressure</p>
                    <a href="box_chart.html" target="_blank">查看图表</a>
                </div>
        
            </div>
            
            <div style="margin-top: 40px; text-align: center; color: #666;">
                <p>🚀 由 ECharts 强力驱动 | 替代 Plotly/Matplotlib 的现代化图表解决方案</p>
            </div>
        </div>
    </body>
    </html>
    