# FastMCP 核心依赖
fastmcp
mcp

# 数据处理
pandas>=2.0.0
numpy>=1.24.0

# 可视化
plotly>=5.0.0
matplotlib>=3.7.0
seaborn>=0.12.0

# Web界面
streamlit>=1.47.0
gradio>=4.0.0
flask>=3.0.0

# GUI界面
tkinter-tooltip>=2.0.0
customtkinter>=5.2.0

# 异步处理
aiohttp>=3.9.0

# 配置和环境
python-dotenv>=1.0.0
pydantic>=2.0.0

# 日志和调试
loguru>=0.7.0
rich>=13.0.0

# 数据库连接（如果需要直接连接）
mysql-connector-python>=8.0.0
sqlalchemy>=2.0.0

# 工具库
click>=8.0.0
typer>=0.9.0
tqdm>=4.65.0

# 测试
pytest>=7.0.0
pytest-asyncio>=0.21.0
