#!/usr/bin/env python3
"""
启动Streamlit前端的简单脚本
"""

import os
import sys
import subprocess
import time
import webbrowser
from threading import Thread

def start_api_server():
    """启动HTTP API服务器"""
    print("🚀 启动HTTP API服务器...")
    
    # 设置环境变量
    env = os.environ.copy()
    env["HTTP_API_MODE"] = "true"
    env["HTTP_API_PORT"] = "8000"
    
    try:
        process = subprocess.Popen(
            [sys.executable, "enterprise_database_mcp_server.py"],
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        print("✅ API服务器启动中...")
        print("📚 API文档: http://localhost:8000/docs")
        
        # 等待服务器启动
        time.sleep(5)
        return process
        
    except Exception as e:
        print(f"❌ API服务器启动失败: {e}")
        return None

def start_streamlit():
    """启动Streamlit前端"""
    print("🎨 启动Streamlit前端...")
    
    # 设置环境变量
    env = os.environ.copy()
    env["API_BASE_URL"] = "http://localhost:8000"
    env["REFRESH_INTERVAL"] = "30"
    
    try:
        process = subprocess.Popen(
            [
                sys.executable, "-m", "streamlit", "run",
                "streamlit_dashboard.py",
                "--server.port", "8501",
                "--server.headless", "true",
                "--browser.gatherUsageStats", "false"
            ],
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        print("✅ Streamlit前端启动中...")
        print("🌐 前端地址: http://localhost:8501")
        
        # 等待Streamlit启动
        time.sleep(8)
        return process
        
    except Exception as e:
        print(f"❌ Streamlit前端启动失败: {e}")
        return None

def open_browser():
    """打开浏览器"""
    print("🌐 正在打开浏览器...")
    
    try:
        # 等待服务完全启动
        time.sleep(3)
        
        # 打开Streamlit前端
        webbrowser.open("http://localhost:8501")
        time.sleep(2)
        
        # 打开API文档
        webbrowser.open("http://localhost:8000/docs")
        
    except Exception as e:
        print(f"⚠️ 自动打开浏览器失败: {e}")
        print("请手动访问:")
        print("  http://localhost:8501 (Streamlit前端)")
        print("  http://localhost:8000/docs (API文档)")

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 企业级数据库分析系统 - 前端启动器")
    print("=" * 60)
    
    # 启动API服务器
    api_process = start_api_server()
    if not api_process:
        print("\n❌ API服务器启动失败")
        input("按回车键退出...")
        return
    
    # 启动Streamlit前端
    streamlit_process = start_streamlit()
    if not streamlit_process:
        print("\n❌ Streamlit前端启动失败")
        if api_process:
            api_process.terminate()
        input("按回车键退出...")
        return
    
    print("\n" + "=" * 60)
    print("🎉 系统启动成功！")
    print("=" * 60)
    print("📊 Streamlit前端: http://localhost:8501")
    print("📚 API文档: http://localhost:8000/docs")
    print("🔧 MCP服务器: STDIO模式 (用于Claude Desktop)")
    print("=" * 60)
    
    # 在后台打开浏览器
    browser_thread = Thread(target=open_browser, daemon=True)
    browser_thread.start()
    
    try:
        # 保持程序运行
        print("\n⏳ 系统运行中，按 Ctrl+C 停止...")
        while True:
            time.sleep(1)
            
            # 检查进程是否还在运行
            if api_process.poll() is not None:
                print("\n⚠️ API服务器意外停止")
                break
                
            if streamlit_process.poll() is not None:
                print("\n⚠️ Streamlit前端意外停止")
                break
                
    except KeyboardInterrupt:
        print("\n\n🛑 正在停止服务...")
        
        # 停止进程
        if api_process:
            api_process.terminate()
            print("✅ API服务器已停止")
            
        if streamlit_process:
            streamlit_process.terminate()
            print("✅ Streamlit前端已停止")
        
        print("👋 系统已完全停止")

if __name__ == "__main__":
    main()
