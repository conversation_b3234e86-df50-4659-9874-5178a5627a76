#!/usr/bin/env python3
"""
直接测试数据库连接
"""

import mysql.connector
import pandas as pd
from datetime import datetime, timedelta

def test_database_connection():
    """测试数据库连接"""
    print("测试MySQL数据库连接...")
    
    try:
        # 连接数据库
        connection = mysql.connector.connect(
            host="localhost",
            port=3306,
            user="root",
            password="123456",
            database="sensor_data",
            charset="utf8mb4"
        )
        
        cursor = connection.cursor()
        
        # 测试基本查询
        cursor.execute("SELECT COUNT(*) FROM sensor_data")
        count = cursor.fetchone()[0]
        print(f"✅ 数据库连接成功，数据行数: {count}")
        
        # 测试表结构
        cursor.execute("DESCRIBE sensor_data")
        columns = cursor.fetchall()
        print(f"✅ 数据表结构正常，列数: {len(columns)}")
        
        # 测试时间范围
        cursor.execute("SELECT MIN(timestamp), MAX(timestamp) FROM sensor_data")
        time_range = cursor.fetchone()
        print(f"✅ 数据时间范围: {time_range[0]} 到 {time_range[1]}")
        
        # 测试温度数据
        cursor.execute("SELECT AVG(temperature), COUNT(*) FROM sensor_data WHERE temperature IS NOT NULL")
        temp_stats = cursor.fetchone()
        print(f"✅ 温度平均值: {temp_stats[0]:.2f}°C，有效数据点: {temp_stats[1]}")
        
        # 测试异常数据
        cursor.execute("SELECT COUNT(*) FROM sensor_data WHERE temperature > 40")
        high_temp_count = cursor.fetchone()[0]
        print(f"✅ 高温异常数据点 (>40°C): {high_temp_count}")
        
        cursor.close()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {str(e)}")
        return False

def test_pandas_integration():
    """测试pandas集成"""
    print("\n测试pandas数据处理...")
    
    try:
        connection = mysql.connector.connect(
            host="localhost",
            port=3306,
            user="root",
            password="123456",
            database="sensor_data",
            charset="utf8mb4"
        )
        
        # 使用pandas读取数据
        query = "SELECT timestamp, temperature, pressure, humidity FROM sensor_data ORDER BY timestamp DESC LIMIT 10"
        df = pd.read_sql(query, connection)
        
        print(f"✅ pandas读取成功，数据形状: {df.shape}")
        print("最新10条数据:")
        print(df.head())
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ pandas集成失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 数据库分析服务器直接测试")
    print("=" * 50)
    
    # 测试数据库连接
    db_success = test_database_connection()
    
    # 测试pandas集成
    pandas_success = test_pandas_integration()
    
    print("\n" + "=" * 50)
    if db_success and pandas_success:
        print("🎉 所有测试通过！")
        print("\n📋 MCP服务器已准备就绪:")
        print("1. 数据库连接正常")
        print("2. 数据表结构完整")
        print("3. 示例数据已加载")
        print("4. pandas集成正常")
        print("\n🔧 Claude Desktop安装状态:")
        print("✅ MCP服务器已安装到Claude Desktop")
        print("🔄 请重启Claude Desktop以加载服务器")
        print("\n💡 测试命令:")
        print("   '分析过去24小时的温度平均值'")
        print("   '检测温度异常数据'")
        print("   '获取数据库表信息'")
    else:
        print("❌ 部分测试失败，请检查配置")
