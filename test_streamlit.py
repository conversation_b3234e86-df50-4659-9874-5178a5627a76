#!/usr/bin/env python3
"""
测试Streamlit前端是否可以正常启动
"""

import streamlit as st
import pandas as pd
import plotly.graph_objects as go
from datetime import datetime
import os

# 页面配置
st.set_page_config(
    page_title="数据库分析系统测试",
    page_icon="📊",
    layout="wide"
)

# 标题
st.title("📊 企业级数据库分析系统")
st.subheader("Streamlit前端测试页面")

# 测试基本功能
st.success("✅ Streamlit前端启动成功！")

# 显示环境信息
col1, col2 = st.columns(2)

with col1:
    st.info("🔧 环境信息")
    st.write(f"Streamlit版本: {st.__version__}")
    st.write(f"当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    st.write(f"API地址: {os.getenv('API_BASE_URL', 'http://localhost:8000')}")

with col2:
    st.info("📋 功能测试")
    
    # 测试数据表格
    test_data = pd.DataFrame({
        'timestamp': [datetime.now()],
        'temperature': [25.5],
        'pressure': [101325.0],
        'humidity': [65.2]
    })
    
    st.write("测试数据表格:")
    st.dataframe(test_data)

# 测试图表
st.subheader("📈 图表测试")

# 创建测试图表
fig = go.Figure()
fig.add_trace(go.Scatter(
    x=[1, 2, 3, 4, 5],
    y=[10, 11, 12, 13, 14],
    mode='lines+markers',
    name='测试数据'
))

fig.update_layout(
    title="测试折线图",
    xaxis_title="时间",
    yaxis_title="数值"
)

st.plotly_chart(fig, use_container_width=True)

# 测试交互组件
st.subheader("🎛️ 交互组件测试")

col1, col2, col3 = st.columns(3)

with col1:
    test_slider = st.slider("测试滑块", 0, 100, 50)

with col2:
    test_select = st.selectbox("测试选择框", ["选项1", "选项2", "选项3"])

with col3:
    test_button = st.button("测试按钮")

if test_button:
    st.balloons()
    st.success(f"按钮点击成功！滑块值: {test_slider}, 选择: {test_select}")

# 侧边栏测试
st.sidebar.title("📋 侧边栏测试")
st.sidebar.success("侧边栏正常工作")
st.sidebar.info("这是一个测试页面，用于验证Streamlit前端是否正常工作")

# 状态信息
st.sidebar.subheader("🔍 系统状态")
st.sidebar.write("✅ Streamlit: 正常")
st.sidebar.write("⏳ API连接: 测试中...")

# 说明信息
st.markdown("---")
st.info("""
📝 **测试说明**

这是一个简化的测试页面，用于验证Streamlit前端的基本功能：

1. ✅ 页面布局和组件
2. ✅ 数据表格显示
3. ✅ 图表生成
4. ✅ 交互组件
5. ✅ 侧边栏功能

如果您看到这个页面，说明Streamlit前端已经成功启动！

下一步可以启动完整的混合系统来测试与后端API的集成。
""")

st.markdown("---")
st.caption("企业级数据库分析系统 - Streamlit前端测试 © 2024")
