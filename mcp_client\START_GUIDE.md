# 🚀 分步启动指南

## 📋 概述

根据FastMCP文档，我们采用**三层分离架构**：
1. **HTTP MCP服务器** - 独立运行的数据分析服务
2. **HTTP MCP客户端** - 连接服务器的客户端
3. **Web前端界面** - 用户交互界面

## 🎯 启动步骤

### 第1步：启动HTTP服务器 🌐

**在第1个终端窗口运行：**
```bash
cd mcp_client
python step1_start_server.py
```

**预期输出：**
```
🚀 第1步：启动MCP HTTP服务器
==================================================
📁 服务器文件: C:\...\enterprise_database_mcp_server.py
🌍 设置环境变量:
  DB_HOST = localhost
  DB_PORT = 3306
  ...
🌐 启动HTTP服务器...
📍 服务器地址: http://127.0.0.1:8000/mcp/
⏹️ 按 Ctrl+C 停止服务器
--------------------------------------------------
INFO:     Started server process [12345]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://127.0.0.1:8000 (Press CTRL+C to quit)
```

**✅ 成功标志：**
- 看到 "Uvicorn running on http://127.0.0.1:8000"
- 没有错误信息
- 服务器保持运行状态

---

### 第2步：测试客户端连接 🤖

**在第2个终端窗口运行：**
```bash
cd mcp_client
python step2_start_client.py test
```

**预期输出：**
```
🔗 第2步：测试HTTP客户端连接
==================================================
🤖 初始化HTTP MCP客户端
🌐 服务器地址: http://127.0.0.1:8000/mcp/
🔗 正在连接到HTTP MCP服务器...
   正在测试连接...
✅ 连接成功!

📋 测试基本功能:
------------------------------
1. 获取工具列表...
   ✅ 找到 X 个工具:
     • get_system_status: 获取系统状态
     • advanced_statistical_analysis: 高级统计分析
     ...

2. 测试系统状态...
   ✅ 系统状态获取成功
     • 数据库状态: healthy
     • 数据总量: 1,234

🎉 HTTP客户端测试成功!
✅ 现在可以运行第3步启动前端界面
```

**✅ 成功标志：**
- 连接成功
- 获取到工具列表
- 系统状态正常

---

### 第3步：启动前端界面 🖥️

**在第3个终端窗口运行：**
```bash
cd mcp_client
streamlit run step3_start_frontend.py --server.port=8501
```

**预期输出：**
```
  You can now view your Streamlit app in your browser.

  Local URL: http://localhost:8501
  Network URL: http://*************:8501
```

**✅ 成功标志：**
- 浏览器自动打开 http://localhost:8501
- 看到"企业级数据库分析系统"界面
- 连接状态显示正常

---

## 🔧 故障排除

### 问题1：第1步服务器启动失败
**可能原因：**
- 端口8000被占用
- 数据库连接失败
- 依赖包缺失

**解决方案：**
```bash
# 检查端口占用
netstat -an | findstr :8000

# 安装依赖
pip install fastmcp mysql-connector-python

# 检查数据库连接
mysql -h localhost -u root -p
```

### 问题2：第2步客户端连接失败
**可能原因：**
- 第1步服务器未运行
- 网络连接问题
- 防火墙阻止

**解决方案：**
```bash
# 检查服务器是否运行
curl http://127.0.0.1:8000/mcp/

# 检查防火墙设置
# Windows: 允许Python通过防火墙
```

### 问题3：第3步前端无法访问
**可能原因：**
- Streamlit未安装
- 端口8501被占用
- 浏览器缓存问题

**解决方案：**
```bash
# 安装Streamlit
pip install streamlit plotly

# 使用不同端口
streamlit run step3_start_frontend.py --server.port=8502

# 清除浏览器缓存
```

## 📊 架构图

```
┌─────────────────┐    HTTP     ┌─────────────────┐    HTTP     ┌─────────────────┐
│   Web前端界面    │ ◄────────► │   MCP客户端     │ ◄────────► │   MCP服务器     │
│  (Streamlit)    │             │  (HTTP Client)  │             │ (HTTP Server)   │
│  Port: 8501     │             │                 │             │  Port: 8000     │
└─────────────────┘             └─────────────────┘             └─────────────────┘
        │                               │                               │
        │                               │                               │
        ▼                               ▼                               ▼
   用户交互界面                    FastMCP客户端                    数据库分析引擎
```

## 🎯 使用流程

1. **启动服务器** → 第1个终端保持运行
2. **测试客户端** → 第2个终端验证连接
3. **打开前端** → 浏览器访问 http://localhost:8501
4. **开始使用** → 在Web界面进行数据分析

## 📞 技术支持

如果遇到问题：
1. 检查所有3个步骤是否按顺序执行
2. 确认每个步骤的成功标志
3. 查看错误信息并参考故障排除
4. 确保所有依赖包已安装

---

🎉 **恭喜！您已成功部署了分布式MCP数据分析系统！**
