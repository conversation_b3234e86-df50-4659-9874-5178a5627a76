#!/usr/bin/env python3
"""
第2步：启动MCP客户端（连接到HTTP服务器）
基于FastMCP文档的HTTP客户端连接方式
"""

import asyncio
import sys
import time
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

class HTTPMCPClient:
    """HTTP MCP客户端"""
    
    def __init__(self, server_url: str = "http://127.0.0.1:8000/mcp/"):
        """
        初始化HTTP客户端
        
        Args:
            server_url: MCP服务器URL
        """
        self.server_url = server_url
        self.client = None
        
        print(f"🤖 初始化HTTP MCP客户端")
        print(f"🌐 服务器地址: {server_url}")
    
    async def connect(self):
        """连接到HTTP服务器"""
        try:
            print("🔗 正在连接到HTTP MCP服务器...")
            
            # 根据FastMCP文档，使用StreamableHttpTransport
            transport = StreamableHttpTransport(url=self.server_url)
            self.client = Client(transport)
            
            # 测试连接
            print("   正在测试连接...")
            async with self.client:
                await self.client.ping()
                print("✅ 连接成功!")
                return True
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            print("💡 请确保第1步的HTTP服务器正在运行")
            return False
    
    async def get_tools(self):
        """获取可用工具"""
        try:
            async with self.client:
                tools = await self.client.list_tools()
                return tools
        except Exception as e:
            print(f"❌ 获取工具失败: {e}")
            return []
    
    async def call_tool(self, tool_name: str, arguments: dict = None):
        """调用工具"""
        try:
            async with self.client:
                result = await self.client.call_tool(tool_name, arguments or {})
                return result
        except Exception as e:
            print(f"❌ 调用工具失败: {e}")
            return None
    
    async def test_functionality(self):
        """测试基本功能"""
        print("\n📋 测试基本功能:")
        print("-" * 30)
        
        # 获取工具列表
        print("1. 获取工具列表...")
        tools = await self.get_tools()
        if tools:
            print(f"   ✅ 找到 {len(tools)} 个工具:")
            for tool in tools[:5]:  # 显示前5个
                print(f"     • {tool.name}: {tool.description}")
            if len(tools) > 5:
                print(f"     ... 还有 {len(tools) - 5} 个工具")
        else:
            print("   ❌ 无法获取工具列表")
            return False
        
        # 测试系统状态
        print("\n2. 测试系统状态...")
        try:
            result = await self.call_tool("get_system_status")
            if result:
                print("   ✅ 系统状态获取成功")
                
                # 显示结果
                if hasattr(result, 'data') and result.data:
                    data = result.data
                    print(f"     • 数据库状态: {data.get('database_status', 'unknown')}")
                    print(f"     • 数据总量: {data.get('total_data_rows', 0):,}")
                elif hasattr(result, 'content') and result.content:
                    print(f"     • 内容: {result.content}")
                else:
                    print(f"     • 原始结果: {result}")
            else:
                print("   ❌ 系统状态获取失败")
        except Exception as e:
            print(f"   ❌ 系统状态测试失败: {e}")
        
        # 测试统计分析
        print("\n3. 测试统计分析...")
        try:
            result = await self.call_tool("advanced_statistical_analysis", {
                "start_time": "2024-01-01 00:00:00",
                "end_time": "2024-01-02 00:00:00",
                "columns": ["temperature"],
                "operation": "average"
            })
            if result:
                print("   ✅ 统计分析成功")
            else:
                print("   ❌ 统计分析失败")
        except Exception as e:
            print(f"   ❌ 统计分析测试失败: {e}")
        
        return True

async def interactive_mode():
    """交互模式"""
    print("\n💬 进入交互模式")
    print("可用命令: status, tools, stats, anomaly, chart, quit")
    print("-" * 50)
    
    client = HTTPMCPClient()
    
    if not await client.connect():
        print("❌ 无法连接到服务器")
        return
    
    while True:
        try:
            command = input("\n> ").strip().lower()
            
            if command in ['quit', 'exit', 'q']:
                break
            
            elif command == 'status':
                result = await client.call_tool("get_system_status")
                print(f"📊 系统状态: {result}")
            
            elif command == 'tools':
                tools = await client.get_tools()
                print("🛠️ 可用工具:")
                for tool in tools:
                    print(f"  • {tool.name}: {tool.description}")
            
            elif command == 'stats':
                result = await client.call_tool("advanced_statistical_analysis", {
                    "start_time": "2024-01-01 00:00:00",
                    "end_time": "2024-01-02 00:00:00",
                    "columns": ["temperature"],
                    "operation": "average"
                })
                print(f"📈 统计分析: {result}")
            
            elif command == 'anomaly':
                result = await client.call_tool("intelligent_anomaly_detection", {
                    "column": "temperature",
                    "method": "hybrid",
                    "time_window": "24h"
                })
                print(f"🚨 异常检测: {result}")
            
            elif command == 'chart':
                result = await client.call_tool("generate_advanced_chart", {
                    "chart_type": "line",
                    "columns": ["temperature"],
                    "time_range": "24h",
                    "title": "温度趋势图"
                })
                print(f"📊 图表生成: {result}")
            
            else:
                print("❓ 可用命令: status, tools, stats, anomaly, chart, quit")
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("\n👋 退出交互模式")

async def test_connection():
    """测试连接"""
    print("🔗 第2步：测试HTTP客户端连接")
    print("=" * 50)
    
    client = HTTPMCPClient()
    
    # 连接测试
    if not await client.connect():
        print("\n💡 解决方案:")
        print("1. 确保第1步的HTTP服务器正在运行")
        print("2. 检查服务器地址: http://127.0.0.1:8000/mcp/")
        print("3. 检查防火墙设置")
        return False
    
    # 功能测试
    success = await client.test_functionality()
    
    if success:
        print("\n🎉 HTTP客户端测试成功!")
        print("✅ 现在可以运行第3步启动前端界面")
        return True
    else:
        print("\n⚠️ 部分功能测试失败")
        return False

async def main():
    """主函数"""
    print("🤖 MCP HTTP客户端")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        if mode == "interactive":
            await interactive_mode()
        elif mode == "test":
            await test_connection()
        else:
            print("用法: python step2_start_client.py [test|interactive]")
    else:
        # 默认运行测试
        await test_connection()

if __name__ == "__main__":
    asyncio.run(main())
