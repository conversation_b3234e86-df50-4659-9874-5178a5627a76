#!/usr/bin/env python3
"""
仪表板界面 - 基于Streamlit的数据分析仪表板
"""

import asyncio
import streamlit as st
import streamlit.components.v1 as components
import pandas as pd
from datetime import datetime, timedelta
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from mcp_client.client import MCPClient
from mcp_client.config import config

class Dashboard:
    """仪表板"""
    
    def __init__(self):
        self.client = None
        self.setup_page()
    
    def setup_page(self):
        """设置页面配置"""
        st.set_page_config(
            page_title="企业级数据库分析仪表板",
            page_icon="📊",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # 自定义CSS
        st.markdown("""
        <style>
        .metric-card {
            background: white;
            padding: 1rem;
            border-radius: 0.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 0.5rem 0;
        }
        .status-healthy { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        </style>
        """, unsafe_allow_html=True)
    
    async def initialize_client(self):
        """初始化客户端"""
        if self.client is None:
            try:
                self.client = MCPClient()
                success = await self.client.connect()
                return success
            except Exception as e:
                st.error(f"❌ 初始化客户端失败: {e}")
                return False
        return True
    
    def render_header(self):
        """渲染页面头部"""
        st.title("📊 企业级数据库分析仪表板")
        st.markdown("---")
        
        # 连接状态
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            if self.client and self.client.connected:
                st.success("🟢 MCP服务器已连接")
            else:
                st.error("🔴 MCP服务器未连接")
        
        with col2:
            if st.button("🔄 刷新数据"):
                st.rerun()
        
        with col3:
            auto_refresh = st.checkbox("自动刷新", value=False)
            if auto_refresh:
                st.rerun()
    
    async def render_system_overview(self):
        """渲染系统概览"""
        st.subheader("🖥️ 系统概览")
        
        try:
            status = await self.client.get_system_status()
            
            if "error" not in status:
                data = status.get("data", {})
                summary = status.get("summary", {})
                
                # 系统指标
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    db_status = data.get("database_status", "unknown")
                    status_color = "🟢" if db_status == "healthy" else "🔴"
                    st.metric("数据库状态", f"{status_color} {db_status}")
                
                with col2:
                    total_rows = data.get("total_data_rows", 0)
                    st.metric("数据总量", f"{total_rows:,}")
                
                with col3:
                    pool_size = data.get("connection_pool_size", 0)
                    st.metric("连接池", pool_size)
                
                with col4:
                    uptime = data.get("uptime", "unknown")
                    st.metric("运行时间", uptime)
                
                # 详细信息
                with st.expander("📋 详细系统信息"):
                    st.json(data)
            
            else:
                st.error(f"❌ 获取系统状态失败: {status['error']}")
                
        except Exception as e:
            st.error(f"❌ 系统概览错误: {e}")
    
    async def render_data_analysis(self):
        """渲染数据分析"""
        st.subheader("📈 数据分析")
        
        # 分析参数
        col1, col2, col3 = st.columns(3)
        
        with col1:
            columns = st.multiselect(
                "选择数据列",
                ["temperature", "pressure", "humidity", "flow_rate"],
                default=["temperature"]
            )
        
        with col2:
            operation = st.selectbox(
                "统计操作",
                ["average", "sum", "max", "min", "count"],
                index=0
            )
        
        with col3:
            time_range = st.selectbox(
                "时间范围",
                ["1h", "6h", "24h", "7d"],
                index=2
            )
        
        if st.button("🔍 执行分析") and columns:
            try:
                with st.spinner("正在分析数据..."):
                    # 计算时间范围
                    now = datetime.now()
                    if time_range.endswith("h"):
                        hours = int(time_range[:-1])
                        start_time = now - timedelta(hours=hours)
                    elif time_range.endswith("d"):
                        days = int(time_range[:-1])
                        start_time = now - timedelta(days=days)
                    else:
                        start_time = now - timedelta(hours=24)
                    
                    result = await self.client.statistical_analysis(
                        start_time=start_time.strftime("%Y-%m-%d %H:%M:%S"),
                        end_time=now.strftime("%Y-%m-%d %H:%M:%S"),
                        columns=columns,
                        operation=operation
                    )
                
                if "error" not in result:
                    summary = result.get("summary", {})
                    
                    # 显示结果
                    st.success("✅ 分析完成")
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        st.write(f"**操作类型:** {summary.get('operation', 'unknown')}")
                        st.write(f"**数据点数:** {summary.get('data_points', 0):,}")
                    
                    with col2:
                        st.write(f"**分析列:** {', '.join(summary.get('columns_analyzed', []))}")
                        st.write(f"**时间范围:** {summary.get('time_range', 'unknown')}")
                    
                    # 关键指标
                    if "key_metrics" in result:
                        st.subheader("📊 关键指标")
                        metrics = result["key_metrics"]
                        
                        metric_cols = st.columns(len(metrics))
                        for i, (key, value) in enumerate(metrics.items()):
                            with metric_cols[i]:
                                st.metric(key.replace("_", " ").title(), f"{value:.2f}")
                
                else:
                    st.error(f"❌ 分析失败: {result['error']}")
                    
            except Exception as e:
                st.error(f"❌ 分析错误: {e}")
    
    async def render_anomaly_detection(self):
        """渲染异常检测"""
        st.subheader("🚨 异常检测")
        
        # 检测参数
        col1, col2, col3 = st.columns(3)
        
        with col1:
            column = st.selectbox(
                "检测列",
                ["temperature", "pressure", "humidity", "flow_rate"],
                index=0
            )
        
        with col2:
            method = st.selectbox(
                "检测方法",
                ["hybrid", "statistical", "isolation_forest"],
                index=0
            )
        
        with col3:
            sensitivity = st.slider(
                "敏感度",
                min_value=1.0,
                max_value=5.0,
                value=2.0,
                step=0.1
            )
        
        if st.button("🔍 检测异常"):
            try:
                with st.spinner("正在检测异常..."):
                    result = await self.client.detect_anomalies(
                        column=column,
                        method=method,
                        time_window="24h",
                        sensitivity=sensitivity
                    )
                
                if "error" not in result:
                    summary = result.get("summary", {})
                    
                    # 异常摘要
                    col1, col2, col3 = st.columns(3)
                    
                    with col1:
                        st.metric("检测方法", summary.get("method", "unknown"))
                    
                    with col2:
                        st.metric("异常总数", summary.get("total_anomalies", 0))
                    
                    with col3:
                        severity_levels = summary.get("severity_levels", {})
                        high_count = severity_levels.get("high", 0) + severity_levels.get("critical", 0)
                        st.metric("高危异常", high_count)
                    
                    # 严重程度分布
                    if severity_levels:
                        st.subheader("📊 严重程度分布")
                        
                        # 创建饼图
                        labels = []
                        values = []
                        colors = []
                        
                        color_map = {
                            "low": "#28a745",
                            "medium": "#ffc107", 
                            "high": "#fd7e14",
                            "critical": "#dc3545"
                        }
                        
                        for level, count in severity_levels.items():
                            if count > 0:
                                labels.append(level.title())
                                values.append(count)
                                colors.append(color_map.get(level, "#6c757d"))
                        
                        if values:
                            fig = go.Figure(data=[go.Pie(
                                labels=labels,
                                values=values,
                                marker_colors=colors
                            )])
                            fig.update_layout(title="异常严重程度分布")
                            st.plotly_chart(fig, use_container_width=True)
                    
                    # 异常详情
                    if "anomaly_categories" in result:
                        categories = result["anomaly_categories"]
                        
                        if categories.get("high_severity"):
                            st.subheader("⚠️ 高危异常")
                            for anomaly in categories["high_severity"][:10]:
                                st.warning(f"时间: {anomaly.get('timestamp', '未知')}, 值: {anomaly.get('value', '未知')}")
                
                else:
                    st.error(f"❌ 异常检测失败: {result['error']}")
                    
            except Exception as e:
                st.error(f"❌ 异常检测错误: {e}")
    
    async def render_data_visualization(self):
        """渲染数据可视化"""
        st.subheader("📊 数据可视化")
        
        # 图表参数
        col1, col2, col3 = st.columns(3)
        
        with col1:
            chart_type = st.selectbox(
                "图表类型",
                ["line", "bar", "scatter"],
                index=0
            )
        
        with col2:
            columns = st.multiselect(
                "数据列",
                ["temperature", "pressure", "humidity", "flow_rate"],
                default=["temperature"]
            )
        
        with col3:
            time_range = st.selectbox(
                "时间范围",
                ["1h", "6h", "24h", "7d"],
                index=2,
                key="viz_time_range"
            )
        
        chart_title = st.text_input("图表标题", value="数据趋势图")
        
        if st.button("📈 生成图表") and columns:
            try:
                with st.spinner("正在生成图表..."):
                    result = await self.client.generate_chart(
                        chart_type=chart_type,
                        columns=columns,
                        time_range=time_range,
                        title=chart_title
                    )
                
                if "error" not in result:
                    # 直接渲染 ECharts HTML
                    chart_html = result.get("data", "")

                    if chart_html and isinstance(chart_html, str) and "echarts" in chart_html.lower():
                        # 渲染 ECharts 图表
                        components.html(chart_html, height=500, scrolling=False)
                    else:
                        st.warning("📊 图表数据格式不正确")
                
                else:
                    st.error(f"❌ 图表生成失败: {result['error']}")
                    
            except Exception as e:
                st.error(f"❌ 图表生成错误: {e}")
    
    def render_sidebar(self):
        """渲染侧边栏"""
        with st.sidebar:
            st.header("🛠️ 控制面板")
            
            # 连接状态
            if self.client and self.client.connected:
                st.success("🟢 已连接")
            else:
                st.error("🔴 未连接")
            
            st.divider()
            
            # 快速操作
            st.subheader("⚡ 快速操作")
            
            if st.button("🔄 刷新所有数据", use_container_width=True):
                st.rerun()
            
            if st.button("📊 系统状态", use_container_width=True):
                st.session_state.show_system_status = True
            
            if st.button("🚨 异常检测", use_container_width=True):
                st.session_state.show_anomaly_detection = True
            
            st.divider()
            
            # 设置
            st.subheader("⚙️ 设置")
            
            refresh_interval = st.slider(
                "刷新间隔(秒)",
                min_value=10,
                max_value=300,
                value=30,
                step=10
            )
            
            show_debug = st.checkbox("显示调试信息", value=False)
            
            if show_debug:
                st.subheader("🐛 调试信息")
                st.write(f"客户端状态: {self.client.connected if self.client else 'None'}")
                st.write(f"配置: {config.app['name']}")
    
    async def run(self):
        """运行仪表板"""
        # 初始化客户端
        if not await self.initialize_client():
            st.error("❌ 无法初始化MCP客户端")
            return
        
        # 渲染页面
        self.render_header()
        self.render_sidebar()
        
        # 主要内容
        tab1, tab2, tab3, tab4 = st.tabs(["🖥️ 系统概览", "📈 数据分析", "🚨 异常检测", "📊 数据可视化"])
        
        with tab1:
            await self.render_system_overview()
        
        with tab2:
            await self.render_data_analysis()
        
        with tab3:
            await self.render_anomaly_detection()
        
        with tab4:
            await self.render_data_visualization()

def main():
    """主函数"""
    dashboard = Dashboard()
    asyncio.run(dashboard.run())

if __name__ == "__main__":
    main()
