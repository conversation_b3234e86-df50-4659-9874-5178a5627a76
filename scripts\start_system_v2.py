#!/usr/bin/env python3
"""
企业级数据分析系统启动脚本 v2.0
基于FastMCP文档的最佳实践
"""

import os
import sys
import time
import subprocess
import webbrowser
from pathlib import Path

def setup_environment():
    """设置环境变量"""
    env_vars = {
        "DB_HOST": "localhost",
        "DB_PORT": "3306", 
        "DB_USER": "root",
        "DB_PASSWORD": "123456",
        "DB_NAME": "sensor_data",
        "DB_CHARSET": "utf8mb4",
        "PYTHONIOENCODING": "utf-8",
        "HTTP_API_MODE": "true",
        "HTTP_API_PORT": "8000"
    }
    
    print("🌍 设置环境变量:")
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  {key} = {value}")

def check_dependencies():
    """检查依赖"""
    print("\n🔍 检查依赖...")
    
    try:
        import fastmcp
        print(f"✅ FastMCP: {fastmcp.__version__}")
    except ImportError:
        print("❌ FastMCP未安装")
        return False
    
    try:
        import streamlit
        print(f"✅ Streamlit: {streamlit.__version__}")
    except ImportError:
        print("❌ Streamlit未安装")
        return False
    
    return True

def start_mcp_server():
    """启动MCP服务器"""
    print("\n🚀 启动MCP服务器...")
    
    server_path = Path(__file__).parent / "enterprise_database_mcp_server.py"
    
    if not server_path.exists():
        print(f"❌ 服务器文件不存在: {server_path}")
        return False
    
    try:
        # 直接运行服务器文件
        cmd = [sys.executable, str(server_path)]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        # 启动服务器进程
        process = subprocess.Popen(
            cmd,
            cwd=str(server_path.parent),
            env=os.environ.copy()
        )
        
        print("✅ MCP服务器启动中...")
        print("📍 服务器地址: http://127.0.0.1:8000/mcp")
        
        # 等待服务器启动
        time.sleep(8)
        
        return True
        
    except Exception as e:
        print(f"❌ MCP服务器启动失败: {e}")
        return False

def start_streamlit():
    """启动Streamlit前端"""
    print("\n🌐 启动Streamlit前端...")
    
    frontend_path = Path(__file__).parent / "mcp_client" / "enterprise_ai_frontend.py"
    
    if not frontend_path.exists():
        print(f"❌ 前端文件不存在: {frontend_path}")
        return False
    
    try:
        cmd = [
            sys.executable, "-m", "streamlit", "run",
            str(frontend_path),
            "--server.port=8501",
            "--server.headless=false"
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        
        # 启动前端进程
        process = subprocess.Popen(
            cmd,
            cwd=str(frontend_path.parent.parent)
        )
        
        print("✅ Streamlit前端启动中...")
        print("📍 Web界面: http://localhost:8501")
        
        # 等待前端启动
        time.sleep(5)
        
        # 自动打开浏览器
        try:
            webbrowser.open("http://localhost:8501")
            print("🌐 已自动打开浏览器")
        except:
            print("⚠️ 无法自动打开浏览器，请手动访问 http://localhost:8501")
        
        return True
        
    except Exception as e:
        print(f"❌ Streamlit前端启动失败: {e}")
        return False

def test_connection():
    """测试连接"""
    print("\n🧪 测试系统连接...")
    
    try:
        import asyncio
        from fastmcp import Client
        from fastmcp.client.transports import StreamableHttpTransport
        
        async def test():
            try:
                transport = StreamableHttpTransport("http://127.0.0.1:8000/mcp")
                client = Client(transport)
                
                async with client:
                    # 测试基础工具调用
                    result = await client.call_tool("get_system_status")
                    print("✅ MCP连接测试成功")
                    return True
            except Exception as e:
                print(f"❌ MCP连接测试失败: {e}")
                return False
        
        return asyncio.run(test())
        
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎯 企业级数据分析系统启动器 v2.0")
    print("=" * 60)
    print("基于FastMCP 2.10.5 + Streamlit")
    print("支持：统计分析、异常检测、智能提醒、图表生成、走势分析")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        print("\n❌ 依赖检查失败，请安装必要的包")
        return
    
    # 设置环境
    setup_environment()
    
    # 启动MCP服务器
    if not start_mcp_server():
        print("\n❌ MCP服务器启动失败，退出")
        return
    
    # 启动Streamlit前端
    if not start_streamlit():
        print("\n❌ Streamlit前端启动失败")
        return
    
    # 测试连接
    print("\n⏳ 等待系统完全启动...")
    time.sleep(3)
    
    if test_connection():
        print("\n🎉 系统启动完成!")
        print("\n📋 功能清单:")
        print("  ✅ 统计分析：按条件求和、求平均")
        print("  ✅ 异常检测：智能异常检测和原因分析")
        print("  ✅ 智能提醒：时间和数值双重提醒机制")
        print("  ✅ 图表生成：多种图表类型支持")
        print("  ✅ 走势分析：深度趋势分析和预测")
        print("  ✅ 语音交互：语音识别和播报")
        print("  ✅ 示例查询：8个预设查询模板")
        
        print("\n🔗 访问地址:")
        print("  📍 Web界面: http://localhost:8501")
        print("  📍 MCP服务器: http://127.0.0.1:8000/mcp")
        
        print("\n💡 使用提示:")
        print("  1. 在Web界面中测试各种功能")
        print("  2. 使用示例查询快速开始")
        print("  3. 查看系统状态确认连接正常")
        
        print("\n⏹️ 按 Ctrl+C 停止服务器进程")
        
        try:
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n👋 系统已停止")
    else:
        print("\n❌ 系统连接测试失败")

if __name__ == "__main__":
    main()
