#!/usr/bin/env python3
"""
简化的前端界面
基于Streamlit的Web界面，连接到HTTP MCP服务器
"""

import streamlit as st
import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

class SimpleFrontend:
    """简化前端界面"""
    
    def __init__(self):
        self.server_url = "http://127.0.0.1:8000/mcp/"
        self.client = None
        self.setup_page()
    
    def setup_page(self):
        """设置页面配置"""
        st.set_page_config(
            page_title="企业级数据库分析系统",
            page_icon="📊",
            layout="wide"
        )
    
    def create_client(self):
        """创建客户端"""
        try:
            transport = StreamableHttpTransport(url=self.server_url)
            self.client = Client(transport)
            return True
        except Exception as e:
            st.error(f"创建客户端失败: {e}")
            return False
    
    def call_tool_sync(self, tool_name, arguments=None):
        """同步调用工具"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            async def _call():
                async with self.client:
                    result = await self.client.call_tool(tool_name, arguments or {})
                    return result
            
            result = loop.run_until_complete(_call())
            loop.close()
            return result
        except Exception as e:
            st.error(f"调用工具失败: {e}")
            return None
    
    def get_tools_sync(self):
        """同步获取工具列表"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            async def _get():
                async with self.client:
                    tools = await self.client.list_tools()
                    return tools
            
            result = loop.run_until_complete(_get())
            loop.close()
            return result
        except Exception as e:
            st.error(f"获取工具失败: {e}")
            return []
    
    def test_connection_sync(self):
        """同步测试连接"""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            async def _test():
                async with self.client:
                    await self.client.ping()
                    return True
            
            result = loop.run_until_complete(_test())
            loop.close()
            return result
        except Exception as e:
            return False
    
    def render_header(self):
        """渲染页面头部"""
        st.title("📊 企业级数据库分析系统")
        st.markdown("### 🌐 HTTP模式 - 分布式架构")
        st.markdown("---")
        
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            st.info(f"🌐 服务器地址: {self.server_url}")
        
        with col2:
            if st.button("🔄 刷新页面"):
                st.rerun()
        
        with col3:
            if st.button("🔍 测试连接"):
                if self.test_connection_sync():
                    st.success("✅ 连接正常")
                else:
                    st.error("❌ 连接失败")
    
    def render_system_status(self):
        """渲染系统状态"""
        st.subheader("🖥️ 系统状态")
        
        if st.button("📊 获取系统状态"):
            with st.spinner("正在获取系统状态..."):
                result = self.call_tool_sync("get_system_status")
                if result:
                    st.success("✅ 获取成功")
                    st.json(result)
                else:
                    st.error("❌ 获取失败")
    
    def render_data_analysis(self):
        """渲染数据分析"""
        st.subheader("📈 数据分析")
        
        col1, col2 = st.columns(2)
        
        with col1:
            if st.button("📈 温度统计分析"):
                with st.spinner("正在分析..."):
                    result = self.call_tool_sync("advanced_statistical_analysis", {
                        "start_time": "2024-01-01 00:00:00",
                        "end_time": "2024-01-02 00:00:00",
                        "columns": ["temperature"],
                        "operation": "average"
                    })
                    if result:
                        st.success("✅ 分析完成")
                        st.json(result)
        
        with col2:
            if st.button("🚨 异常检测"):
                with st.spinner("正在检测..."):
                    result = self.call_tool_sync("intelligent_anomaly_detection", {
                        "column": "temperature",
                        "method": "hybrid",
                        "time_window": "24h"
                    })
                    if result:
                        st.success("✅ 检测完成")
                        st.json(result)
    
    def render_chart_generation(self):
        """渲染图表生成"""
        st.subheader("📊 图表生成")
        
        if st.button("📊 生成温度趋势图"):
            with st.spinner("正在生成图表..."):
                result = self.call_tool_sync("generate_advanced_chart", {
                    "chart_type": "line",
                    "columns": ["temperature"],
                    "time_range": "24h",
                    "title": "温度趋势图"
                })
                if result:
                    st.success("✅ 图表生成完成")
                    st.json(result)
    
    def render_tools_list(self):
        """渲染工具列表"""
        st.subheader("🛠️ 可用工具")
        
        if st.button("🔍 获取工具列表"):
            with st.spinner("正在获取工具列表..."):
                tools = self.get_tools_sync()
                if tools:
                    st.success(f"✅ 找到 {len(tools)} 个工具")
                    for i, tool in enumerate(tools):
                        with st.expander(f"🔧 {tool.name}"):
                            st.write(f"**描述**: {tool.description}")
                else:
                    st.warning("⚠️ 未找到工具")
    
    def render_sidebar(self):
        """渲染侧边栏"""
        with st.sidebar:
            st.header("🎛️ 控制面板")
            
            st.subheader("🔗 连接信息")
            st.write(f"**服务器**: {self.server_url}")
            st.write(f"**模式**: HTTP分布式")
            
            if st.button("🔍 测试连接", key="sidebar_test"):
                if self.test_connection_sync():
                    st.success("✅ 连接正常")
                else:
                    st.error("❌ 连接失败")
            
            st.divider()
            
            st.subheader("ℹ️ 系统信息")
            st.write("**架构**: 三层分离")
            st.write("**服务器**: HTTP MCP Server")
            st.write("**客户端**: HTTP MCP Client")
            st.write("**前端**: Streamlit Web UI")
            
            st.divider()
            
            st.subheader("📖 使用说明")
            st.write("1. 确保HTTP服务器运行")
            st.write("2. 点击各功能按钮测试")
            st.write("3. 查看返回的JSON结果")
    
    def run(self):
        """运行前端界面"""
        # 创建客户端
        if not self.create_client():
            st.error("❌ 无法创建HTTP MCP客户端")
            st.info("💡 请确保:")
            st.info("1. 第1步的HTTP服务器正在运行")
            st.info("2. 服务器地址正确: http://127.0.0.1:8000/mcp/")
            return
        
        # 渲染界面
        self.render_header()
        self.render_sidebar()
        
        # 主要内容
        tab1, tab2, tab3, tab4 = st.tabs([
            "🖥️ 系统状态", 
            "📈 数据分析", 
            "📊 图表生成", 
            "🛠️ 工具列表"
        ])
        
        with tab1:
            self.render_system_status()
        
        with tab2:
            self.render_data_analysis()
        
        with tab3:
            self.render_chart_generation()
        
        with tab4:
            self.render_tools_list()

def main():
    """主函数"""
    frontend = SimpleFrontend()
    frontend.run()

if __name__ == "__main__":
    main()
