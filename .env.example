# 数据库分析MCP服务器环境配置示例
# ==========================================
# 复制此文件为 .env 并修改相应配置

# ==========================================
# 数据库配置
# ==========================================
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=123456
DB_NAME=sensor_data
DB_CHARSET=utf8mb4

# 数据库连接池配置
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
DB_POOL_TIMEOUT=30
DB_POOL_RECYCLE=3600

# ==========================================
# 异常检测阈值配置
# ==========================================
# 温度传感器阈值
TEMP_MIN=0.0
TEMP_MAX=100.0
TEMP_THRESHOLD=50.0

# 压力传感器阈值
PRESSURE_MIN=90000.0
PRESSURE_MAX=110000.0
PRESSURE_THRESHOLD=105000.0

# 湿度传感器阈值
HUMIDITY_MIN=0.0
HUMIDITY_MAX=100.0
HUMIDITY_THRESHOLD=80.0

# 流量传感器阈值
FLOW_MIN=0.0
FLOW_MAX=500.0
FLOW_THRESHOLD=200.0

# 电力监测阈值
VOLTAGE_MIN=200.0
VOLTAGE_MAX=240.0
CURRENT_MAX=20.0
POWER_MAX=5000.0

# 振动监测阈值
VIBRATION_MAX=10.0
ROTATION_SPEED_MAX=5000.0

# ==========================================
# 语音配置
# ==========================================
# 语音识别语言
VOICE_LANGUAGE=zh-CN

# 语音合成配置
SPEECH_RATE=150
SPEECH_VOLUME=0.9
SPEECH_VOICE_ID=0

# 语音文件存储路径
VOICE_OUTPUT_DIR=./voice_output

# ==========================================
# 数据分析配置
# ==========================================
# 统计分析默认时间窗口
DEFAULT_TIME_WINDOW=24h

# 异常检测敏感度
ANOMALY_SENSITIVITY=2.0

# 趋势分析默认预测天数
DEFAULT_PREDICTION_DAYS=3

# 数据缓存配置
CACHE_ENABLED=true
CACHE_TTL=300
CACHE_MAX_SIZE=1000

# ==========================================
# 可视化配置
# ==========================================
# 图表默认尺寸
CHART_WIDTH=800
CHART_HEIGHT=600

# 图表主题
CHART_THEME=plotly_white

# 图表输出目录
CHART_OUTPUT_DIR=./charts

# 支持的图表格式
CHART_FORMATS=html,png,svg,pdf

# ==========================================
# 日志配置
# ==========================================
# 日志级别: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO

# 日志文件路径
LOG_FILE=./logs/mcp_server.log

# 日志轮转配置
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# 结构化日志格式
LOG_FORMAT=json

# ==========================================
# 性能配置
# ==========================================
# 查询超时时间（秒）
QUERY_TIMEOUT=30

# 最大查询结果行数
MAX_QUERY_ROWS=10000

# 并发查询限制
MAX_CONCURRENT_QUERIES=10

# 数据处理批次大小
BATCH_SIZE=1000

# ==========================================
# 安全配置
# ==========================================
# API密钥（如果启用HTTP传输）
API_KEY=your_api_key_here

# 允许的客户端IP（逗号分隔）
ALLOWED_IPS=127.0.0.1,localhost

# 启用查询白名单
QUERY_WHITELIST_ENABLED=false

# 允许的SQL关键词
ALLOWED_SQL_KEYWORDS=SELECT,FROM,WHERE,GROUP,ORDER,LIMIT,JOIN

# ==========================================
# 提醒系统配置
# ==========================================
# 邮件通知配置
EMAIL_ENABLED=false
EMAIL_SMTP_HOST=smtp.gmail.com
EMAIL_SMTP_PORT=587
EMAIL_USERNAME=<EMAIL>
EMAIL_PASSWORD=your_app_password
EMAIL_FROM=<EMAIL>
EMAIL_TO=<EMAIL>

# 短信通知配置
SMS_ENABLED=false
SMS_API_KEY=your_sms_api_key
SMS_PHONE_NUMBER=+86138xxxxxxxx

# 系统通知配置
SYSTEM_NOTIFICATION_ENABLED=true

# 提醒频率限制（分钟）
ALERT_COOLDOWN=5

# ==========================================
# 扩展功能配置
# ==========================================
# 启用Web界面
WEB_UI_ENABLED=false
WEB_UI_PORT=8080
WEB_UI_HOST=0.0.0.0

# 启用MQTT支持
MQTT_ENABLED=false
MQTT_BROKER=localhost
MQTT_PORT=1883
MQTT_TOPIC_PREFIX=sensor_data

# 启用Redis缓存
REDIS_ENABLED=false
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_DB=0

# 启用Prometheus监控
PROMETHEUS_ENABLED=false
PROMETHEUS_PORT=9090

# ==========================================
# 开发配置
# ==========================================
# 开发模式
DEBUG_MODE=false

# 启用热重载
HOT_RELOAD=false

# 测试数据库
TEST_DB_NAME=sensor_data_test

# 模拟数据生成
GENERATE_MOCK_DATA=false
MOCK_DATA_INTERVAL=60

# ==========================================
# 备份配置
# ==========================================
# 自动备份启用
BACKUP_ENABLED=false

# 备份目录
BACKUP_DIR=./backups

# 备份间隔（小时）
BACKUP_INTERVAL=24

# 保留备份数量
BACKUP_RETENTION=7
