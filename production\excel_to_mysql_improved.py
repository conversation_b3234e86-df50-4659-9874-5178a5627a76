#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进的Excel数据导入MySQL脚本
"""

import pandas as pd
import pymysql
import numpy as np
from datetime import datetime
import logging
import re

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# MySQL连接配置
MYSQL_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '123456',
    'database': 'sensor_data',
    'charset': 'utf8mb4'
}

def clean_column_name(col_name):
    """清理列名，使其符合MySQL命名规范"""
    # 移除换行符和特殊字符
    clean_name = re.sub(r'\n', '', str(col_name))
    clean_name = re.sub(r'[^\w\u4e00-\u9fff]', '_', clean_name)
    
    # 创建英文映射
    column_mapping = {
        '日期': 'datetime',
        '1压力_MPa': 'pressure_1_mpa',
        '温度1_℃': 'temperature_1_c',
        '压力2_MPa': 'pressure_2_mpa', 
        '温度2_℃': 'temperature_2_c',
        '压力3_MPa': 'pressure_3_mpa',
        '温度3_℃': 'temperature_3_c',
        '压力4_MPa': 'pressure_4_mpa',
        '压力5_MPa': 'pressure_5_mpa',
        '温度4_℃': 'temperature_4_c',
        '温度5_℃': 'temperature_5_c',
        '温度6_℃': 'temperature_6_c',
        '含氧_%': 'oxygen_percent',
        '流量_m3_h': 'flow_rate_m3h',
        '负荷1_%': 'load_1_percent',
        '负荷2_%': 'load_2_percent',
        '负荷3_%': 'load_3_percent',
        '压力7_MPa': 'pressure_7_mpa',
        '流量2_m3_h': 'flow_rate_2_m3h'
    }
    
    return column_mapping.get(clean_name, clean_name.lower())

def parse_chinese_datetime(date_str):
    """解析中文日期时间格式"""
    try:
        # 2025年06月24日08时36分48秒
        pattern = r'(\d{4})年(\d{2})月(\d{2})日(\d{2})时(\d{2})分(\d{2})秒'
        match = re.match(pattern, str(date_str))
        if match:
            year, month, day, hour, minute, second = match.groups()
            return datetime(int(year), int(month), int(day), int(hour), int(minute), int(second))
    except Exception as e:
        logger.warning(f"日期解析失败: {date_str}, 错误: {e}")
    return None

def read_and_clean_excel(file_path):
    """读取并清理Excel数据"""
    logger.info(f"正在读取Excel文件: {file_path}")
    
    # 读取Excel文件
    df = pd.read_excel(file_path, sheet_name=0)
    logger.info(f"原始数据形状: {df.shape}")
    
    # 清理列名
    original_columns = df.columns.tolist()
    cleaned_columns = [clean_column_name(col) for col in original_columns]
    
    # 创建列名映射
    column_mapping = dict(zip(original_columns, cleaned_columns))
    df.rename(columns=column_mapping, inplace=True)
    
    logger.info("列名映射:")
    for old, new in column_mapping.items():
        logger.info(f"  '{old}' -> '{new}'")
    
    # 处理日期列
    if 'datetime' in df.columns:
        logger.info("正在解析日期时间...")
        df['datetime'] = df['datetime'].apply(parse_chinese_datetime)
        # 移除解析失败的行
        df = df.dropna(subset=['datetime'])
        logger.info(f"日期解析后数据形状: {df.shape}")
    
    # 处理数值列的精度
    numeric_columns = df.select_dtypes(include=[np.number]).columns
    for col in numeric_columns:
        if col != 'datetime':
            df[col] = df[col].round(2)
    
    logger.info("清理后的数据预览:")
    print(df.head())
    
    return df

def create_mysql_table(df, table_name="sensor_data"):
    """创建MySQL表"""
    logger.info(f"正在创建MySQL表: {table_name}")
    
    # 生成建表SQL
    sql_parts = [f"CREATE TABLE IF NOT EXISTS `{table_name}` ("]
    sql_parts.append("  `id` INT AUTO_INCREMENT PRIMARY KEY,")
    
    for col in df.columns:
        if col == 'datetime':
            mysql_type = "DATETIME"
        elif df[col].dtype in ['int64', 'int32']:
            mysql_type = "INT"
        elif df[col].dtype in ['float64', 'float32']:
            mysql_type = "DECIMAL(10,2)"
        else:
            mysql_type = "VARCHAR(255)"
        
        sql_parts.append(f"  `{col}` {mysql_type},")
    
    sql_parts.append("  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,")
    sql_parts.append("  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")
    sql_parts.append(") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='传感器数据表';")
    
    create_sql = "\n".join(sql_parts)
    
    # 连接数据库并创建表
    try:
        connection = pymysql.connect(**MYSQL_CONFIG)
        with connection.cursor() as cursor:
            # 删除已存在的表（如果需要重新创建）
            cursor.execute(f"DROP TABLE IF EXISTS `{table_name}`")
            logger.info(f"已删除旧表: {table_name}")
            
            # 创建新表
            cursor.execute(create_sql)
            logger.info(f"成功创建表: {table_name}")
            
        connection.commit()
        connection.close()
        
        # 保存SQL到文件
        with open(f"create_{table_name}.sql", "w", encoding="utf-8") as f:
            f.write(create_sql)
        
        return True
        
    except Exception as e:
        logger.error(f"创建表失败: {e}")
        return False

def insert_data_to_mysql(df, table_name="sensor_data"):
    """将数据插入MySQL"""
    logger.info(f"正在插入数据到表: {table_name}")
    
    try:
        connection = pymysql.connect(**MYSQL_CONFIG)
        
        # 准备插入SQL
        columns = list(df.columns)
        placeholders = ', '.join(['%s'] * len(columns))
        insert_sql = f"INSERT INTO `{table_name}` ({', '.join([f'`{col}`' for col in columns])}) VALUES ({placeholders})"
        
        # 批量插入数据
        with connection.cursor() as cursor:
            for index, row in df.iterrows():
                values = []
                for col in columns:
                    value = row[col]
                    if pd.isna(value):
                        values.append(None)
                    elif isinstance(value, datetime):
                        values.append(value)
                    else:
                        values.append(value)
                
                cursor.execute(insert_sql, values)
                
                if (index + 1) % 10 == 0:
                    logger.info(f"已插入 {index + 1} 行数据")
        
        connection.commit()
        connection.close()
        
        logger.info(f"成功插入 {len(df)} 行数据到表 {table_name}")
        return True
        
    except Exception as e:
        logger.error(f"插入数据失败: {e}")
        return False

def main():
    """主函数"""
    excel_file_path = "数据库.xls"
    table_name = "sensor_data"
    
    # 读取并清理Excel数据
    df = read_and_clean_excel(excel_file_path)
    if df is None or df.empty:
        logger.error("无法读取Excel数据")
        return
    
    # 创建MySQL表
    if not create_mysql_table(df, table_name):
        logger.error("创建表失败")
        return
    
    # 插入数据
    if insert_data_to_mysql(df, table_name):
        logger.info("数据导入完成！")
        
        # 验证数据
        try:
            connection = pymysql.connect(**MYSQL_CONFIG)
            with connection.cursor() as cursor:
                cursor.execute(f"SELECT COUNT(*) FROM `{table_name}`")
                count = cursor.fetchone()[0]
                logger.info(f"数据库中共有 {count} 条记录")
                
                cursor.execute(f"SELECT * FROM `{table_name}` LIMIT 3")
                sample_data = cursor.fetchall()
                logger.info("数据库中的示例数据:")
                for row in sample_data:
                    logger.info(f"  {row}")
            
            connection.close()
            
        except Exception as e:
            logger.error(f"验证数据失败: {e}")
    
    else:
        logger.error("数据插入失败")

if __name__ == "__main__":
    main()
