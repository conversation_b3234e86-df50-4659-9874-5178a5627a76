#!/usr/bin/env python3
"""
企业级数据库分析 - Streamlit前端
基于Streamlit构建的专业数据分析仪表板

功能特性：
1. 实时数据监控仪表板
2. 交互式统计分析
3. 智能异常检测
4. 高级数据可视化
5. 趋势分析与预测
6. 提醒规则管理
7. 系统状态监控
8. 数据导出功能
"""

import streamlit as st
import pandas as pd
import numpy as np
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import requests
import json
from datetime import datetime, timedelta
import time
import os
import io
from typing import Dict, List, Any

# 页面配置
st.set_page_config(
    page_title="企业级数据库分析系统",
    page_icon="📊",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 全局配置
API_BASE_URL = os.getenv("API_BASE_URL", "http://localhost:8000")
REFRESH_INTERVAL = int(os.getenv("REFRESH_INTERVAL", "30"))  # 秒

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .metric-card {
        background-color: #f0f2f6;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #1f77b4;
    }
    .alert-high {
        background-color: #ffebee;
        border-left: 4px solid #f44336;
        padding: 1rem;
        border-radius: 0.5rem;
    }
    .alert-medium {
        background-color: #fff3e0;
        border-left: 4px solid #ff9800;
        padding: 1rem;
        border-radius: 0.5rem;
    }
    .alert-low {
        background-color: #e8f5e8;
        border-left: 4px solid #4caf50;
        padding: 1rem;
        border-radius: 0.5rem;
    }
</style>
""", unsafe_allow_html=True)

# API调用函数
def call_api(endpoint: str, method: str = "GET", data: dict = None) -> dict:
    """调用后端API"""
    url = f"{API_BASE_URL}/api/{endpoint}"
    
    try:
        if method == "GET":
            response = requests.get(url, timeout=30)
        elif method == "POST":
            response = requests.post(url, json=data, timeout=30)
        
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        st.error(f"API调用失败: {str(e)}")
        return {}

def check_api_health() -> bool:
    """检查API服务状态"""
    try:
        response = requests.get(f"{API_BASE_URL}/api/health", timeout=5)
        return response.status_code == 200
    except:
        return False

# 侧边栏导航
def render_sidebar():
    """渲染侧边栏"""
    st.sidebar.title("📊 数据分析系统")
    
    # API状态检查
    if check_api_health():
        st.sidebar.success("🟢 API服务正常")
    else:
        st.sidebar.error("🔴 API服务异常")
        st.sidebar.info(f"请确保后端服务运行在: {API_BASE_URL}")
    
    # 页面选择
    pages = {
        "🏠 实时监控": "dashboard",
        "📈 统计分析": "statistics", 
        "🚨 异常检测": "anomaly",
        "📊 数据可视化": "visualization",
        "📉 趋势分析": "trend",
        "⚠️ 提醒管理": "alerts",
        "⚙️ 系统管理": "system"
    }
    
    selected_page = st.sidebar.selectbox(
        "选择功能页面",
        list(pages.keys()),
        index=0
    )
    
    # 自动刷新设置
    st.sidebar.subheader("⚙️ 显示设置")
    auto_refresh = st.sidebar.checkbox("自动刷新", value=True)
    refresh_interval = st.sidebar.slider("刷新间隔(秒)", 10, 300, REFRESH_INTERVAL)
    
    # 时间范围选择
    st.sidebar.subheader("⏰ 时间范围")
    time_ranges = {
        "过去1小时": "1h",
        "过去6小时": "6h", 
        "过去24小时": "24h",
        "过去3天": "3d",
        "过去7天": "7d",
        "过去30天": "30d"
    }
    selected_time_range = st.sidebar.selectbox(
        "选择时间范围",
        list(time_ranges.keys()),
        index=2  # 默认24小时
    )
    
    return pages[selected_page], auto_refresh, refresh_interval, time_ranges[selected_time_range]

# 主页面 - 实时监控仪表板
def render_dashboard_page(time_range: str):
    """渲染实时监控仪表板"""
    st.markdown('<h1 class="main-header">🏠 实时监控仪表板</h1>', unsafe_allow_html=True)
    
    # 获取系统状态
    system_status = call_api("system/status")
    
    if system_status:
        # 系统状态指标
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                label="📊 数据总量",
                value=f"{system_status.get('total_data_rows', 0):,}",
                delta="实时更新"
            )
        
        with col2:
            st.metric(
                label="🔗 连接池",
                value=f"{system_status.get('connection_pool_size', 0)}",
                delta="连接数"
            )
        
        with col3:
            st.metric(
                label="⚠️ 活跃规则",
                value=f"{system_status.get('active_alert_rules', 0)}",
                delta=f"总计{system_status.get('total_alert_rules', 0)}"
            )
        
        with col4:
            st.metric(
                label="💾 缓存条目",
                value=f"{system_status.get('cache_entries', 0)}",
                delta="已缓存"
            )
    
    # 获取最新数据
    latest_data = call_api("data/latest?limit=50")
    
    if latest_data:
        df = pd.DataFrame(latest_data)
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        
        # 实时数据表格
        st.subheader("📋 最新数据")
        
        # 数据过滤
        col1, col2 = st.columns(2)
        with col1:
            device_filter = st.selectbox(
                "设备筛选",
                ["全部"] + list(df['device_id'].unique()) if 'device_id' in df.columns else ["全部"]
            )
        
        with col2:
            status_filter = st.selectbox(
                "状态筛选", 
                ["全部"] + list(df['status'].unique()) if 'status' in df.columns else ["全部"]
            )
        
        # 应用过滤
        filtered_df = df.copy()
        if device_filter != "全部":
            filtered_df = filtered_df[filtered_df['device_id'] == device_filter]
        if status_filter != "全部":
            filtered_df = filtered_df[filtered_df['status'] == status_filter]
        
        # 显示数据表
        st.dataframe(
            filtered_df.head(20),
            use_container_width=True,
            hide_index=True
        )
        
        # 实时图表
        st.subheader("📈 实时趋势")
        
        # 选择要显示的指标
        numeric_columns = [col for col in df.columns if df[col].dtype in ['float64', 'int64']]
        selected_metrics = st.multiselect(
            "选择显示指标",
            numeric_columns,
            default=numeric_columns[:3] if len(numeric_columns) >= 3 else numeric_columns
        )
        
        if selected_metrics:
            # 创建实时趋势图
            fig = go.Figure()
            
            for metric in selected_metrics:
                fig.add_trace(go.Scatter(
                    x=filtered_df['timestamp'],
                    y=filtered_df[metric],
                    mode='lines+markers',
                    name=metric,
                    line=dict(width=2),
                    marker=dict(size=4)
                ))
            
            fig.update_layout(
                title="实时数据趋势",
                xaxis_title="时间",
                yaxis_title="数值",
                hovermode='x unified',
                height=400
            )
            
            st.plotly_chart(fig, use_container_width=True)
    
    # 检查警报
    alerts = call_api("alerts/check")
    
    if alerts:
        st.subheader("🚨 当前警报")
        
        if alerts:
            for alert in alerts:
                severity = alert.get('severity', 'low')
                css_class = f"alert-{severity}"
                
                st.markdown(f"""
                <div class="{css_class}">
                    <strong>🚨 {alert.get('rule_name', '未知规则')}</strong><br>
                    {alert.get('message', '无消息')}<br>
                    <small>时间: {alert.get('timestamp', '未知')} | 严重级别: {severity.upper()}</small>
                </div>
                """, unsafe_allow_html=True)
        else:
            st.success("✅ 当前无警报")

# 统计分析页面
def render_statistics_page(time_range: str):
    """渲染统计分析页面"""
    st.markdown('<h1 class="main-header">📈 统计分析</h1>', unsafe_allow_html=True)
    
    # 参数设置
    col1, col2 = st.columns(2)
    
    with col1:
        # 时间范围设置
        st.subheader("⏰ 时间设置")
        
        use_custom_time = st.checkbox("自定义时间范围")
        
        if use_custom_time:
            start_date = st.date_input("开始日期", value=datetime.now().date() - timedelta(days=1))
            start_time = st.time_input("开始时间", value=datetime.now().time())
            end_date = st.date_input("结束日期", value=datetime.now().date())
            end_time = st.time_input("结束时间", value=datetime.now().time())
            
            start_datetime = datetime.combine(start_date, start_time).strftime('%Y-%m-%d %H:%M:%S')
            end_datetime = datetime.combine(end_date, end_time).strftime('%Y-%m-%d %H:%M:%S')
        else:
            end_datetime = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            if time_range.endswith('h'):
                hours = int(time_range[:-1])
                start_datetime = (datetime.now() - timedelta(hours=hours)).strftime('%Y-%m-%d %H:%M:%S')
            elif time_range.endswith('d'):
                days = int(time_range[:-1])
                start_datetime = (datetime.now() - timedelta(days=days)).strftime('%Y-%m-%d %H:%M:%S')
    
    with col2:
        # 分析参数设置
        st.subheader("📊 分析参数")
        
        # 选择分析列
        available_columns = ["temperature", "pressure", "humidity", "flow_rate", "voltage", "current", "power"]
        selected_columns = st.multiselect(
            "选择分析列",
            available_columns,
            default=["temperature", "pressure"]
        )
        
        # 选择统计操作
        operations = ["sum", "average", "count", "min", "max", "std", "median", "percentile"]
        selected_operation = st.selectbox("统计操作", operations, index=1)
        
        # 百分位数设置（仅当选择percentile时）
        percentile_value = 95
        if selected_operation == "percentile":
            percentile_value = st.slider("百分位数", 1, 99, 95)
    
    # 执行分析按钮
    if st.button("🚀 执行统计分析", type="primary"):
        if selected_columns:
            with st.spinner("正在执行统计分析..."):
                # 调用API
                data = {
                    "start_time": start_datetime,
                    "end_time": end_datetime,
                    "columns": selected_columns,
                    "operation": selected_operation,
                    "percentile_value": percentile_value
                }
                
                results = call_api("analysis/statistical", method="POST", data=data)
                
                if results:
                    st.success("✅ 统计分析完成")
                    
                    # 显示结果
                    st.subheader("📊 分析结果")
                    
                    # 创建结果表格
                    result_data = []
                    for result in results:
                        result_data.append({
                            "列名": result['column'],
                            "操作": result['operation'],
                            "结果": f"{result['result']:.4f}",
                            "数据点数": result['count'],
                            "处理时间(秒)": f"{result['processing_time']:.3f}"
                        })
                    
                    result_df = pd.DataFrame(result_data)
                    st.dataframe(result_df, use_container_width=True, hide_index=True)
                    
                    # 可视化结果
                    if len(results) > 1:
                        st.subheader("📈 结果可视化")
                        
                        # 创建柱状图
                        fig = go.Figure(data=[
                            go.Bar(
                                x=[r['column'] for r in results],
                                y=[r['result'] for r in results],
                                text=[f"{r['result']:.2f}" for r in results],
                                textposition='auto'
                            )
                        ])
                        
                        fig.update_layout(
                            title=f"{selected_operation.upper()} 分析结果对比",
                            xaxis_title="数据列",
                            yaxis_title="统计值",
                            height=400
                        )
                        
                        st.plotly_chart(fig, use_container_width=True)
                else:
                    st.error("❌ 统计分析失败")
        else:
            st.warning("⚠️ 请选择至少一个分析列")

# 异常检测页面
def render_anomaly_page(time_range: str):
    """渲染异常检测页面"""
    st.markdown('<h1 class="main-header">🚨 异常检测</h1>', unsafe_allow_html=True)

    col1, col2 = st.columns(2)

    with col1:
        st.subheader("🔧 检测参数")

        # 选择检测列
        available_columns = ["temperature", "pressure", "humidity", "flow_rate", "voltage", "current", "power"]
        selected_column = st.selectbox("选择检测列", available_columns)

        # 检测方法
        methods = {
            "statistical": "统计学方法",
            "isolation_forest": "孤立森林",
            "zscore": "Z-Score标准化",
            "iqr": "四分位距",
            "hybrid": "混合方法"
        }
        selected_method = st.selectbox("检测方法", list(methods.keys()),
                                     format_func=lambda x: methods[x], index=4)

        # 敏感度设置
        sensitivity = st.slider("敏感度", 1.0, 5.0, 2.0, 0.1)

        # 污染率（仅孤立森林）
        contamination = 0.1
        if selected_method == "isolation_forest":
            contamination = st.slider("异常比例估计", 0.01, 0.5, 0.1, 0.01)

    with col2:
        st.subheader("⚙️ 高级设置")

        # 时间窗口
        time_windows = ["1h", "6h", "24h", "3d", "7d"]
        selected_time_window = st.selectbox("时间窗口", time_windows, index=2)

        # 是否包含原因分析
        include_reasons = st.checkbox("包含原因分析", value=True)

        # 显示设置
        show_details = st.checkbox("显示详细信息", value=True)

    # 执行检测
    if st.button("🔍 执行异常检测", type="primary"):
        with st.spinner("正在执行异常检测..."):
            data = {
                "column": selected_column,
                "method": selected_method,
                "time_window": selected_time_window,
                "sensitivity": sensitivity,
                "contamination": contamination,
                "include_reasons": include_reasons
            }

            result = call_api("analysis/anomaly", method="POST", data=data)

            if result:
                st.success("✅ 异常检测完成")

                # 显示检测结果摘要
                col1, col2, col3, col4 = st.columns(4)

                with col1:
                    st.metric("总数据点", result['total_points'])

                with col2:
                    st.metric("异常点数", result['anomaly_count'])

                with col3:
                    st.metric("异常率", f"{result['anomaly_rate']:.2%}")

                with col4:
                    st.metric("置信度", f"{result['confidence_score']:.2%}")

                # 异常原因分析
                if result.get('possible_causes'):
                    st.subheader("🔍 可能原因分析")
                    for i, cause in enumerate(result['possible_causes'], 1):
                        st.write(f"{i}. {cause}")

                # 异常点详情
                if show_details and result.get('anomalies'):
                    st.subheader("📋 异常点详情")

                    anomaly_data = []
                    for anomaly in result['anomalies'][:20]:  # 只显示前20个
                        anomaly_data.append({
                            "时间": anomaly['timestamp'],
                            "异常值": f"{anomaly['value']:.4f}",
                            "偏差": f"{anomaly['deviation']:.4f}",
                            "Z-Score": f"{anomaly['z_score']:.4f}",
                            "设备ID": anomaly.get('device_id', 'N/A'),
                            "位置": anomaly.get('location', 'N/A')
                        })

                    anomaly_df = pd.DataFrame(anomaly_data)
                    st.dataframe(anomaly_df, use_container_width=True, hide_index=True)
            else:
                st.error("❌ 异常检测失败")

# 数据可视化页面
def render_visualization_page(time_range: str):
    """渲染数据可视化页面"""
    st.markdown('<h1 class="main-header">📊 数据可视化</h1>', unsafe_allow_html=True)

    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📈 图表设置")

        # 图表类型
        chart_types = {
            "line": "折线图",
            "bar": "柱状图",
            "pie": "饼状图",
            "scatter": "散点图",
            "heatmap": "热力图",
            "histogram": "直方图",
            "box": "箱线图"
        }
        selected_chart_type = st.selectbox("图表类型", list(chart_types.keys()),
                                         format_func=lambda x: chart_types[x])

        # 选择数据列
        available_columns = ["temperature", "pressure", "humidity", "flow_rate", "voltage", "current", "power"]
        selected_columns = st.multiselect("选择数据列", available_columns,
                                        default=["temperature", "pressure"])

        # 图表标题
        chart_title = st.text_input("图表标题", value="数据可视化图表")

    with col2:
        st.subheader("⚙️ 高级选项")

        # 数据聚合
        aggregations = {
            "raw": "原始数据",
            "hourly": "按小时聚合",
            "daily": "按天聚合"
        }
        selected_aggregation = st.selectbox("数据聚合", list(aggregations.keys()),
                                          format_func=lambda x: aggregations[x])

        # 分组字段
        group_by = st.selectbox("分组字段", ["无", "device_id", "location", "status"], index=0)
        if group_by == "无":
            group_by = None

        # 交互式图表
        interactive = st.checkbox("交互式图表", value=True)

    # 生成图表
    if st.button("🎨 生成图表", type="primary"):
        if selected_columns:
            with st.spinner("正在生成图表..."):
                data = {
                    "chart_type": selected_chart_type,
                    "columns": selected_columns,
                    "time_range": time_range,
                    "title": chart_title,
                    "group_by": group_by,
                    "aggregation": selected_aggregation,
                    "interactive": interactive
                }

                result = call_api("visualization/chart", method="POST", data=data)

                if result and result.get('chart_html'):
                    st.success("✅ 图表生成完成")

                    # 显示图表
                    st.subheader("📊 生成的图表")
                    st.components.v1.html(result['chart_html'], height=600)

                    # 下载选项
                    st.download_button(
                        label="💾 下载图表HTML",
                        data=result['chart_html'],
                        file_name=f"chart_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html",
                        mime="text/html"
                    )
                else:
                    st.error("❌ 图表生成失败")
        else:
            st.warning("⚠️ 请选择至少一个数据列")

# 趋势分析页面
def render_trend_page(time_range: str):
    """渲染趋势分析页面"""
    st.markdown('<h1 class="main-header">📉 趋势分析</h1>', unsafe_allow_html=True)

    col1, col2 = st.columns(2)

    with col1:
        st.subheader("📊 分析参数")

        # 选择分析列
        available_columns = ["temperature", "pressure", "humidity", "flow_rate", "voltage", "current", "power"]
        selected_columns = st.multiselect("选择分析列", available_columns,
                                        default=["temperature"])

        # 预测方法
        methods = {
            "linear": "线性回归",
            "polynomial": "多项式回归",
            "arima": "ARIMA时间序列",
            "lstm": "LSTM神经网络",
            "ensemble": "集成方法"
        }
        selected_method = st.selectbox("预测方法", list(methods.keys()),
                                     format_func=lambda x: methods[x], index=4)

        # 历史数据范围
        time_ranges = ["3d", "7d", "14d", "30d"]
        historical_range = st.selectbox("历史数据范围", time_ranges, index=1)

    with col2:
        st.subheader("🔮 预测设置")

        # 预测时长
        prediction_hours = st.slider("预测小时数", 1, 168, 24)  # 最多一周

        # 置信区间
        confidence_interval = st.slider("置信区间", 0.8, 0.99, 0.95, 0.01)

        # 季节性分析
        include_seasonality = st.checkbox("包含季节性分析", value=True)

    # 执行趋势分析
    if st.button("📈 执行趋势分析", type="primary"):
        if selected_columns:
            with st.spinner("正在执行趋势分析..."):
                data = {
                    "columns": selected_columns,
                    "time_range": historical_range,
                    "prediction_hours": prediction_hours,
                    "method": selected_method,
                    "confidence_interval": confidence_interval,
                    "include_seasonality": include_seasonality
                }

                results = call_api("analysis/trend", method="POST", data=data)

                if results:
                    st.success("✅ 趋势分析完成")

                    # 显示每列的分析结果
                    for column, result in results.items():
                        if 'error' not in result:
                            st.subheader(f"📊 {column} 趋势分析")

                            # 关键指标
                            col1, col2, col3, col4 = st.columns(4)

                            with col1:
                                st.metric("当前值", f"{result['current_value']:.4f}")

                            with col2:
                                st.metric("预测值", f"{result['predicted_value']:.4f}")

                            with col3:
                                st.metric("变化率", f"{result['change_rate']:.2f}%")

                            with col4:
                                st.metric("模型准确度", f"{result['model_accuracy']:.2%}")

                            # 趋势信息
                            st.info(f"🔍 趋势方向: {result['trend_direction']} | "
                                   f"趋势强度: {result['trend_strength']:.2f} | "
                                   f"置信度: {result['confidence']}")

                            # 预测图表
                            if result.get('predictions') and result.get('prediction_timestamps'):
                                fig = go.Figure()

                                # 预测数据
                                fig.add_trace(go.Scatter(
                                    x=result['prediction_timestamps'],
                                    y=result['predictions'],
                                    mode='lines+markers',
                                    name=f'{column} 预测',
                                    line=dict(dash='dash', color='red')
                                ))

                                fig.update_layout(
                                    title=f"{column} 趋势预测",
                                    xaxis_title="时间",
                                    yaxis_title="数值",
                                    height=400
                                )

                                st.plotly_chart(fig, use_container_width=True)
                else:
                    st.error("❌ 趋势分析失败")
        else:
            st.warning("⚠️ 请选择至少一个分析列")

# 提醒管理页面
def render_alerts_page():
    """渲染提醒管理页面"""
    st.markdown('<h1 class="main-header">⚠️ 提醒管理</h1>', unsafe_allow_html=True)

    # 获取现有规则
    existing_rules = call_api("alerts/rules")

    # 创建新规则
    st.subheader("➕ 创建新提醒规则")

    col1, col2 = st.columns(2)

    with col1:
        rule_id = st.text_input("规则ID", value=f"rule_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
        rule_name = st.text_input("规则名称", value="新提醒规则")

        # 监控列
        available_columns = ["temperature", "pressure", "humidity", "flow_rate", "voltage", "current", "power"]
        selected_column = st.selectbox("监控列", available_columns)

        # 条件类型
        conditions = {
            "greater_than": "大于阈值",
            "less_than": "小于阈值",
            "range": "超出范围",
            "change_rate": "变化率超限"
        }
        selected_condition = st.selectbox("触发条件", list(conditions.keys()),
                                        format_func=lambda x: conditions[x])

    with col2:
        # 阈值设置
        threshold = st.number_input("主要阈值", value=50.0)

        threshold2 = None
        if selected_condition == "range":
            threshold2 = st.number_input("第二阈值", value=100.0)

        # 严重级别
        severities = ["low", "medium", "high", "critical"]
        selected_severity = st.selectbox("严重级别", severities, index=1)

        # 冷却时间
        cooldown_minutes = st.slider("冷却时间(分钟)", 1, 60, 5)

        # 通知方式
        notification_methods = st.multiselect("通知方式",
                                            ["system", "email", "sms", "voice"],
                                            default=["system"])

    # 创建规则按钮
    if st.button("✅ 创建规则", type="primary"):
        data = {
            "rule_id": rule_id,
            "name": rule_name,
            "column": selected_column,
            "condition": selected_condition,
            "threshold": threshold,
            "threshold2": threshold2,
            "severity": selected_severity,
            "notification_methods": notification_methods,
            "cooldown_minutes": cooldown_minutes,
            "enabled": True
        }

        result = call_api("alerts/create", method="POST", data=data)

        if result:
            st.success(f"✅ 规则创建成功: {rule_name}")
            st.rerun()
        else:
            st.error("❌ 规则创建失败")

    # 显示现有规则
    st.subheader("📋 现有提醒规则")

    if existing_rules:
        for rule_id, rule in existing_rules.items():
            with st.expander(f"📌 {rule['name']} ({rule_id})"):
                col1, col2, col3 = st.columns(3)

                with col1:
                    st.write(f"**监控列**: {rule['column']}")
                    st.write(f"**条件**: {conditions.get(rule['condition'], rule['condition'])}")
                    st.write(f"**阈值**: {rule['threshold']}")
                    if rule.get('threshold2'):
                        st.write(f"**第二阈值**: {rule['threshold2']}")

                with col2:
                    st.write(f"**严重级别**: {rule['severity'].upper()}")
                    st.write(f"**状态**: {'🟢 启用' if rule['enabled'] else '🔴 禁用'}")
                    st.write(f"**冷却时间**: {rule['cooldown_minutes']}分钟")

                with col3:
                    st.write(f"**通知方式**: {', '.join(rule['notification_methods'])}")
                    if rule.get('last_triggered'):
                        st.write(f"**最后触发**: {rule['last_triggered']}")
                    else:
                        st.write("**最后触发**: 从未触发")
    else:
        st.info("📝 暂无提醒规则，请创建新规则")

    # 当前警报
    st.subheader("🚨 当前警报")

    current_alerts = call_api("alerts/check")

    if current_alerts:
        for alert in current_alerts:
            severity_colors = {
                "low": "🟢",
                "medium": "🟡",
                "high": "🟠",
                "critical": "🔴"
            }

            severity_icon = severity_colors.get(alert['severity'], "⚪")

            st.warning(f"""
            {severity_icon} **{alert['rule_name']}**

            {alert['message']}

            **时间**: {alert['timestamp']}
            **设备**: {alert.get('device_id', 'N/A')}
            **位置**: {alert.get('location', 'N/A')}
            """)
    else:
        st.success("✅ 当前无警报")

# 系统管理页面
def render_system_page():
    """渲染系统管理页面"""
    st.markdown('<h1 class="main-header">⚙️ 系统管理</h1>', unsafe_allow_html=True)

    # 系统状态
    st.subheader("📊 系统状态")

    system_status = call_api("system/status")

    if system_status:
        col1, col2 = st.columns(2)

        with col1:
            st.json({
                "数据库状态": system_status.get('database_status', 'unknown'),
                "数据总量": system_status.get('total_data_rows', 0),
                "最新数据时间": system_status.get('latest_data_time', 'unknown'),
                "连接池大小": system_status.get('connection_pool_size', 0)
            })

        with col2:
            st.json({
                "缓存条目": system_status.get('cache_entries', 0),
                "活跃规则": system_status.get('active_alert_rules', 0),
                "总规则数": system_status.get('total_alert_rules', 0),
                "系统时间": system_status.get('system_time', 'unknown')
            })

        # 性能指标
        if system_status.get('performance'):
            st.subheader("⚡ 性能指标")
            perf = system_status['performance']

            col1, col2, col3 = st.columns(3)

            with col1:
                st.metric("平均查询时间", perf.get('avg_query_time', 'N/A'))

            with col2:
                st.metric("内存使用", perf.get('memory_usage', 'N/A'))

            with col3:
                st.metric("CPU使用", perf.get('cpu_usage', 'N/A'))

    # 数据库优化
    st.subheader("🔧 数据库优化")

    st.info("数据库优化可以提高查询性能，建议定期执行")

    if st.button("🚀 执行数据库优化", type="primary"):
        with st.spinner("正在优化数据库..."):
            result = call_api("database/optimize", method="POST")

            if result:
                st.success("✅ 数据库优化完成")
                st.text(result.get('message', '优化成功'))
            else:
                st.error("❌ 数据库优化失败")

    # API配置
    st.subheader("🌐 API配置")

    col1, col2 = st.columns(2)

    with col1:
        st.code(f"API地址: {API_BASE_URL}")
        st.code(f"刷新间隔: {REFRESH_INTERVAL}秒")

    with col2:
        # API健康检查
        if st.button("🔍 检查API健康状态"):
            if check_api_health():
                st.success("🟢 API服务正常")
            else:
                st.error("🔴 API服务异常")

    # 系统日志
    st.subheader("📋 系统信息")

    st.info(f"""
    **前端框架**: Streamlit {st.__version__}
    **API基础地址**: {API_BASE_URL}
    **自动刷新间隔**: {REFRESH_INTERVAL}秒
    **当前时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
    """)

    # 导出功能
    st.subheader("💾 数据导出")

    col1, col2 = st.columns(2)

    with col1:
        export_limit = st.number_input("导出数据量", min_value=100, max_value=10000, value=1000)

    with col2:
        export_format = st.selectbox("导出格式", ["CSV", "JSON", "Excel"])

    if st.button("📥 导出数据"):
        latest_data = call_api(f"data/latest?limit={export_limit}")

        if latest_data:
            df = pd.DataFrame(latest_data)

            if export_format == "CSV":
                csv = df.to_csv(index=False)
                st.download_button(
                    label="💾 下载CSV文件",
                    data=csv,
                    file_name=f"sensor_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )
            elif export_format == "JSON":
                json_data = df.to_json(orient='records', date_format='iso')
                st.download_button(
                    label="💾 下载JSON文件",
                    data=json_data,
                    file_name=f"sensor_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json",
                    mime="application/json"
                )
            elif export_format == "Excel":
                # 注意：需要安装openpyxl
                try:
                    excel_buffer = io.BytesIO()
                    df.to_excel(excel_buffer, index=False)
                    excel_data = excel_buffer.getvalue()

                    st.download_button(
                        label="💾 下载Excel文件",
                        data=excel_data,
                        file_name=f"sensor_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xlsx",
                        mime="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
                    )
                except ImportError:
                    st.error("❌ Excel导出需要安装openpyxl: pip install openpyxl")
        else:
            st.error("❌ 数据导出失败")

# 主函数
def main():
    """主函数"""
    # 渲染侧边栏
    page, auto_refresh, refresh_interval, time_range = render_sidebar()
    
    # 根据选择的页面渲染内容
    if page == "dashboard":
        render_dashboard_page(time_range)
    elif page == "statistics":
        render_statistics_page(time_range)
    elif page == "anomaly":
        render_anomaly_page(time_range)
    elif page == "visualization":
        render_visualization_page(time_range)
    elif page == "trend":
        render_trend_page(time_range)
    elif page == "alerts":
        render_alerts_page()
    elif page == "system":
        render_system_page()
    
    # 自动刷新
    if auto_refresh and page == "dashboard":
        time.sleep(refresh_interval)
        st.rerun()

if __name__ == "__main__":
    main()
