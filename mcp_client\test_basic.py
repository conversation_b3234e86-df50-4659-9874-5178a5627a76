#!/usr/bin/env python3
"""
基础测试脚本 - 验证MCP客户端基本功能
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

def test_imports():
    """测试导入"""
    print("🔍 测试导入...")
    
    try:
        from fastmcp import Client
        print("✅ fastmcp.Client 导入成功")
    except Exception as e:
        print(f"❌ fastmcp.Client 导入失败: {e}")
        return False
    
    try:
        from fastmcp.client.transports import StdioTransport
        print("✅ StdioTransport 导入成功")
    except Exception as e:
        print(f"❌ StdioTransport 导入失败: {e}")
        return False
    
    try:
        from config import config
        print("✅ config 导入成功")
    except Exception as e:
        print(f"❌ config 导入失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置"""
    print("\n🔧 测试配置...")
    
    try:
        from config import config
        
        # 检查服务器配置
        server_config = config.get_server_config("enterprise_db")
        if server_config:
            print(f"✅ 服务器配置存在: {server_config.name}")
            print(f"   服务器路径: {server_config.server_path}")
            
            # 检查服务器文件是否存在
            server_path = Path(server_config.server_path)
            if server_path.exists():
                print("✅ 服务器文件存在")
            else:
                print(f"❌ 服务器文件不存在: {server_path}")
                return False
        else:
            print("❌ 服务器配置不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

async def test_client_creation():
    """测试客户端创建"""
    print("\n🤖 测试客户端创建...")
    
    try:
        from client import MCPClient
        
        # 创建客户端
        client = MCPClient()
        print("✅ 客户端创建成功")
        
        # 测试配置
        if client.server_config:
            print(f"✅ 客户端配置正确: {client.server_config.name}")
        else:
            print("❌ 客户端配置失败")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 客户端创建失败: {e}")
        return False

async def test_connection():
    """测试连接"""
    print("\n🔗 测试连接...")
    
    try:
        from client import MCPClient
        
        client = MCPClient()
        
        # 尝试连接
        print("正在连接到MCP服务器...")
        success = await client.connect()
        
        if success:
            print("✅ 连接成功!")
            
            # 测试基本功能
            try:
                tools = await client.get_available_tools()
                print(f"✅ 获取工具列表成功，共 {len(tools)} 个工具")
                
                for name in list(tools.keys())[:3]:  # 显示前3个工具
                    print(f"   • {name}")
                
            except Exception as e:
                print(f"⚠️ 获取工具列表失败: {e}")
            
            # 断开连接
            await client.disconnect()
            print("✅ 断开连接成功")
            
            return True
        else:
            print("❌ 连接失败")
            return False
            
    except Exception as e:
        print(f"❌ 连接测试失败: {e}")
        return False

async def main():
    """主测试函数"""
    print("🚀 MCP客户端基础测试")
    print("=" * 50)
    
    # 测试步骤
    tests = [
        ("导入测试", test_imports),
        ("配置测试", test_config),
        ("客户端创建测试", test_client_creation),
        ("连接测试", test_connection)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
                
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    # 总结
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！MCP客户端可以正常使用")
        print("\n🚀 您现在可以运行:")
        print("   python run.py cli      # 命令行界面")
        print("   python run.py chat     # 聊天界面")
        print("   python run.py dashboard # 仪表板界面")
    else:
        print("⚠️ 部分测试失败，请检查配置和依赖")
        
        # 给出修复建议
        print("\n🔧 修复建议:")
        print("1. 确保安装了所有依赖: pip install -r requirements.txt")
        print("2. 检查服务器文件路径是否正确")
        print("3. 确保数据库配置正确")

if __name__ == "__main__":
    asyncio.run(main())
