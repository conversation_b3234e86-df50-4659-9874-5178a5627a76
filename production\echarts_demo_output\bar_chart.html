
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
        <style>
            body { margin: 0; padding: 10px; font-family: Arial, sans-serif; }
            #chart { width: 100%; height: 600px; }
        </style>
    </head>
    <body>
        <div id="chart"></div>
        <script>
            var chartDom = document.getElementById('chart');
            var myChart = echarts.init(chartDom);
            var option = {
  "title": {
    "text": "传感器数据柱状图",
    "left": "center",
    "textStyle": {
      "fontSize": 16,
      "fontWeight": "bold"
    }
  },
  "tooltip": {
    "trigger": "axis",
    "axisPointer": {
      "type": "shadow"
    }
  },
  "legend": {
    "top": "bottom",
    "data": [
      "temperature",
      "humidity",
      "pressure"
    ]
  },
  "grid": {
    "left": "3%",
    "right": "4%",
    "bottom": "15%",
    "containLabel": true
  },
  "color": [
    "#5470c6",
    "#91cc75",
    "#fac858",
    "#ee6666",
    "#73c0de",
    "#3ba272",
    "#fc8452",
    "#9a60b4",
    "#ea7ccc",
    "#ff9f7f"
  ],
  "xAxis": {
    "type": "category",
    "data": [
      "temperature",
      "humidity",
      "pressure"
    ]
  },
  "yAxis": {
    "type": "value"
  },
  "series": [
    {
      "name": "平均值",
      "type": "bar",
      "data": [
        21.95714640191695,
        51.316915893279756,
        1014.9002945832847
      ],
      "itemStyle": {
        "borderRadius": [
          4,
          4,
          0,
          0
        ]
      }
    }
  ]
};
            
            myChart.setOption(option);
            
            // 响应式调整
            window.addEventListener('resize', function() {
                myChart.resize();
            });
            
            // 错误处理
            myChart.on('error', function(params) {
                console.error('ECharts 渲染错误:', params);
            });
        </script>
    </body>
    </html>
    