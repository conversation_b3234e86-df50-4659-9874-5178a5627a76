@echo off
echo 🔧 启动调试版前端
echo =====================================
echo.
echo 📋 启动信息:
echo   - 调试前端: http://localhost:8503
echo   - MCP服务器: http://127.0.0.1:8000/mcp/
echo   - 版本: 调试版 v1.0
echo.
echo 🔧 请确保MCP服务器已启动！
echo    如果未启动，请在另一个终端运行:
echo    python mcp_client/simple_http_server.py
echo.
echo ⏰ 正在启动调试前端...
echo.

streamlit run mcp_client/simple_debug.py --server.port=8503

echo.
echo 👋 调试前端已关闭
pause
