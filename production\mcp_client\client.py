#!/usr/bin/env python3
"""
企业级数据库分析MCP客户端
基于FastMCP的本地化客户端，支持智能对话和数据分析
"""

import asyncio
import sys
from pathlib import Path
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from fastmcp import Client
from fastmcp.client.transports import StdioTransport
from fastmcp.exceptions import ToolError

from .config import config, DEFAULT_SERVER
from .utils.logger import log
from .handlers.tool_handler import ToolHandler
from .handlers.data_handler import DataHandler
from .handlers.response_handler import ResponseHandler

class MCPClient:
    """企业级数据库分析MCP客户端"""
    
    def __init__(self, server_name: str = DEFAULT_SERVER):
        """
        初始化MCP客户端
        
        Args:
            server_name: 服务器名称
        """
        self.server_name = server_name
        self.server_config = config.get_server_config(server_name)
        
        if not self.server_config:
            raise ValueError(f"未找到服务器配置: {server_name}")
        
        self.client = None
        self.transport = None
        self.connected = False
        
        # 初始化处理器
        self.tool_handler = ToolHandler(self)
        self.data_handler = DataHandler(self)
        self.response_handler = ResponseHandler(self)
        
        # 可用工具缓存
        self.available_tools = {}
        self.last_tool_refresh = None
        
        log.info(f"MCP客户端初始化完成: {server_name}")
    
    async def connect(self) -> bool:
        """连接到MCP服务器"""
        try:
            log.info(f"正在连接到MCP服务器: {self.server_config.name}")
            
            # 创建STDIO传输
            self.transport = StdioTransport(
                command="python",
                args=[self.server_config.server_path],
                env=self.server_config.env_vars,
                keep_alive=self.server_config.keep_alive
            )
            
            # 创建客户端
            self.client = Client(
                transport=self.transport,
                timeout=self.server_config.timeout
            )
            
            # 测试连接
            async with self.client:
                await self.client.ping()
                self.connected = True
                
                # 刷新工具列表
                await self._refresh_tools()
                
                log.success(f"成功连接到MCP服务器: {self.server_config.name}")
                return True
                
        except Exception as e:
            log.error(f"连接MCP服务器失败: {e}")
            self.connected = False
            return False
    
    async def disconnect(self):
        """断开连接"""
        try:
            if self.client and self.connected:
                # 客户端会自动处理断开连接
                self.connected = False
                log.info("已断开MCP服务器连接")
        except Exception as e:
            log.error(f"断开连接时出错: {e}")
    
    async def _refresh_tools(self):
        """刷新可用工具列表"""
        try:
            if not self.connected:
                return
            
            async with self.client:
                tools = await self.client.list_tools()
                self.available_tools = {tool.name: tool for tool in tools}
                self.last_tool_refresh = datetime.now()
                
                log.info(f"刷新工具列表完成，共 {len(self.available_tools)} 个工具")
                
        except Exception as e:
            log.error(f"刷新工具列表失败: {e}")
    
    async def get_available_tools(self) -> Dict[str, Any]:
        """获取可用工具列表"""
        if not self.available_tools or not self.last_tool_refresh:
            await self._refresh_tools()
        
        return {
            name: {
                "name": tool.name,
                "description": tool.description,
                "inputSchema": tool.inputSchema
            }
            for name, tool in self.available_tools.items()
        }
    
    async def call_tool(self, tool_name: str, arguments: Dict[str, Any] = None) -> Dict[str, Any]:
        """
        调用MCP工具
        
        Args:
            tool_name: 工具名称
            arguments: 工具参数
            
        Returns:
            工具执行结果
        """
        if not self.connected:
            await self.connect()
        
        if not self.connected:
            raise Exception("无法连接到MCP服务器")
        
        try:
            log.info(f"调用工具: {tool_name}, 参数: {arguments}")
            
            async with self.client:
                result = await self.client.call_tool(tool_name, arguments or {})
                
                # 处理结果
                processed_result = await self.response_handler.process_tool_result(
                    tool_name, result
                )
                
                log.success(f"工具调用成功: {tool_name}")
                return processed_result
                
        except ToolError as e:
            log.error(f"工具调用失败: {tool_name}, 错误: {e}")
            raise
        except Exception as e:
            log.error(f"工具调用异常: {tool_name}, 异常: {e}")
            raise
    
    async def query(self, query_text: str) -> Dict[str, Any]:
        """
        智能查询接口
        
        Args:
            query_text: 查询文本
            
        Returns:
            查询结果
        """
        log.info(f"处理智能查询: {query_text}")
        
        # 解析查询意图
        intent = await self.tool_handler.parse_query_intent(query_text)
        
        # 执行相应的工具调用
        result = await self.tool_handler.execute_intent(intent)
        
        return result
    
    async def get_system_status(self) -> Dict[str, Any]:
        """获取系统状态"""
        try:
            return await self.call_tool("get_system_status")
        except Exception as e:
            log.error(f"获取系统状态失败: {e}")
            return {"error": str(e)}
    
    async def statistical_analysis(self, 
                                 start_time: str,
                                 end_time: str, 
                                 columns: List[str],
                                 operation: str = "average") -> Dict[str, Any]:
        """统计分析"""
        return await self.call_tool("advanced_statistical_analysis", {
            "start_time": start_time,
            "end_time": end_time,
            "columns": columns,
            "operation": operation
        })
    
    async def detect_anomalies(self,
                             column: str,
                             method: str = "hybrid",
                             time_window: str = "24h",
                             sensitivity: float = 2.0) -> Dict[str, Any]:
        """异常检测"""
        return await self.call_tool("intelligent_anomaly_detection", {
            "column": column,
            "method": method,
            "time_window": time_window,
            "sensitivity": sensitivity
        })
    
    async def generate_chart(self,
                           chart_type: str,
                           columns: List[str],
                           time_range: str = "24h",
                           title: str = "数据图表") -> Dict[str, Any]:
        """生成图表"""
        return await self.call_tool("generate_advanced_chart", {
            "chart_type": chart_type,
            "columns": columns,
            "time_range": time_range,
            "title": title
        })
    
    async def create_alert_rule(self,
                              rule_id: str,
                              name: str,
                              column: str,
                              condition: str,
                              threshold: float) -> Dict[str, Any]:
        """创建提醒规则"""
        return await self.call_tool("create_alert_rule", {
            "rule_id": rule_id,
            "name": name,
            "column": column,
            "condition": condition,
            "threshold": threshold
        })
    
    def __enter__(self):
        """同步上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """同步上下文管理器退出"""
        if self.connected:
            asyncio.run(self.disconnect())
    
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.connect()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        await self.disconnect()

# 便捷函数
async def create_client(server_name: str = DEFAULT_SERVER) -> MCPClient:
    """创建并连接MCP客户端"""
    client = MCPClient(server_name)
    await client.connect()
    return client

# 主函数 - 命令行界面
async def main():
    """主函数 - 简单的命令行界面"""
    print("🤖 企业级数据库分析MCP客户端")
    print("=" * 50)
    
    client = MCPClient()
    
    try:
        # 连接服务器
        if not await client.connect():
            print("❌ 无法连接到MCP服务器")
            return
        
        # 显示可用工具
        tools = await client.get_available_tools()
        print(f"\n📊 可用工具 ({len(tools)} 个):")
        for name, tool in tools.items():
            print(f"  • {name}: {tool['description']}")
        
        # 获取系统状态
        print("\n⚙️ 系统状态:")
        status = await client.get_system_status()
        if "error" not in status:
            print(f"  • 数据库状态: {status.get('database_status', 'unknown')}")
            print(f"  • 数据总量: {status.get('total_data_rows', 0):,}")
            print(f"  • 连接池大小: {status.get('connection_pool_size', 0)}")
        
        # 交互式查询
        print("\n💬 输入查询 (输入 'quit' 退出):")
        while True:
            try:
                query = input("\n> ").strip()
                if query.lower() in ['quit', 'exit', 'q']:
                    break
                
                if query:
                    result = await client.query(query)
                    print(f"📋 结果: {result}")
                    
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ 错误: {e}")
    
    finally:
        await client.disconnect()
        print("\n👋 客户端已退出")

if __name__ == "__main__":
    asyncio.run(main())
