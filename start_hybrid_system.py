#!/usr/bin/env python3
"""
混合架构启动脚本
同时启动MCP服务器（HTTP API模式）和Streamlit前端
"""

import os
import sys
import subprocess
import time
import threading
import signal
from pathlib import Path

def start_mcp_server():
    """启动MCP服务器（HTTP API模式）"""
    print("🚀 启动MCP数据分析服务器...")
    
    # 设置环境变量启用HTTP API模式
    env = os.environ.copy()
    env["HTTP_API_MODE"] = "true"
    env["HTTP_API_PORT"] = "8000"
    
    # 启动MCP服务器
    try:
        process = subprocess.Popen(
            [sys.executable, "enterprise_database_mcp_server.py"],
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # 实时输出日志
        for line in iter(process.stdout.readline, ''):
            print(f"[MCP] {line.rstrip()}")
            
    except Exception as e:
        print(f"❌ MCP服务器启动失败: {e}")

def start_streamlit_app():
    """启动Streamlit前端"""
    print("🌐 启动Streamlit前端...")
    
    # 等待MCP服务器启动
    time.sleep(5)
    
    # 设置环境变量
    env = os.environ.copy()
    env["API_BASE_URL"] = "http://localhost:8000"
    env["REFRESH_INTERVAL"] = "30"
    
    try:
        process = subprocess.Popen(
            [
                sys.executable, "-m", "streamlit", "run", 
                "streamlit_dashboard.py",
                "--server.port", "8501",
                "--server.address", "0.0.0.0",
                "--server.headless", "true",
                "--browser.gatherUsageStats", "false"
            ],
            env=env,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            bufsize=1
        )
        
        # 实时输出日志
        for line in iter(process.stdout.readline, ''):
            print(f"[Streamlit] {line.rstrip()}")
            
    except Exception as e:
        print(f"❌ Streamlit前端启动失败: {e}")

def check_dependencies():
    """检查依赖是否安装"""
    print("🔍 检查依赖...")
    
    required_packages = [
        "streamlit",
        "fastmcp", 
        "mysql-connector-python",
        "pandas",
        "plotly",
        "requests"
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ 缺少依赖包: {', '.join(missing_packages)}")
        print("请运行: pip install " + " ".join(missing_packages))
        return False
    
    print("✅ 所有依赖已安装")
    return True

def check_database():
    """检查数据库连接"""
    print("🔍 检查数据库连接...")
    
    try:
        import mysql.connector
        
        connection = mysql.connector.connect(
            host=os.getenv("DB_HOST", "localhost"),
            port=int(os.getenv("DB_PORT", "3306")),
            user=os.getenv("DB_USER", "root"),
            password=os.getenv("DB_PASSWORD", "123456"),
            database=os.getenv("DB_NAME", "sensor_data")
        )
        
        cursor = connection.cursor()
        cursor.execute("SELECT COUNT(*) FROM sensor_data")
        count = cursor.fetchone()[0]
        
        cursor.close()
        connection.close()
        
        print(f"✅ 数据库连接正常，数据行数: {count}")
        return True
        
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return False

def signal_handler(signum, frame):
    """信号处理器"""
    print("\n🛑 收到停止信号，正在关闭服务...")
    sys.exit(0)

def main():
    """主函数"""
    print("🚀 企业级数据库分析系统 - 混合架构启动")
    print("=" * 60)
    
    # 注册信号处理器
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    # 检查数据库
    if not check_database():
        print("⚠️ 数据库连接失败，但仍可启动服务（部分功能可能不可用）")
    
    print("\n🎯 启动服务...")
    print("📊 MCP服务器: http://localhost:8000")
    print("🌐 Streamlit前端: http://localhost:8501")
    print("📚 API文档: http://localhost:8000/docs")
    
    # 创建线程启动服务
    mcp_thread = threading.Thread(target=start_mcp_server, daemon=True)
    streamlit_thread = threading.Thread(target=start_streamlit_app, daemon=True)
    
    # 启动服务
    mcp_thread.start()
    streamlit_thread.start()
    
    print("\n✅ 服务启动完成！")
    print("\n📋 使用指南:")
    print("1. 访问 http://localhost:8501 使用Streamlit前端")
    print("2. 访问 http://localhost:8000/docs 查看API文档")
    print("3. 按 Ctrl+C 停止所有服务")
    
    try:
        # 保持主线程运行
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n👋 服务已停止")

if __name__ == "__main__":
    main()
