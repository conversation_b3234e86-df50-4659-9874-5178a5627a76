set HTTP_API_MODE=true
set HTTP_API_PORT=8000
python enterprise_database_mcp_server.py


set API_BASE_URL=http://localhost:8000
set REFRESH_INTERVAL=30
python -m streamlit run streamlit_dashboard.py --server.port 8501


streamlit run mcp_client/debug_frontend.py --server.port=8502

# 第1步：启动MCP服务器
python mcp_client/simple_http_server.py

# 第2步：启动前端（新终端）
streamlit run mcp_client/enterprise_ai_frontend.py --server.port=8501