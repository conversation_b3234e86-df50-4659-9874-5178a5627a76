#!/usr/bin/env python3
"""测试语法"""

import ast
import sys

def test_syntax(filename):
    try:
        with open(filename, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查语法
        ast.parse(content)
        print(f"✅ {filename} 语法检查通过")
        return True
    except SyntaxError as e:
        print(f"❌ {filename} 语法错误:")
        print(f"   行 {e.lineno}: {e.text}")
        print(f"   错误: {e.msg}")
        return False
    except Exception as e:
        print(f"❌ {filename} 检查失败: {e}")
        return False

if __name__ == "__main__":
    test_syntax("mcp_client/enterprise_ai_frontend.py")
