#!/usr/bin/env python3
"""测试修复的CallToolResult处理 - 版本2"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

async def test_call_tool():
    """测试工具调用和结果处理"""
    try:
        # 创建客户端
        transport = StreamableHttpTransport("http://127.0.0.1:8000/mcp/")
        client = Client(transport)
        
        print("🔗 连接到MCP服务器...")
        
        async with client:
            print("✅ 连接成功")
            
            # 测试工具调用
            print("🔧 调用工具: advanced_statistical_analysis")
            result = await client.call_tool("advanced_statistical_analysis", {
                "start_time": "2020-01-01 00:00:00",
                "end_time": "2030-01-01 00:00:00",
                "columns": ["temperature"],
                "operation": "count"
            })
            
            print(f"📊 结果类型: {type(result)}")
            
            # 测试新的数据访问方式
            if hasattr(result, 'data'):
                print(f"📊 result.data: {result.data}")
                print(f"📊 result.data 类型: {type(result.data)}")
                
                # 使用修复后的逻辑
                if isinstance(result.data, (int, float)):
                    count = result.data
                elif isinstance(result.data, list) and len(result.data) > 0:
                    # 处理列表格式的结果
                    first_item = result.data[0]
                    if isinstance(first_item, dict) and 'result' in first_item:
                        count = first_item['result']
                    else:
                        count = 0
                elif isinstance(result.data, dict) and 'result' in result.data:
                    count = result.data['result']
                else:
                    count = 0
                    
                print(f"✅ 提取的计数: {count}")
            else:
                print("❌ 没有 data 属性")
                
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_call_tool())
