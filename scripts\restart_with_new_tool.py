#!/usr/bin/env python3
"""
重启MCP服务器并加载新工具
"""

import os
import sys
import time
import subprocess
import signal

def kill_existing_servers():
    """杀死现有的MCP服务器进程"""
    print("🧹 清理现有MCP服务器进程...")
    
    try:
        # 获取Python进程列表
        result = subprocess.run(
            ["tasklist", "/FI", "IMAGENAME eq python.exe", "/FO", "CSV"],
            capture_output=True, text=True, shell=True
        )
        
        if result.returncode == 0:
            lines = result.stdout.strip().split('\n')
            for line in lines[1:]:  # 跳过标题行
                if line:
                    parts = line.split(',')
                    if len(parts) >= 2:
                        pid = parts[1].strip('"')
                        try:
                            pid_num = int(pid)
                            # 尝试杀死进程
                            subprocess.run(["taskkill", "/F", "/PID", str(pid_num)], 
                                         capture_output=True, shell=True)
                            print(f"✅ 已终止进程 PID: {pid_num}")
                        except ValueError:
                            continue
        
        time.sleep(2)  # 等待进程完全终止
        print("✅ 进程清理完成")
        
    except Exception as e:
        print(f"⚠️ 进程清理警告: {e}")

def setup_environment():
    """设置环境变量"""
    env_vars = {
        "HTTP_API_MODE": "true",
        "HTTP_API_PORT": "8000",
        "DB_HOST": "localhost",
        "DB_PORT": "3306",
        "DB_USER": "root",
        "DB_PASSWORD": "123456",
        "DB_NAME": "sensor_data",
        "DB_CHARSET": "utf8mb4",
        "PYTHONIOENCODING": "utf-8"
    }
    
    print("🌍 设置环境变量:")
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  {key} = {value}")

def start_new_server():
    """启动新的MCP服务器"""
    print("\n🚀 启动新的MCP服务器...")
    
    try:
        # 启动服务器
        cmd = [sys.executable, "enterprise_database_mcp_server.py"]
        
        process = subprocess.Popen(
            cmd,
            cwd=os.getcwd(),
            env=os.environ.copy(),
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print("✅ MCP服务器启动中...")
        print("📍 服务器地址: http://127.0.0.1:8000/mcp")
        
        # 等待服务器启动
        time.sleep(8)
        
        return process
        
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")
        return None

def test_new_tool():
    """测试新工具是否可用"""
    print("\n🧪 测试新工具...")
    
    try:
        import asyncio
        from fastmcp import Client
        from fastmcp.client.transports import StreamableHttpTransport
        
        async def test():
            try:
                transport = StreamableHttpTransport("http://127.0.0.1:8000/mcp")
                client = Client(transport)
                
                async with client:
                    # 测试新的AI智能分析工具
                    result = await client.call_tool("ai_intelligent_analysis", {
                        "data_query": "测试查询",
                        "analysis_type": "trend",
                        "context": "测试",
                        "language": "zh",
                        "include_visualization": True
                    })
                    
                    print("✅ ai_intelligent_analysis工具测试成功")
                    return True
                    
            except Exception as e:
                print(f"❌ 工具测试失败: {e}")
                return False
        
        return asyncio.run(test())
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔄 MCP服务器重启 - 加载新工具")
    print("=" * 50)
    
    # 1. 清理现有进程
    kill_existing_servers()
    
    # 2. 设置环境
    setup_environment()
    
    # 3. 启动新服务器
    process = start_new_server()
    if not process:
        print("❌ 服务器启动失败")
        return
    
    # 4. 测试新工具
    if test_new_tool():
        print("\n🎉 重启成功！新工具已加载")
        print("📍 MCP服务器: http://127.0.0.1:8000/mcp")
        print("🆕 新工具: ai_intelligent_analysis")
        print("\n现在可以在Streamlit前端中使用AI智能分析功能了！")
        
        print("\n⏹️ 按 Ctrl+C 停止服务器")
        try:
            # 监控服务器进程
            while True:
                if process.poll() is not None:
                    print("❌ 服务器意外停止")
                    break
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n👋 正在停止服务器...")
            process.terminate()
            process.wait()
            print("✅ 服务器已停止")
    else:
        print("\n❌ 新工具测试失败")
        process.terminate()

if __name__ == "__main__":
    main()
