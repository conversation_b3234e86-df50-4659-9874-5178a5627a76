#!/usr/bin/env python3
"""
企业级AI数据分析MCP系统 - 一键启动脚本
自动启动所有组件：MCP服务器、前端界面、监控服务
"""

import os
import sys
import time
import subprocess
import threading
import signal
import logging
from pathlib import Path
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enterprise_startup.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class EnterpriseSystemLauncher:
    """企业级系统启动器"""
    
    def __init__(self):
        self.processes = []
        self.base_dir = Path(__file__).parent
        self.mcp_client_dir = self.base_dir / "mcp_client"
        self.running = True
        
        # 注册信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
    
    def signal_handler(self, signum, frame):
        """处理退出信号"""
        logger.info("收到退出信号，正在关闭系统...")
        self.running = False
        self.stop_all_processes()
        sys.exit(0)
    
    def check_dependencies(self):
        """检查依赖环境"""
        logger.info("🔍 检查系统依赖...")
        
        # 检查Python版本
        if sys.version_info < (3, 9):
            logger.error("❌ Python版本需要3.9+")
            return False
        
        # 检查必要的包
        required_packages = [
            'fastmcp', 'streamlit', 'mysql.connector', 
            'pandas', 'numpy', 'plotly', 'openai'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package.replace('-', '_').replace('.', '_'))
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            logger.error(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
            logger.info("请运行: pip install -r requirements.txt")
            return False
        
        # 检查环境变量
        required_env_vars = ['DB_HOST', 'DB_USER', 'DB_PASSWORD', 'DB_NAME']
        missing_env_vars = []
        
        for var in required_env_vars:
            if not os.getenv(var):
                missing_env_vars.append(var)
        
        if missing_env_vars:
            logger.warning(f"⚠️ 缺少环境变量: {', '.join(missing_env_vars)}")
            logger.info("将使用默认配置")
        
        logger.info("✅ 依赖检查完成")
        return True
    
    def check_database_connection(self):
        """检查数据库连接"""
        logger.info("🔗 检查数据库连接...")
        
        try:
            import mysql.connector
            
            config = {
                'host': os.getenv('DB_HOST', 'localhost'),
                'port': int(os.getenv('DB_PORT', '3306')),
                'user': os.getenv('DB_USER', 'root'),
                'password': os.getenv('DB_PASSWORD', '123456'),
                'database': os.getenv('DB_NAME', 'sensor_data'),
                'charset': os.getenv('DB_CHARSET', 'utf8mb4')
            }
            
            connection = mysql.connector.connect(**config)
            connection.close()
            
            logger.info("✅ 数据库连接正常")
            return True
            
        except Exception as e:
            logger.error(f"❌ 数据库连接失败: {e}")
            logger.info("请检查MySQL服务是否启动，配置是否正确")
            return False
    
    def start_mcp_server(self):
        """启动MCP服务器"""
        logger.info("🚀 启动AI MCP服务器...")
        
        try:
            # 检查服务器文件
            server_file = self.base_dir / "enterprise_ai_mcp_server.py"
            if not server_file.exists():
                # 回退到简单服务器
                server_file = self.mcp_client_dir / "simple_http_server.py"
            
            if not server_file.exists():
                logger.error("❌ 找不到MCP服务器文件")
                return None
            
            # 启动服务器
            cmd = [sys.executable, str(server_file)]
            process = subprocess.Popen(
                cmd,
                cwd=str(server_file.parent),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # 等待服务器启动
            time.sleep(5)
            
            if process.poll() is None:
                logger.info("✅ MCP服务器启动成功")
                self.processes.append(('MCP Server', process))
                
                # 启动日志监控线程
                threading.Thread(
                    target=self.monitor_process_output,
                    args=(process, "MCP Server"),
                    daemon=True
                ).start()
                
                return process
            else:
                stdout, stderr = process.communicate()
                logger.error(f"❌ MCP服务器启动失败: {stderr}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 启动MCP服务器时出错: {e}")
            return None
    
    def test_mcp_connection(self):
        """测试MCP连接"""
        logger.info("🔍 测试MCP连接...")
        
        try:
            import requests
            
            # 测试HTTP连接
            response = requests.get("http://127.0.0.1:8000/health", timeout=10)
            if response.status_code == 200:
                logger.info("✅ MCP HTTP连接正常")
                return True
            else:
                logger.warning(f"⚠️ MCP HTTP响应异常: {response.status_code}")
                
        except requests.exceptions.RequestException:
            # 尝试使用MCP客户端测试
            try:
                client_file = self.mcp_client_dir / "step2_start_client.py"
                if client_file.exists():
                    result = subprocess.run(
                        [sys.executable, str(client_file), "test"],
                        cwd=str(client_file.parent),
                        capture_output=True,
                        text=True,
                        timeout=30
                    )
                    
                    if "连接成功" in result.stdout:
                        logger.info("✅ MCP连接测试成功")
                        return True
                    else:
                        logger.warning("⚠️ MCP连接测试失败")
                        
            except Exception as e:
                logger.warning(f"⚠️ MCP连接测试出错: {e}")
        
        return False
    
    def start_frontend(self):
        """启动前端界面"""
        logger.info("🌐 启动前端界面...")
        
        try:
            # 检查前端文件
            frontend_files = [
                self.mcp_client_dir / "enterprise_ai_frontend.py",
                self.mcp_client_dir / "simple_frontend.py",
                self.mcp_client_dir / "step3_start_frontend.py"
            ]
            
            frontend_file = None
            for file in frontend_files:
                if file.exists():
                    frontend_file = file
                    break
            
            if not frontend_file:
                logger.error("❌ 找不到前端文件")
                return None
            
            # 启动Streamlit
            cmd = [
                sys.executable, "-m", "streamlit", "run",
                str(frontend_file),
                "--server.port=8501",
                "--server.address=localhost",
                "--browser.gatherUsageStats=false",
                "--server.headless=true"
            ]
            
            process = subprocess.Popen(
                cmd,
                cwd=str(frontend_file.parent),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待前端启动
            time.sleep(8)
            
            if process.poll() is None:
                logger.info("✅ 前端界面启动成功")
                logger.info("🌐 访问地址: http://localhost:8501")
                self.processes.append(('Frontend', process))
                
                # 启动日志监控线程
                threading.Thread(
                    target=self.monitor_process_output,
                    args=(process, "Frontend"),
                    daemon=True
                ).start()
                
                return process
            else:
                stdout, stderr = process.communicate()
                logger.error(f"❌ 前端启动失败: {stderr}")
                return None
                
        except Exception as e:
            logger.error(f"❌ 启动前端时出错: {e}")
            return None
    
    def monitor_process_output(self, process, name):
        """监控进程输出"""
        try:
            for line in iter(process.stdout.readline, ''):
                if line.strip():
                    logger.info(f"[{name}] {line.strip()}")
                if not self.running:
                    break
        except Exception as e:
            logger.error(f"监控{name}输出时出错: {e}")
    
    def stop_all_processes(self):
        """停止所有进程"""
        logger.info("🛑 正在停止所有服务...")
        
        for name, process in self.processes:
            try:
                logger.info(f"停止 {name}...")
                process.terminate()
                
                # 等待进程结束
                try:
                    process.wait(timeout=10)
                    logger.info(f"✅ {name} 已停止")
                except subprocess.TimeoutExpired:
                    logger.warning(f"⚠️ {name} 未响应，强制终止")
                    process.kill()
                    
            except Exception as e:
                logger.error(f"停止 {name} 时出错: {e}")
        
        self.processes.clear()
    
    def show_system_status(self):
        """显示系统状态"""
        print("\n" + "="*60)
        print("🤖 企业级AI数据分析MCP系统")
        print("="*60)
        print(f"📅 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🔗 MCP服务器: http://127.0.0.1:8000/mcp/")
        print(f"🌐 前端界面: http://localhost:8501")
        print(f"📊 运行进程: {len(self.processes)}")
        print("="*60)
        print("💡 使用说明:")
        print("  • 访问 http://localhost:8501 使用Web界面")
        print("  • 按 Ctrl+C 停止系统")
        print("  • 查看 enterprise_startup.log 获取详细日志")
        print("="*60)
    
    def run(self):
        """运行系统"""
        logger.info("🚀 启动企业级AI数据分析MCP系统")
        
        try:
            # 1. 检查依赖
            if not self.check_dependencies():
                return False
            
            # 2. 检查数据库
            if not self.check_database_connection():
                logger.warning("⚠️ 数据库连接失败，部分功能可能不可用")
            
            # 3. 启动MCP服务器
            mcp_process = self.start_mcp_server()
            if not mcp_process:
                logger.error("❌ MCP服务器启动失败，无法继续")
                return False
            
            # 4. 测试MCP连接
            if not self.test_mcp_connection():
                logger.warning("⚠️ MCP连接测试失败，但继续启动前端")
            
            # 5. 启动前端
            frontend_process = self.start_frontend()
            if not frontend_process:
                logger.error("❌ 前端启动失败")
                self.stop_all_processes()
                return False
            
            # 6. 显示系统状态
            self.show_system_status()
            
            # 7. 保持运行
            try:
                while self.running:
                    time.sleep(1)
                    
                    # 检查进程状态
                    for name, process in self.processes[:]:
                        if process.poll() is not None:
                            logger.error(f"❌ {name} 进程意外退出")
                            self.processes.remove((name, process))
                    
                    # 如果所有进程都退出了，停止系统
                    if not self.processes:
                        logger.error("❌ 所有进程都已退出")
                        break
                        
            except KeyboardInterrupt:
                logger.info("收到中断信号")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 系统启动失败: {e}")
            return False
        
        finally:
            self.stop_all_processes()

def main():
    """主函数"""
    launcher = EnterpriseSystemLauncher()
    success = launcher.run()
    
    if success:
        print("\n✅ 系统已成功启动并运行")
    else:
        print("\n❌ 系统启动失败")
        sys.exit(1)

if __name__ == "__main__":
    main()
