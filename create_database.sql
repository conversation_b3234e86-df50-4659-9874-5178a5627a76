-- =====================================================
-- 数据库分析MCP服务器 - 数据库初始化脚本
-- =====================================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS sensor_data 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE sensor_data;

-- =====================================================
-- 主数据表：传感器数据
-- =====================================================
CREATE TABLE IF NOT EXISTS sensor_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    timestamp DATETIME NOT NULL COMMENT '时间戳',
    
    -- 环境传感器数据
    temperature DECIMAL(5,2) COMMENT '温度(°C)',
    humidity DECIMAL(5,2) COMMENT '湿度(%)',
    pressure DECIMAL(8,2) COMMENT '大气压力(Pa)',
    
    -- 流体传感器数据
    flow_rate DECIMAL(8,2) COMMENT '流量(L/min)',
    flow_total DECIMAL(12,2) COMMENT '累计流量(L)',
    
    -- 电气传感器数据
    voltage DECIMAL(6,2) COMMENT '电压(V)',
    current DECIMAL(6,2) COMMENT '电流(A)',
    power DECIMAL(8,2) COMMENT '功率(W)',
    frequency DECIMAL(5,2) COMMENT '频率(Hz)',
    
    -- 机械传感器数据
    vibration DECIMAL(6,3) COMMENT '振动(mm/s)',
    rotation_speed DECIMAL(8,2) COMMENT '转速(RPM)',
    torque DECIMAL(8,2) COMMENT '扭矩(N·m)',
    
    -- 位置传感器数据
    position_x DECIMAL(8,3) COMMENT 'X轴位置(mm)',
    position_y DECIMAL(8,3) COMMENT 'Y轴位置(mm)',
    position_z DECIMAL(8,3) COMMENT 'Z轴位置(mm)',
    
    -- 质量传感器数据
    weight DECIMAL(8,2) COMMENT '重量(kg)',
    density DECIMAL(6,3) COMMENT '密度(kg/m³)',
    
    -- 光学传感器数据
    light_intensity DECIMAL(8,2) COMMENT '光照强度(lux)',
    uv_index DECIMAL(4,2) COMMENT '紫外线指数',
    
    -- 化学传感器数据
    ph_value DECIMAL(4,2) COMMENT 'pH值',
    dissolved_oxygen DECIMAL(6,2) COMMENT '溶解氧(mg/L)',
    conductivity DECIMAL(8,2) COMMENT '电导率(μS/cm)',
    
    -- 状态字段
    device_id VARCHAR(50) COMMENT '设备ID',
    location VARCHAR(100) COMMENT '位置信息',
    status ENUM('normal', 'warning', 'error', 'offline') DEFAULT 'normal' COMMENT '设备状态',
    quality_flag TINYINT DEFAULT 1 COMMENT '数据质量标志(1=好,0=差)',
    
    -- 索引
    INDEX idx_timestamp (timestamp),
    INDEX idx_device_timestamp (device_id, timestamp),
    INDEX idx_status (status),
    INDEX idx_temperature (temperature),
    INDEX idx_pressure (pressure),
    INDEX idx_flow_rate (flow_rate),
    INDEX idx_power (power)
    
) ENGINE=InnoDB 
COMMENT='传感器实时数据表'
PARTITION BY RANGE (YEAR(timestamp)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026),
    PARTITION p2026 VALUES LESS THAN (2027)
);

-- =====================================================
-- 设备信息表
-- =====================================================
CREATE TABLE IF NOT EXISTS device_info (
    device_id VARCHAR(50) PRIMARY KEY COMMENT '设备ID',
    device_name VARCHAR(100) NOT NULL COMMENT '设备名称',
    device_type VARCHAR(50) NOT NULL COMMENT '设备类型',
    location VARCHAR(100) COMMENT '安装位置',
    manufacturer VARCHAR(100) COMMENT '制造商',
    model VARCHAR(100) COMMENT '型号',
    install_date DATE COMMENT '安装日期',
    maintenance_date DATE COMMENT '最后维护日期',
    status ENUM('active', 'inactive', 'maintenance') DEFAULT 'active' COMMENT '设备状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) ENGINE=InnoDB COMMENT='设备信息表';

-- =====================================================
-- 警报规则表
-- =====================================================
CREATE TABLE IF NOT EXISTS alert_rules (
    rule_id VARCHAR(50) PRIMARY KEY COMMENT '规则ID',
    rule_name VARCHAR(100) NOT NULL COMMENT '规则名称',
    device_id VARCHAR(50) COMMENT '设备ID',
    column_name VARCHAR(50) NOT NULL COMMENT '监控字段',
    condition_type ENUM('greater_than', 'less_than', 'range', 'change_rate') NOT NULL COMMENT '条件类型',
    threshold_value DECIMAL(10,3) NOT NULL COMMENT '阈值',
    threshold_value2 DECIMAL(10,3) COMMENT '第二阈值(范围条件用)',
    severity ENUM('low', 'medium', 'high', 'critical') DEFAULT 'medium' COMMENT '严重级别',
    enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    notification_method SET('email', 'sms', 'voice', 'system') DEFAULT 'system' COMMENT '通知方式',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (device_id) REFERENCES device_info(device_id) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='警报规则表';

-- =====================================================
-- 警报历史表
-- =====================================================
CREATE TABLE IF NOT EXISTS alert_history (
    alert_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '警报ID',
    rule_id VARCHAR(50) NOT NULL COMMENT '规则ID',
    device_id VARCHAR(50) COMMENT '设备ID',
    alert_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '警报时间',
    column_name VARCHAR(50) NOT NULL COMMENT '触发字段',
    current_value DECIMAL(10,3) NOT NULL COMMENT '当前值',
    threshold_value DECIMAL(10,3) NOT NULL COMMENT '阈值',
    severity ENUM('low', 'medium', 'high', 'critical') NOT NULL COMMENT '严重级别',
    message TEXT COMMENT '警报消息',
    acknowledged BOOLEAN DEFAULT FALSE COMMENT '是否已确认',
    acknowledged_by VARCHAR(100) COMMENT '确认人',
    acknowledged_at TIMESTAMP NULL COMMENT '确认时间',
    resolved BOOLEAN DEFAULT FALSE COMMENT '是否已解决',
    resolved_at TIMESTAMP NULL COMMENT '解决时间',
    
    INDEX idx_alert_time (alert_time),
    INDEX idx_rule_id (rule_id),
    INDEX idx_device_id (device_id),
    INDEX idx_severity (severity),
    
    FOREIGN KEY (rule_id) REFERENCES alert_rules(rule_id) ON DELETE CASCADE
) ENGINE=InnoDB COMMENT='警报历史表';

-- =====================================================
-- 数据统计表（用于快速查询）
-- =====================================================
CREATE TABLE IF NOT EXISTS data_statistics (
    stat_id BIGINT AUTO_INCREMENT PRIMARY KEY,
    device_id VARCHAR(50),
    column_name VARCHAR(50) NOT NULL,
    stat_date DATE NOT NULL,
    stat_hour TINYINT COMMENT '小时(0-23)',
    min_value DECIMAL(10,3),
    max_value DECIMAL(10,3),
    avg_value DECIMAL(10,3),
    sum_value DECIMAL(15,3),
    count_value INT,
    std_value DECIMAL(10,3),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY uk_device_column_date_hour (device_id, column_name, stat_date, stat_hour),
    INDEX idx_stat_date (stat_date),
    INDEX idx_column_name (column_name)
) ENGINE=InnoDB COMMENT='数据统计表';

-- =====================================================
-- 插入示例设备信息
-- =====================================================
INSERT INTO device_info (device_id, device_name, device_type, location, manufacturer, model, install_date) VALUES
('TEMP_001', '温度传感器-1号', '温度传感器', '车间A-1区', '霍尼韦尔', 'T6000-TB', '2024-01-15'),
('PRESS_001', '压力传感器-1号', '压力传感器', '车间A-2区', '西门子', 'SITRANS P220', '2024-01-16'),
('FLOW_001', '流量传感器-1号', '流量传感器', '管道B-1', '艾默生', 'Micro Motion F-Series', '2024-01-17'),
('POWER_001', '电力监测-1号', '电力传感器', '配电室-1', '施耐德', 'PowerLogic PM8000', '2024-01-18'),
('VIB_001', '振动传感器-1号', '振动传感器', '设备C-1', 'SKF', 'CMSS 2200', '2024-01-19');

-- =====================================================
-- 插入示例警报规则
-- =====================================================
INSERT INTO alert_rules (rule_id, rule_name, device_id, column_name, condition_type, threshold_value, severity) VALUES
('TEMP_HIGH', '温度过高警报', 'TEMP_001', 'temperature', 'greater_than', 50.0, 'high'),
('TEMP_LOW', '温度过低警报', 'TEMP_001', 'temperature', 'less_than', 0.0, 'medium'),
('PRESS_HIGH', '压力过高警报', 'PRESS_001', 'pressure', 'greater_than', 110000.0, 'critical'),
('FLOW_LOW', '流量过低警报', 'FLOW_001', 'flow_rate', 'less_than', 50.0, 'medium'),
('POWER_HIGH', '功率过高警报', 'POWER_001', 'power', 'greater_than', 2000.0, 'high');

-- =====================================================
-- 插入示例传感器数据（最近24小时）
-- =====================================================
DELIMITER $$

CREATE PROCEDURE InsertSampleData()
BEGIN
    DECLARE i INT DEFAULT 0;
    DECLARE sample_time DATETIME;
    
    -- 从24小时前开始，每分钟插入一条数据
    WHILE i < 1440 DO  -- 24小时 * 60分钟 = 1440条记录
        SET sample_time = DATE_SUB(NOW(), INTERVAL (1440 - i) MINUTE);
        
        INSERT INTO sensor_data (
            timestamp, device_id, location, 
            temperature, humidity, pressure,
            flow_rate, flow_total,
            voltage, current, power, frequency,
            vibration, rotation_speed, torque,
            position_x, position_y, position_z,
            weight, density,
            light_intensity, uv_index,
            ph_value, dissolved_oxygen, conductivity,
            status, quality_flag
        ) VALUES (
            sample_time,
            CASE (i % 5)
                WHEN 0 THEN 'TEMP_001'
                WHEN 1 THEN 'PRESS_001'
                WHEN 2 THEN 'FLOW_001'
                WHEN 3 THEN 'POWER_001'
                ELSE 'VIB_001'
            END,
            CASE (i % 5)
                WHEN 0 THEN '车间A-1区'
                WHEN 1 THEN '车间A-2区'
                WHEN 2 THEN '管道B-1'
                WHEN 3 THEN '配电室-1'
                ELSE '设备C-1'
            END,
            -- 温度：20-30度，加入随机波动和异常值
            20 + 10 * RAND() + IF(RAND() > 0.95, 20, 0),
            -- 湿度：50-80%
            50 + 30 * RAND(),
            -- 压力：100000-105000 Pa
            100000 + 5000 * RAND(),
            -- 流量：100-200 L/min
            100 + 100 * RAND(),
            -- 累计流量
            1000 + i * 0.1,
            -- 电压：220V ± 10V
            220 + 20 * (RAND() - 0.5),
            -- 电流：5-10A
            5 + 5 * RAND(),
            -- 功率：1000-2200W
            1000 + 1200 * RAND(),
            -- 频率：50Hz ± 1Hz
            50 + 2 * (RAND() - 0.5),
            -- 振动：0-5 mm/s
            5 * RAND(),
            -- 转速：1000-3000 RPM
            1000 + 2000 * RAND(),
            -- 扭矩：50-150 N·m
            50 + 100 * RAND(),
            -- 位置坐标
            100 * (RAND() - 0.5),
            100 * (RAND() - 0.5),
            100 * (RAND() - 0.5),
            -- 重量：100-500 kg
            100 + 400 * RAND(),
            -- 密度：1.0-1.5 kg/m³
            1.0 + 0.5 * RAND(),
            -- 光照强度：100-1000 lux
            100 + 900 * RAND(),
            -- 紫外线指数：0-10
            10 * RAND(),
            -- pH值：6.5-8.5
            6.5 + 2 * RAND(),
            -- 溶解氧：5-15 mg/L
            5 + 10 * RAND(),
            -- 电导率：100-1000 μS/cm
            100 + 900 * RAND(),
            -- 状态
            CASE 
                WHEN RAND() > 0.95 THEN 'warning'
                WHEN RAND() > 0.98 THEN 'error'
                ELSE 'normal'
            END,
            -- 数据质量
            IF(RAND() > 0.9, 0, 1)
        );
        
        SET i = i + 1;
    END WHILE;
END$$

DELIMITER ;

-- 执行存储过程插入示例数据
CALL InsertSampleData();

-- 删除存储过程
DROP PROCEDURE InsertSampleData;

-- =====================================================
-- 创建视图：最新数据概览
-- =====================================================
CREATE VIEW latest_sensor_data AS
SELECT 
    d.device_name,
    d.device_type,
    d.location,
    s.timestamp,
    s.temperature,
    s.humidity,
    s.pressure,
    s.flow_rate,
    s.voltage,
    s.current,
    s.power,
    s.status
FROM sensor_data s
JOIN device_info d ON s.device_id = d.device_id
WHERE s.timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY s.timestamp DESC;

-- =====================================================
-- 创建触发器：自动更新统计数据
-- =====================================================
DELIMITER $$

CREATE TRIGGER update_statistics_after_insert
AFTER INSERT ON sensor_data
FOR EACH ROW
BEGIN
    -- 更新小时统计
    INSERT INTO data_statistics (
        device_id, column_name, stat_date, stat_hour,
        min_value, max_value, avg_value, sum_value, count_value
    ) VALUES (
        NEW.device_id, 'temperature', DATE(NEW.timestamp), HOUR(NEW.timestamp),
        NEW.temperature, NEW.temperature, NEW.temperature, NEW.temperature, 1
    ) ON DUPLICATE KEY UPDATE
        min_value = LEAST(min_value, NEW.temperature),
        max_value = GREATEST(max_value, NEW.temperature),
        avg_value = (avg_value * count_value + NEW.temperature) / (count_value + 1),
        sum_value = sum_value + NEW.temperature,
        count_value = count_value + 1;
END$$

DELIMITER ;

-- =====================================================
-- 完成信息
-- =====================================================
SELECT '数据库初始化完成！' AS message;
SELECT COUNT(*) AS '插入的数据行数' FROM sensor_data;
SELECT COUNT(*) AS '设备数量' FROM device_info;
SELECT COUNT(*) AS '警报规则数量' FROM alert_rules;
