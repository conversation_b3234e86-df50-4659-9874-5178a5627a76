#!/usr/bin/env python3
"""测试修复的CallToolResult处理"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

async def test_call_tool():
    """测试工具调用和结果处理"""
    try:
        # 创建客户端
        transport = StreamableHttpTransport("http://127.0.0.1:8000/mcp/")
        client = Client(transport)
        
        print("🔗 连接到MCP服务器...")
        
        async with client:
            print("✅ 连接成功")
            
            # 测试工具调用
            print("🔧 调用工具: advanced_statistical_analysis")
            result = await client.call_tool("advanced_statistical_analysis", {
                "start_time": "2020-01-01 00:00:00",
                "end_time": "2030-01-01 00:00:00",
                "columns": ["temperature"],
                "operation": "count"
            })
            
            print(f"📊 结果类型: {type(result)}")
            print(f"📊 结果属性: {dir(result)}")
            
            # 测试新的数据访问方式
            if hasattr(result, 'data'):
                print(f"📊 result.data: {result.data}")
                print(f"📊 result.data 类型: {type(result.data)}")
                
                if isinstance(result.data, (int, float)):
                    count = result.data
                elif isinstance(result.data, dict) and 'result' in result.data:
                    count = result.data['result']
                else:
                    count = 0
                    
                print(f"✅ 提取的计数: {count}")
            else:
                print("❌ 没有 data 属性")
                
            if hasattr(result, 'content'):
                print(f"📊 result.content: {result.content}")
                
            if hasattr(result, 'structured_content'):
                print(f"📊 result.structured_content: {result.structured_content}")
                
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_call_tool())
