#!/usr/bin/env python3
"""
调试版前端界面
专门用于调试MCP连接和工具调用问题
"""

import streamlit as st
import asyncio
import json
import traceback
from datetime import datetime
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

# 页面配置
st.set_page_config(
    page_title="MCP调试界面",
    page_icon="🔧",
    layout="wide"
)

class MCPDebugger:
    """MCP调试器"""
    
    def __init__(self):
        self.server_url = "http://127.0.0.1:8000/mcp/"
        self.client = None
        self.debug_logs = []
    
    def log_debug(self, message: str, level: str = "INFO"):
        """记录调试信息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        self.debug_logs.append(log_entry)
        
        # 在Streamlit中显示
        if level == "ERROR":
            st.error(f"🔴 {message}")
        elif level == "WARNING":
            st.warning(f"🟡 {message}")
        elif level == "SUCCESS":
            st.success(f"🟢 {message}")
        else:
            st.info(f"🔵 {message}")
    
    def create_client(self):
        """创建MCP客户端"""
        try:
            self.log_debug(f"正在创建MCP客户端，服务器地址: {self.server_url}")
            transport = StreamableHttpTransport(url=self.server_url)
            self.client = Client(transport)
            self.log_debug("MCP客户端创建成功", "SUCCESS")
            return True
        except Exception as e:
            self.log_debug(f"MCP客户端创建失败: {str(e)}", "ERROR")
            self.log_debug(f"错误详情: {traceback.format_exc()}", "ERROR")
            return False
    
    def test_connection(self):
        """测试连接"""
        try:
            self.log_debug("开始测试MCP连接...")
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            async def _test():
                async with self.client:
                    await self.client.ping()
                    return True
            
            result = loop.run_until_complete(_test())
            loop.close()
            
            if result:
                self.log_debug("MCP连接测试成功", "SUCCESS")
                return True
            else:
                self.log_debug("MCP连接测试失败", "ERROR")
                return False
                
        except Exception as e:
            self.log_debug(f"连接测试异常: {str(e)}", "ERROR")
            self.log_debug(f"异常详情: {traceback.format_exc()}", "ERROR")
            return False
    
    def get_tools(self):
        """获取工具列表"""
        try:
            self.log_debug("正在获取工具列表...")
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            async def _get():
                async with self.client:
                    tools = await self.client.list_tools()
                    return tools
            
            tools = loop.run_until_complete(_get())
            loop.close()
            
            if tools:
                self.log_debug(f"成功获取 {len(tools)} 个工具", "SUCCESS")
                return tools
            else:
                self.log_debug("未获取到任何工具", "WARNING")
                return []
                
        except Exception as e:
            self.log_debug(f"获取工具列表失败: {str(e)}", "ERROR")
            self.log_debug(f"错误详情: {traceback.format_exc()}", "ERROR")
            return []
    
    def call_tool(self, tool_name: str, arguments: dict = None):
        """调用工具"""
        try:
            self.log_debug(f"正在调用工具: {tool_name}")
            if arguments:
                self.log_debug(f"工具参数: {json.dumps(arguments, ensure_ascii=False, indent=2)}")
            
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            async def _call():
                async with self.client:
                    result = await self.client.call_tool(tool_name, arguments or {})
                    return result
            
            result = loop.run_until_complete(_call())
            loop.close()
            
            if result:
                self.log_debug(f"工具调用成功: {tool_name}", "SUCCESS")
                return result
            else:
                self.log_debug(f"工具调用返回空结果: {tool_name}", "WARNING")
                return None
                
        except Exception as e:
            self.log_debug(f"工具调用失败: {tool_name} - {str(e)}", "ERROR")
            self.log_debug(f"错误详情: {traceback.format_exc()}", "ERROR")
            return None
    
    def render_debug_panel(self):
        """渲染调试面板"""
        st.header("🔧 MCP系统调试面板")
        
        # 基础信息
        st.subheader("📋 基础信息")
        col1, col2 = st.columns(2)
        
        with col1:
            st.info(f"🌐 MCP服务器: {self.server_url}")
            st.info(f"🐍 Python版本: {sys.version}")
        
        with col2:
            st.info(f"⏰ 当前时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            st.info(f"📁 工作目录: {Path.cwd()}")
        
        st.markdown("---")
        
        # 连接测试
        st.subheader("🔗 连接测试")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            if st.button("🔧 创建客户端", type="primary"):
                self.create_client()
        
        with col2:
            if st.button("🔍 测试连接"):
                if self.client:
                    self.test_connection()
                else:
                    self.log_debug("请先创建客户端", "WARNING")
        
        with col3:
            if st.button("🛠️ 获取工具"):
                if self.client:
                    tools = self.get_tools()
                    if tools:
                        st.session_state.tools = tools
                else:
                    self.log_debug("请先创建客户端", "WARNING")
        
        st.markdown("---")

        # 示例问题
        st.subheader("💡 示例问题")

        example_col1, example_col2 = st.columns(2)

        with example_col1:
            st.markdown("""
            **📊 数据分析示例**
            - 查询过去24小时的温度平均值
            - 检测温度异常数据
            - 分析设备运行状态
            - 生成温度趋势图表
            """)

            if st.button("🚀 快速测试：系统状态"):
                if self.client:
                    result = self.call_tool("get_system_status")
                    if result:
                        st.json(result)
                else:
                    self.log_debug("请先创建客户端", "WARNING")

        with example_col2:
            st.markdown("""
            **🔍 异常检测示例**
            - 检测温度突变
            - 识别设备故障模式
            - 分析数据质量问题
            - 预测设备维护需求
            """)

            if st.button("🚀 快速测试：异常检测"):
                if self.client:
                    args = {
                        "column": "temperature",
                        "method": "hybrid",
                        "time_window": "24h",
                        "include_reasons": True
                    }
                    result = self.call_tool("intelligent_anomaly_detection", args)
                    if result:
                        st.json(result)
                else:
                    self.log_debug("请先创建客户端", "WARNING")

        # 常见问题解答
        with st.expander("❓ 常见问题解答"):
            st.markdown("""
            **Q: 为什么显示"无有效数据"？**
            A: 可能原因：
            1. 数据库中没有指定时间范围的数据
            2. 列名不存在或拼写错误
            3. 数据格式问题

            **Q: 如何添加测试数据？**
            A: 可以在MySQL中执行：
            ```sql
            INSERT INTO sensor_data (timestamp, device_id, location, temperature, humidity)
            VALUES (NOW(), 'TEST_001', 'Lab', 25.5, 60.0);
            ```

            **Q: 连接失败怎么办？**
            A: 检查步骤：
            1. 确保MCP服务器正在运行
            2. 检查端口8000是否被占用
            3. 验证数据库连接
            """)

        st.markdown("---")

        # 工具测试
        st.subheader("🛠️ 工具测试")
        
        if 'tools' in st.session_state and st.session_state.tools:
            tool_names = [tool.name for tool in st.session_state.tools]
            selected_tool = st.selectbox("选择工具", tool_names)
            
            # 显示工具信息
            selected_tool_obj = next((t for t in st.session_state.tools if t.name == selected_tool), None)
            if selected_tool_obj:
                st.write(f"**描述**: {selected_tool_obj.description}")
                
                # 工具参数输入
                st.write("**参数设置**:")
                
                if selected_tool == "get_system_status":
                    if st.button("🚀 调用 get_system_status"):
                        result = self.call_tool("get_system_status")
                        if result:
                            st.json(result)
                
                elif selected_tool == "advanced_statistical_analysis":
                    st.info("💡 提示：如果没有数据，请尝试使用当前时间范围")

                    # 预设时间选项
                    time_preset = st.selectbox("时间预设", [
                        "自定义",
                        "过去1小时",
                        "过去24小时",
                        "过去7天",
                        "测试数据范围"
                    ])

                    col1, col2 = st.columns(2)
                    with col1:
                        if time_preset == "过去1小时":
                            start_time = st.text_input("开始时间", "DATE_SUB(NOW(), INTERVAL 1 HOUR)")
                            end_time = st.text_input("结束时间", "NOW()")
                        elif time_preset == "过去24小时":
                            start_time = st.text_input("开始时间", "DATE_SUB(NOW(), INTERVAL 24 HOUR)")
                            end_time = st.text_input("结束时间", "NOW()")
                        elif time_preset == "过去7天":
                            start_time = st.text_input("开始时间", "DATE_SUB(NOW(), INTERVAL 7 DAY)")
                            end_time = st.text_input("结束时间", "NOW()")
                        elif time_preset == "测试数据范围":
                            start_time = st.text_input("开始时间", "2024-08-01 00:00:00")
                            end_time = st.text_input("结束时间", "2024-08-03 00:00:00")
                        else:
                            start_time = st.text_input("开始时间", "2024-01-01 00:00:00")
                            end_time = st.text_input("结束时间", "2024-01-02 00:00:00")

                    with col2:
                        columns = st.text_input("列名", "temperature")
                        operation = st.selectbox("操作", ["average", "sum", "count", "min", "max"])

                    # 显示将要执行的SQL查询
                    with st.expander("🔍 查看SQL查询"):
                        st.code(f"""
SELECT {operation}({columns}) as result
FROM sensor_data
WHERE timestamp BETWEEN '{start_time}' AND '{end_time}'
AND {columns} IS NOT NULL
                        """)

                    if st.button("🚀 调用统计分析"):
                        args = {
                            "start_time": start_time,
                            "end_time": end_time,
                            "columns": [columns],
                            "operation": operation
                        }
                        self.log_debug(f"统计分析参数: {args}")
                        result = self.call_tool("advanced_statistical_analysis", args)
                        if result:
                            st.success("✅ 统计分析完成")
                            st.json(result)
                        else:
                            st.warning("⚠️ 可能原因：指定时间范围内没有数据")
                
                elif selected_tool == "intelligent_anomaly_detection":
                    st.info("💡 异常检测会分析指定时间窗口内的数据模式")

                    col1, col2 = st.columns(2)
                    with col1:
                        column = st.selectbox("列名", [
                            "temperature", "humidity", "pressure",
                            "voltage", "current", "power"
                        ])
                        method = st.selectbox("检测方法", [
                            "hybrid", "statistical", "isolation_forest",
                            "zscore", "iqr"
                        ], format_func=lambda x: {
                            "hybrid": "🔄 混合方法（推荐）",
                            "statistical": "📊 统计学方法",
                            "isolation_forest": "🌲 孤立森林",
                            "zscore": "📈 Z-Score标准化",
                            "iqr": "📦 四分位距"
                        }[x])

                    with col2:
                        time_window = st.selectbox("时间窗口", [
                            "1h", "6h", "12h", "24h", "7d"
                        ], index=3, format_func=lambda x: {
                            "1h": "⏰ 过去1小时",
                            "6h": "🕕 过去6小时",
                            "12h": "🕐 过去12小时",
                            "24h": "📅 过去24小时",
                            "7d": "📆 过去7天"
                        }[x])
                        include_reasons = st.checkbox("包含异常原因分析", True)

                    # 显示检测说明
                    with st.expander("🔍 检测方法说明"):
                        st.markdown("""
                        - **混合方法**: 结合多种算法，准确率最高
                        - **统计学方法**: 基于均值和标准差
                        - **孤立森林**: 机器学习异常检测
                        - **Z-Score**: 标准化分数检测
                        - **四分位距**: 基于数据分布的检测
                        """)

                    if st.button("🚀 调用异常检测"):
                        args = {
                            "column": column,
                            "method": method,
                            "time_window": time_window,
                            "include_reasons": include_reasons
                        }
                        self.log_debug(f"异常检测参数: {args}")
                        result = self.call_tool("intelligent_anomaly_detection", args)
                        if result:
                            st.success("✅ 异常检测完成")
                            st.json(result)
                        else:
                            st.warning("⚠️ 可能原因：指定时间窗口内没有足够数据进行异常检测")
                
                else:
                    # 通用参数输入
                    args_text = st.text_area("参数 (JSON格式)", "{}")
                    if st.button(f"🚀 调用 {selected_tool}"):
                        try:
                            args = json.loads(args_text) if args_text.strip() else {}
                            result = self.call_tool(selected_tool, args)
                            if result:
                                st.json(result)
                        except json.JSONDecodeError:
                            self.log_debug("参数JSON格式错误", "ERROR")
        
        else:
            st.info("请先获取工具列表")
        
        st.markdown("---")

        # 数据库管理
        st.subheader("🗄️ 数据库管理")

        db_col1, db_col2, db_col3 = st.columns(3)

        with db_col1:
            if st.button("📊 检查数据库状态"):
                if self.client:
                    result = self.call_tool("get_system_status")
                    if result:
                        st.json(result)
                else:
                    self.log_debug("请先创建客户端", "WARNING")

        with db_col2:
            if st.button("📈 查看数据统计"):
                if self.client:
                    # 查询数据总量
                    args = {
                        "start_time": "2020-01-01 00:00:00",
                        "end_time": "2030-01-01 00:00:00",
                        "columns": ["temperature"],
                        "operation": "count"
                    }
                    result = self.call_tool("advanced_statistical_analysis", args)
                    if result:
                        st.json(result)
                else:
                    self.log_debug("请先创建客户端", "WARNING")

        with db_col3:
            if st.button("🔧 插入测试数据"):
                st.info("💡 这将通过MCP工具插入一条测试数据")
                # 注意：这需要在MCP服务器中实现插入数据的工具
                self.log_debug("测试数据插入功能开发中...", "INFO")

        # 数据库连接信息
        with st.expander("🔍 数据库连接信息"):
            st.code("""
数据库配置:
- 主机: localhost
- 端口: 3306
- 用户: root
- 数据库: sensor_data
- 字符集: utf8mb4

表结构 (sensor_data):
- id: 主键
- timestamp: 时间戳
- device_id: 设备ID
- location: 位置
- temperature: 温度
- humidity: 湿度
- pressure: 压力
- voltage: 电压
- current: 电流
- power: 功率
            """)

        # SQL查询测试
        st.subheader("🔍 SQL查询测试")

        query_examples = st.selectbox("选择查询示例", [
            "自定义查询",
            "查看最新10条数据",
            "统计数据总量",
            "查看数据时间范围",
            "检查数据完整性"
        ])

        if query_examples == "查看最新10条数据":
            sql_query = "SELECT * FROM sensor_data ORDER BY timestamp DESC LIMIT 10"
        elif query_examples == "统计数据总量":
            sql_query = "SELECT COUNT(*) as total_rows FROM sensor_data"
        elif query_examples == "查看数据时间范围":
            sql_query = "SELECT MIN(timestamp) as earliest, MAX(timestamp) as latest FROM sensor_data"
        elif query_examples == "检查数据完整性":
            sql_query = "SELECT COUNT(*) as total, COUNT(temperature) as temp_count, COUNT(humidity) as humidity_count FROM sensor_data"
        else:
            sql_query = st.text_area("SQL查询", "SELECT COUNT(*) FROM sensor_data", height=100)

        st.code(sql_query, language="sql")

        if st.button("🚀 执行查询"):
            self.log_debug(f"执行SQL查询: {sql_query}")
            st.info("💡 注意：当前版本通过MCP工具执行查询，不是直接SQL")
            # 这里可以通过MCP工具执行查询

        st.markdown("---")

        # 调试日志
        st.subheader("📝 调试日志")
        
        if st.button("🗑️ 清空日志"):
            self.debug_logs.clear()
            st.rerun()
        
        if self.debug_logs:
            # 日志过滤
            log_filter = st.selectbox("日志级别过滤", [
                "全部", "INFO", "SUCCESS", "WARNING", "ERROR"
            ])

            filtered_logs = self.debug_logs
            if log_filter != "全部":
                filtered_logs = [log for log in self.debug_logs if log_filter in log]

            log_text = "\n".join(filtered_logs[-30:])  # 显示最近30条
            st.text_area("日志输出", log_text, height=300)

            # 日志统计
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                info_count = len([log for log in self.debug_logs if "INFO" in log])
                st.metric("信息", info_count)
            with col2:
                success_count = len([log for log in self.debug_logs if "SUCCESS" in log])
                st.metric("成功", success_count)
            with col3:
                warning_count = len([log for log in self.debug_logs if "WARNING" in log])
                st.metric("警告", warning_count)
            with col4:
                error_count = len([log for log in self.debug_logs if "ERROR" in log])
                st.metric("错误", error_count)
        else:
            st.info("暂无调试日志")

        # 系统信息
        st.subheader("💻 系统信息")

        sys_col1, sys_col2 = st.columns(2)

        with sys_col1:
            st.code(f"""
🐍 Python信息:
版本: {sys.version}
路径: {sys.executable}

📁 路径信息:
工作目录: {Path.cwd()}
脚本目录: {Path(__file__).parent}
            """)

        with sys_col2:
            st.code(f"""
🌐 网络信息:
MCP服务器: {self.server_url}
本地地址: http://localhost:8502
时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

🔧 客户端状态:
已创建: {'是' if self.client else '否'}
日志条数: {len(self.debug_logs)}
            """)

        # 性能监控
        if st.checkbox("显示性能监控"):
            import psutil

            perf_col1, perf_col2, perf_col3 = st.columns(3)

            with perf_col1:
                cpu_percent = psutil.cpu_percent()
                st.metric("CPU使用率", f"{cpu_percent}%")

            with perf_col2:
                memory = psutil.virtual_memory()
                st.metric("内存使用率", f"{memory.percent}%")

            with perf_col3:
                disk = psutil.disk_usage('/')
                st.metric("磁盘使用率", f"{disk.percent}%")

def main():
    """主函数"""
    debugger = MCPDebugger()

    # 添加标签页
    tab1, tab2, tab3 = st.tabs(["🔧 调试面板", "📊 实时监控", "📚 文档帮助"])

    with tab1:
        debugger.render_debug_panel()

    with tab2:
        debugger.render_realtime_monitor()

    with tab3:
        debugger.render_help_docs()

# 为MCPDebugger类添加新方法
def render_realtime_monitor(self):
    """渲染实时监控面板"""
    st.header("📊 实时系统监控")

    # 自动刷新控制
    auto_refresh = st.checkbox("自动刷新", value=False)
    refresh_interval = st.slider("刷新间隔(秒)", 1, 30, 5)

    if auto_refresh:
        st.info(f"🔄 每{refresh_interval}秒自动刷新")
        # 使用st.rerun()实现自动刷新
        import time
        time.sleep(refresh_interval)
        st.rerun()

    # 实时状态卡片
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        if self.client:
            st.success("🟢 MCP客户端")
            st.write("状态：已连接")
        else:
            st.error("🔴 MCP客户端")
            st.write("状态：未连接")

    with col2:
        # 检查MCP服务器状态
        try:
            import requests
            response = requests.get("http://127.0.0.1:8000/", timeout=2)
            if response.status_code == 200:
                st.success("🟢 MCP服务器")
                st.write("状态：运行中")
            else:
                st.warning("🟡 MCP服务器")
                st.write("状态：异常")
        except:
            st.error("🔴 MCP服务器")
            st.write("状态：离线")

    with col3:
        # 检查数据库连接
        if self.client:
            result = self.call_tool("get_system_status")
            if result and "database" in str(result):
                st.success("🟢 数据库")
                st.write("状态：已连接")
            else:
                st.warning("🟡 数据库")
                st.write("状态：检查中")
        else:
            st.error("🔴 数据库")
            st.write("状态：未知")

    with col4:
        current_time = datetime.now()
        st.info("🕐 系统时间")
        st.write(current_time.strftime("%H:%M:%S"))

    st.markdown("---")

    # 实时数据图表
    st.subheader("📈 实时数据监控")

    if st.button("🔄 刷新数据"):
        if self.client:
            # 获取最新数据
            args = {
                "start_time": "DATE_SUB(NOW(), INTERVAL 1 HOUR)",
                "end_time": "NOW()",
                "columns": ["temperature"],
                "operation": "average"
            }
            result = self.call_tool("advanced_statistical_analysis", args)
            if result:
                st.metric("平均温度", f"{result.get('result', 'N/A')}°C")

            # 异常检测
            anomaly_args = {
                "column": "temperature",
                "method": "hybrid",
                "time_window": "1h",
                "include_reasons": False
            }
            anomaly_result = self.call_tool("intelligent_anomaly_detection", anomaly_args)
            if anomaly_result:
                st.metric("异常检测", "正常" if "正常" in str(anomaly_result) else "异常")

    # 系统资源监控
    st.subheader("💻 系统资源")

    try:
        import psutil

        # CPU和内存使用率
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()

        col1, col2 = st.columns(2)

        with col1:
            st.metric("CPU使用率", f"{cpu_percent}%",
                     delta=f"{cpu_percent-50}%" if cpu_percent > 50 else None)

        with col2:
            st.metric("内存使用率", f"{memory.percent}%",
                     delta=f"{memory.percent-70}%" if memory.percent > 70 else None)

        # 进程信息
        with st.expander("🔍 进程信息"):
            processes = []
            for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent']):
                if 'python' in proc.info['name'].lower():
                    processes.append(proc.info)

            if processes:
                import pandas as pd
                df = pd.DataFrame(processes)
                st.dataframe(df)

    except ImportError:
        st.warning("⚠️ 需要安装psutil库来显示系统资源信息")

def render_help_docs(self):
    """渲染帮助文档"""
    st.header("📚 使用文档")

    # FastMCP文档链接
    st.subheader("🔗 相关文档")

    col1, col2 = st.columns(2)

    with col1:
        st.markdown("""
        **📖 FastMCP官方文档**
        - [FastMCP官网](https://gofastmcp.com)
        - [GitHub仓库](https://github.com/jlowin/fastmcp)
        - [部署指南](https://fastmcp.cloud)
        """)

    with col2:
        st.markdown("""
        **🛠️ 开发资源**
        - [MCP协议文档](https://spec.modelcontextprotocol.io/)
        - [Streamlit文档](https://docs.streamlit.io/)
        - [Python异步编程](https://docs.python.org/3/library/asyncio.html)
        """)

    # 使用指南
    st.subheader("📋 使用指南")

    with st.expander("🚀 快速开始"):
        st.markdown("""
        1. **启动MCP服务器**
           ```bash
           python mcp_client/simple_http_server.py
           ```

        2. **启动调试前端**
           ```bash
           streamlit run mcp_client/debug_frontend.py --server.port=8502
           ```

        3. **测试连接**
           - 点击"创建客户端"
           - 点击"测试连接"
           - 点击"获取工具"
        """)

    with st.expander("🔧 故障排除"):
        st.markdown("""
        **常见问题解决方案：**

        1. **连接失败**
           - 检查MCP服务器是否运行
           - 验证端口8000是否可用
           - 检查防火墙设置

        2. **工具调用失败**
           - 验证参数格式
           - 检查数据库连接
           - 查看服务器日志

        3. **数据查询无结果**
           - 检查时间范围
           - 验证列名是否正确
           - 确认数据库中有数据
        """)

    with st.expander("💡 最佳实践"):
        st.markdown("""
        **开发建议：**

        1. **调试流程**
           - 先使用调试前端测试
           - 逐步验证每个功能
           - 查看详细日志信息

        2. **性能优化**
           - 合理设置时间范围
           - 避免频繁的大数据查询
           - 使用连接池管理数据库

        3. **错误处理**
           - 添加详细的异常捕获
           - 提供用户友好的错误信息
           - 记录详细的调试日志
        """)

# 将新方法添加到MCPDebugger类
MCPDebugger.render_realtime_monitor = render_realtime_monitor
MCPDebugger.render_help_docs = render_help_docs

if __name__ == "__main__":
    main()
