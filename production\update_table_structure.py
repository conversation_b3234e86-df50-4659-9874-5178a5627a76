#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
更新表结构以匹配MCP服务器期望的列名
"""

import pymysql
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# MySQL连接配置
MYSQL_CONFIG = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': '123456',
    'database': 'sensor_data',
    'charset': 'utf8mb4'
}

def create_mcp_compatible_table():
    """创建与MCP服务器兼容的表结构"""
    logger.info("创建与MCP服务器兼容的表结构")
    
    # 创建新的表结构
    create_sql = """
    CREATE TABLE IF NOT EXISTS `sensor_data_new` (
      `id` INT AUTO_INCREMENT PRIMARY KEY,
      `timestamp` DATETIME NOT NULL,
      `temperature` DECIMAL(10,2) COMMENT '温度1',
      `temperature_2` DECIMAL(10,2) COMMENT '温度2',
      `temperature_3` DECIMAL(10,2) COMMENT '温度3',
      `temperature_4` DECIMAL(10,2) COMMENT '温度4',
      `temperature_5` DECIMAL(10,2) COMMENT '温度5',
      `temperature_6` DECIMAL(10,2) COMMENT '温度6',
      `pressure` DECIMAL(10,2) COMMENT '压力1',
      `pressure_2` DECIMAL(10,2) COMMENT '压力2',
      `pressure_3` DECIMAL(10,2) COMMENT '压力3',
      `pressure_4` DECIMAL(10,2) COMMENT '压力4',
      `pressure_5` DECIMAL(10,2) COMMENT '压力5',
      `pressure_7` DECIMAL(10,2) COMMENT '压力7',
      `oxygen_percent` DECIMAL(10,2) COMMENT '含氧百分比',
      `flow_rate` DECIMAL(10,2) COMMENT '流量1',
      `flow_rate_2` INT COMMENT '流量2',
      `load_1` DECIMAL(10,2) COMMENT '负荷1',
      `load_2` DECIMAL(10,2) COMMENT '负荷2',
      `load_3` DECIMAL(10,2) COMMENT '负荷3',
      `device_id` VARCHAR(50) DEFAULT 'sensor_001' COMMENT '设备ID',
      `location` VARCHAR(100) DEFAULT '生产车间' COMMENT '位置',
      `status` VARCHAR(20) DEFAULT 'normal' COMMENT '状态',
      `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
      INDEX `idx_timestamp` (`timestamp`),
      INDEX `idx_device_id` (`device_id`),
      INDEX `idx_temperature` (`temperature`),
      INDEX `idx_pressure` (`pressure`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='传感器数据表(MCP兼容)';
    """
    
    try:
        connection = pymysql.connect(**MYSQL_CONFIG)
        with connection.cursor() as cursor:
            # 创建新表
            cursor.execute(create_sql)
            logger.info("✅ 新表结构创建成功")
            
        connection.commit()
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"创建新表失败: {e}")
        return False

def migrate_data():
    """迁移数据到新表"""
    logger.info("开始数据迁移")
    
    # 数据迁移SQL
    migrate_sql = """
    INSERT INTO sensor_data_new (
        timestamp, temperature, temperature_2, temperature_3, temperature_4, temperature_5, temperature_6,
        pressure, pressure_2, pressure_3, pressure_4, pressure_5, pressure_7,
        oxygen_percent, flow_rate, flow_rate_2, load_1, load_2, load_3
    )
    SELECT 
        datetime as timestamp,
        `温度1_` as temperature,
        `温度2_` as temperature_2,
        `温度3_` as temperature_3,
        `温度4_` as temperature_4,
        `温度5_` as temperature_5,
        `温度6_` as temperature_6,
        `1压力mpa` as pressure,
        `压力2mpa` as pressure_2,
        `压力3mpa` as pressure_3,
        `压力4mpa` as pressure_4,
        `压力5mpa` as pressure_5,
        `压力7mpa` as pressure_7,
        `含氧_` as oxygen_percent,
        `流量m3_h` as flow_rate,
        `流量2m3_h` as flow_rate_2,
        `负荷1_` as load_1,
        `负荷2_` as load_2,
        `负荷3_` as load_3
    FROM sensor_data
    """
    
    try:
        connection = pymysql.connect(**MYSQL_CONFIG)
        with connection.cursor() as cursor:
            # 执行数据迁移
            cursor.execute(migrate_sql)
            affected_rows = cursor.rowcount
            logger.info(f"✅ 数据迁移成功，迁移了 {affected_rows} 行数据")
            
        connection.commit()
        connection.close()
        return affected_rows
        
    except Exception as e:
        logger.error(f"数据迁移失败: {e}")
        return 0

def replace_old_table():
    """替换旧表"""
    logger.info("替换旧表")
    
    try:
        connection = pymysql.connect(**MYSQL_CONFIG)
        with connection.cursor() as cursor:
            # 备份旧表
            cursor.execute("DROP TABLE IF EXISTS sensor_data_backup")
            cursor.execute("RENAME TABLE sensor_data TO sensor_data_backup")
            logger.info("✅ 旧表已备份为 sensor_data_backup")
            
            # 将新表重命名为主表
            cursor.execute("RENAME TABLE sensor_data_new TO sensor_data")
            logger.info("✅ 新表已设置为主表 sensor_data")
            
        connection.commit()
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"替换表失败: {e}")
        return False

def verify_data():
    """验证数据"""
    logger.info("验证迁移后的数据")
    
    try:
        connection = pymysql.connect(**MYSQL_CONFIG)
        with connection.cursor() as cursor:
            # 检查记录数
            cursor.execute("SELECT COUNT(*) FROM sensor_data")
            count = cursor.fetchone()[0]
            logger.info(f"📊 sensor_data 表中共有 {count} 条记录")
            
            # 检查列结构
            cursor.execute("DESCRIBE sensor_data")
            columns = cursor.fetchall()
            logger.info("📋 表结构:")
            for col in columns:
                logger.info(f"  {col[0]} - {col[1]} - {col[4] if col[4] else ''}")
            
            # 检查示例数据
            cursor.execute("SELECT * FROM sensor_data LIMIT 3")
            sample_data = cursor.fetchall()
            logger.info("📝 示例数据:")
            for i, row in enumerate(sample_data, 1):
                logger.info(f"  记录 {i}: ID={row[0]}, 时间={row[1]}, 温度={row[2]}, 压力={row[8]}")
            
        connection.close()
        return True
        
    except Exception as e:
        logger.error(f"验证数据失败: {e}")
        return False

def main():
    """主函数"""
    logger.info("🚀 开始表结构更新和数据迁移")
    
    # 1. 创建新表
    if not create_mcp_compatible_table():
        logger.error("❌ 创建新表失败，停止迁移")
        return
    
    # 2. 迁移数据
    migrated_rows = migrate_data()
    if migrated_rows == 0:
        logger.error("❌ 数据迁移失败，停止操作")
        return
    
    # 3. 替换旧表
    if not replace_old_table():
        logger.error("❌ 替换表失败")
        return
    
    # 4. 验证数据
    if verify_data():
        logger.info("🎉 表结构更新和数据迁移完成！")
        logger.info("✅ 现在数据库与MCP服务器完全兼容")
        logger.info("📊 可以使用AI分析功能了")
    else:
        logger.error("❌ 数据验证失败")

if __name__ == "__main__":
    main()
