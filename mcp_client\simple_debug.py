#!/usr/bin/env python3
"""
简化版调试前端
专门用于快速测试MCP连接
"""

import streamlit as st
import asyncio
import json
import traceback
from datetime import datetime
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

try:
    from fastmcp import Client
    from fastmcp.client.transports import StreamableHttpTransport
    st.success("✅ FastMCP导入成功")
except Exception as e:
    st.error(f"❌ FastMCP导入失败: {e}")

# 页面配置
st.set_page_config(
    page_title="MCP简化调试",
    page_icon="🔧",
    layout="wide"
)

st.title("🔧 MCP简化调试界面")
st.write("专门用于快速测试MCP连接和工具调用")

# 全局变量
if 'client' not in st.session_state:
    st.session_state.client = None
if 'logs' not in st.session_state:
    st.session_state.logs = []

def add_log(message, level="INFO"):
    """添加日志"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    log_entry = f"[{timestamp}] {level}: {message}"
    st.session_state.logs.append(log_entry)
    
    if level == "ERROR":
        st.error(f"🔴 {message}")
    elif level == "SUCCESS":
        st.success(f"🟢 {message}")
    elif level == "WARNING":
        st.warning(f"🟡 {message}")
    else:
        st.info(f"🔵 {message}")

# 基础信息
st.subheader("📋 基础信息")
col1, col2 = st.columns(2)

with col1:
    st.info("🌐 MCP服务器: http://127.0.0.1:8000/mcp/")
    st.info(f"⏰ 当前时间: {datetime.now().strftime('%H:%M:%S')}")

with col2:
    st.info(f"🐍 Python: {sys.version.split()[0]}")
    st.info(f"📁 工作目录: {Path.cwd().name}")

st.markdown("---")

# 连接测试
st.subheader("🔗 连接测试")

col1, col2, col3 = st.columns(3)

with col1:
    if st.button("🔧 创建客户端", type="primary"):
        try:
            add_log("正在创建MCP客户端...")
            transport = StreamableHttpTransport(url="http://127.0.0.1:8000/mcp/")
            st.session_state.client = Client(transport)
            add_log("MCP客户端创建成功", "SUCCESS")
        except Exception as e:
            add_log(f"客户端创建失败: {str(e)}", "ERROR")

with col2:
    if st.button("🔍 测试连接"):
        if st.session_state.client:
            try:
                add_log("开始测试连接...")
                
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                async def test_ping():
                    async with st.session_state.client:
                        await st.session_state.client.ping()
                        return True
                
                result = loop.run_until_complete(test_ping())
                loop.close()
                
                if result:
                    add_log("连接测试成功", "SUCCESS")
                else:
                    add_log("连接测试失败", "ERROR")
                    
            except Exception as e:
                add_log(f"连接测试异常: {str(e)}", "ERROR")
        else:
            add_log("请先创建客户端", "WARNING")

with col3:
    if st.button("🛠️ 获取工具"):
        if st.session_state.client:
            try:
                add_log("正在获取工具列表...")
                
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                async def get_tools():
                    async with st.session_state.client:
                        tools = await st.session_state.client.list_tools()
                        return tools
                
                tools = loop.run_until_complete(get_tools())
                loop.close()
                
                if tools:
                    add_log(f"成功获取 {len(tools)} 个工具", "SUCCESS")
                    st.session_state.tools = tools
                    
                    # 显示工具列表
                    st.write("**可用工具:**")
                    for i, tool in enumerate(tools[:5]):  # 显示前5个
                        st.write(f"{i+1}. {tool.name}")
                else:
                    add_log("未获取到工具", "WARNING")
                    
            except Exception as e:
                add_log(f"获取工具失败: {str(e)}", "ERROR")
        else:
            add_log("请先创建客户端", "WARNING")

st.markdown("---")

# 快速测试
st.subheader("🚀 快速测试")

test_col1, test_col2 = st.columns(2)

with test_col1:
    if st.button("📊 测试系统状态"):
        if st.session_state.client:
            try:
                add_log("调用 get_system_status...")
                
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                async def call_status():
                    async with st.session_state.client:
                        result = await st.session_state.client.call_tool("get_system_status", {})
                        return result
                
                result = loop.run_until_complete(call_status())
                loop.close()
                
                if result:
                    add_log("系统状态获取成功", "SUCCESS")
                    st.json(result)
                else:
                    add_log("系统状态获取失败", "ERROR")
                    
            except Exception as e:
                add_log(f"系统状态测试失败: {str(e)}", "ERROR")
        else:
            add_log("请先创建客户端", "WARNING")

with test_col2:
    if st.button("🔍 测试数据查询"):
        if st.session_state.client:
            try:
                add_log("调用统计分析工具...")
                
                args = {
                    "start_time": "2024-08-01 00:00:00",
                    "end_time": "2024-08-03 00:00:00",
                    "columns": ["temperature"],
                    "operation": "count"
                }
                
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                async def call_analysis():
                    async with st.session_state.client:
                        result = await st.session_state.client.call_tool("advanced_statistical_analysis", args)
                        return result
                
                result = loop.run_until_complete(call_analysis())
                loop.close()
                
                if result:
                    add_log("数据查询成功", "SUCCESS")
                    st.json(result)
                else:
                    add_log("数据查询失败", "ERROR")
                    
            except Exception as e:
                add_log(f"数据查询测试失败: {str(e)}", "ERROR")
        else:
            add_log("请先创建客户端", "WARNING")

st.markdown("---")

# 示例问题
st.subheader("💡 示例问题")

examples = [
    {
        "title": "📊 查看系统状态",
        "description": "检查MCP服务器和数据库连接状态",
        "tool": "get_system_status",
        "args": {}
    },
    {
        "title": "📈 统计数据总量",
        "description": "统计数据库中的记录总数",
        "tool": "advanced_statistical_analysis",
        "args": {
            "start_time": "2020-01-01 00:00:00",
            "end_time": "2030-01-01 00:00:00",
            "columns": ["temperature"],
            "operation": "count"
        }
    },
    {
        "title": "🔍 异常检测",
        "description": "检测温度数据中的异常值",
        "tool": "intelligent_anomaly_detection",
        "args": {
            "column": "temperature",
            "method": "hybrid",
            "time_window": "24h",
            "include_reasons": True
        }
    }
]

for i, example in enumerate(examples):
    with st.expander(f"{example['title']} - {example['description']}"):
        st.code(f"工具: {example['tool']}")
        st.code(f"参数: {json.dumps(example['args'], indent=2, ensure_ascii=False)}")
        
        if st.button(f"🚀 执行", key=f"example_{i}"):
            if st.session_state.client:
                try:
                    add_log(f"执行示例: {example['title']}")
                    
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    
                    async def call_example():
                        async with st.session_state.client:
                            result = await st.session_state.client.call_tool(example['tool'], example['args'])
                            return result
                    
                    result = loop.run_until_complete(call_example())
                    loop.close()
                    
                    if result:
                        add_log(f"示例执行成功: {example['title']}", "SUCCESS")
                        st.json(result)
                    else:
                        add_log(f"示例执行失败: {example['title']}", "ERROR")
                        
                except Exception as e:
                    add_log(f"示例执行异常: {str(e)}", "ERROR")
            else:
                add_log("请先创建客户端", "WARNING")

st.markdown("---")

# 日志显示
st.subheader("📝 调试日志")

if st.button("🗑️ 清空日志"):
    st.session_state.logs.clear()
    st.rerun()

if st.session_state.logs:
    log_text = "\n".join(st.session_state.logs[-20:])  # 显示最近20条
    st.text_area("日志输出", log_text, height=200)
    
    # 日志统计
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        info_count = len([log for log in st.session_state.logs if "INFO" in log])
        st.metric("信息", info_count)
    with col2:
        success_count = len([log for log in st.session_state.logs if "SUCCESS" in log])
        st.metric("成功", success_count)
    with col3:
        warning_count = len([log for log in st.session_state.logs if "WARNING" in log])
        st.metric("警告", warning_count)
    with col4:
        error_count = len([log for log in st.session_state.logs if "ERROR" in log])
        st.metric("错误", error_count)
else:
    st.info("暂无调试日志")

# 帮助信息
with st.expander("❓ 使用帮助"):
    st.markdown("""
    **使用步骤:**
    1. 确保MCP服务器正在运行 (http://127.0.0.1:8000/mcp/)
    2. 点击"创建客户端"
    3. 点击"测试连接"
    4. 点击"获取工具"
    5. 尝试快速测试或示例问题
    
    **故障排除:**
    - 如果连接失败，检查MCP服务器是否运行
    - 如果工具调用失败，查看日志中的详细错误信息
    - 如果数据查询无结果，可能是时间范围内没有数据
    """)
