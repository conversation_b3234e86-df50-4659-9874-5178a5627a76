#!/usr/bin/env python3
"""
复制核心文件到production目录
"""

import os
import shutil
from pathlib import Path

def copy_core_files():
    """复制核心文件"""
    
    # 确保目录存在
    Path('production/mcp_client').mkdir(parents=True, exist_ok=True)
    
    # 核心文件映射
    core_files = {
        # 主服务器
        'enterprise_database_mcp_server.py': 'production/enterprise_database_mcp_server.py',
        
        # 客户端文件
        'mcp_client/simple_http_server.py': 'production/mcp_client/simple_http_server.py',
        'mcp_client/enterprise_ai_frontend.py': 'production/mcp_client/enterprise_ai_frontend.py',
        'mcp_client/client.py': 'production/mcp_client/client.py',
        'mcp_client/config.py': 'production/mcp_client/config.py',
        
        # 依赖文件
        'requirements.txt': 'production/requirements.txt',
        'enterprise_requirements.txt': 'production/enterprise_requirements.txt',
        'mcp_client/requirements.txt': 'production/mcp_client/requirements.txt',
    }
    
    print("📂 复制核心文件到production目录...")
    
    for src_path, dst_path in core_files.items():
        src = Path(src_path)
        dst = Path(dst_path)
        
        if src.exists():
            try:
                # 确保目标目录存在
                dst.parent.mkdir(parents=True, exist_ok=True)
                shutil.copy2(src, dst)
                print(f"✅ 复制: {src} -> {dst}")
            except Exception as e:
                print(f"❌ 复制失败: {src} - {e}")
        else:
            print(f"⚠️ 源文件不存在: {src}")

def verify_production_structure():
    """验证production目录结构"""
    
    print("\n🔍 验证production目录结构...")
    
    required_files = [
        'production/enterprise_database_mcp_server.py',
        'production/mcp_client/simple_http_server.py',
        'production/mcp_client/enterprise_ai_frontend.py',
        'production/start_system.py',
        'production/start_system.bat',
        'production/README.md',
        'production/requirements.txt',
    ]
    
    all_good = True
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ 缺失: {file_path}")
            all_good = False
    
    if all_good:
        print("\n🎉 production目录结构完整!")
    else:
        print("\n⚠️ production目录结构不完整，请检查缺失文件")
    
    return all_good

def create_quick_start_guide():
    """创建快速启动指南"""
    
    guide_content = """# 🚀 快速启动指南

## 📋 启动步骤

### 方法1：一键启动（推荐）

**Windows用户：**
```bash
cd production
start_system.bat
```

**Python用户：**
```bash
cd production
python start_system.py
```

### 方法2：手动启动

**第1步：启动MCP服务器**
```bash
cd production
python enterprise_database_mcp_server.py
```

**第2步：启动HTTP服务器（新终端）**
```bash
cd production
python mcp_client/simple_http_server.py
```

**第3步：启动前端界面（新终端）**
```bash
cd production
streamlit run mcp_client/enterprise_ai_frontend.py --server.port=8501
```

## 🌐 访问地址

启动成功后，在浏览器中访问：
- **前端界面**: http://localhost:8501
- **MCP服务器**: http://127.0.0.1:8000/mcp/

## 🛠️ 系统要求

- Python 3.8+
- MySQL 5.7+ 或 8.0+
- 8GB+ 内存（推荐）

## 📦 依赖安装

如果是首次运行，请先安装依赖：

```bash
cd production
pip install -r requirements.txt
```

## 🔧 故障排除

**常见问题：**

1. **端口被占用**
   - 检查8501和8000端口是否被占用
   - 使用 `netstat -ano | findstr :8501` 查看端口占用

2. **数据库连接失败**
   - 确保MySQL服务正在运行
   - 检查数据库配置信息

3. **依赖包缺失**
   - 重新安装依赖：`pip install -r requirements.txt`

4. **权限问题**
   - 以管理员身份运行命令提示符

## 📞 技术支持

如果遇到问题，请检查：
- Python版本是否正确
- 所有依赖是否安装完整
- 数据库服务是否正常运行
- 防火墙设置是否正确

更多详细信息请查看 docs/ 目录中的完整文档。
"""
    
    with open('production/QUICK_START.md', 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print("✅ 创建快速启动指南: production/QUICK_START.md")

if __name__ == "__main__":
    print("🔧 开始复制核心文件...")
    
    copy_core_files()
    create_quick_start_guide()
    
    if verify_production_structure():
        print("\n" + "=" * 60)
        print("🎉 生产环境准备完成!")
        print("=" * 60)
        print("📁 核心文件位置: production/")
        print("🚀 快速启动命令:")
        print("   cd production")
        print("   start_system.bat  (Windows)")
        print("   python start_system.py  (跨平台)")
        print("\n📍 启动后访问: http://localhost:8501")
    else:
        print("\n❌ 生产环境准备失败，请检查错误信息")
