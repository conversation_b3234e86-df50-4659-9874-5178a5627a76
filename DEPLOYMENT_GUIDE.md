# 企业级数据库分析MCP系统部署指南

## 🎯 系统架构概览

```
┌─────────────────────────────────────────────────────────────────┐
│                    完全本地化部署架构                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────┐    MCP协议    ┌─────────────────────────┐   │
│  │   本地LLM客户端   │ ←----------→ │  企业级MCP数据分析服务器   │   │
│  │                │              │                         │   │
│  │ • Ollama       │              │ • 大数据量实时处理        │   │
│  │ • Open WebUI   │              │ • 智能异常检测           │   │
│  │ • 语音识别      │              │ • 实时提醒系统           │   │
│  │ • 语音合成      │              │ • 高性能可视化           │   │
│  │ • 中文对话      │              │ • 趋势分析预测           │   │
│  └─────────────────┘              │ • 语音交互支持           │   │
│                                   └─────────────────────────┘   │
│                                             │                   │
│                                             ▼                   │
│                                   ┌─────────────────────────┐   │
│                                   │      MySQL数据库         │   │
│                                   │                         │   │
│                                   │ • 连接池优化             │   │
│                                   │ • 分区表设计             │   │
│                                   │ • 索引策略               │   │
│                                   │ • 实时数据流             │   │
│                                   └─────────────────────────┘   │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 🚀 快速部署步骤

### 第一步：环境准备

1. **系统要求**
   ```
   操作系统: Windows 10/11, Linux, macOS
   Python: 3.8+
   MySQL: 8.0+
   内存: 8GB+ (推荐16GB)
   存储: 50GB+ 可用空间
   ```

2. **安装Python依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置环境变量**
   ```bash
   # 复制并编辑配置文件
   cp .env.example .env
   # 修改数据库密码等配置
   ```

### 第二步：数据库设置

1. **创建数据库**
   ```bash
   mysql -u root -p12369874b -e "source create_db_minimal.sql"
   ```

2. **验证数据库**
   ```bash
   python test_direct.py
   ```

### 第三步：本地LLM部署

#### 方案A：Ollama + Open WebUI（推荐）

1. **安装Ollama**
   ```bash
   # Windows
   winget install Ollama.Ollama
   
   # Linux/macOS
   curl -fsSL https://ollama.ai/install.sh | sh
   ```

2. **下载中文大模型**
   ```bash
   # 推荐模型（按性能排序）
   ollama pull qwen2.5:14b      # 最佳性能，需要16GB内存
   ollama pull qwen2.5:7b       # 平衡性能，需要8GB内存
   ollama pull qwen2.5:3b       # 轻量级，需要4GB内存
   
   # 或者使用其他中文模型
   ollama pull chatglm3:6b
   ollama pull baichuan2:7b
   ```

3. **安装Open WebUI**
   ```bash
   # 使用Docker（推荐）
   docker run -d -p 3000:8080 --add-host=host.docker.internal:host-gateway \
     -v open-webui:/app/backend/data --name open-webui \
     --restart always ghcr.io/open-webui/open-webui:main
   
   # 或使用pip安装
   pip install open-webui
   open-webui serve
   ```

4. **配置MCP连接**
   - 打开 Open WebUI (http://localhost:3000)
   - 进入设置 → 连接 → MCP服务器
   - 添加服务器：`python enterprise_database_mcp_server.py`

#### 方案B：Claude Desktop（备选）

1. **安装MCP服务器**
   ```bash
   fastmcp install claude-desktop enterprise_database_mcp_server.py \
     --server-name "企业级数据库分析助手" \
     --env-file .env
   ```

2. **重启Claude Desktop**

### 第四步：系统测试

1. **功能测试**
   ```bash
   python test_direct.py
   ```

2. **MCP服务器测试**
   ```bash
   python enterprise_database_mcp_server.py
   ```

3. **语音功能测试**（可选）
   - 录制测试音频
   - 测试语音识别和合成

## 🔧 高级配置

### 大数据量优化

1. **MySQL配置优化**
   ```sql
   -- 在my.cnf中添加
   [mysqld]
   innodb_buffer_pool_size = 4G
   innodb_log_file_size = 1G
   max_connections = 200
   query_cache_size = 256M
   tmp_table_size = 256M
   max_heap_table_size = 256M
   ```

2. **分区表设置**
   ```sql
   -- 按月分区（适用于大数据量）
   ALTER TABLE sensor_data PARTITION BY RANGE (YEAR(timestamp)*100 + MONTH(timestamp)) (
       PARTITION p202401 VALUES LESS THAN (202402),
       PARTITION p202402 VALUES LESS THAN (202403),
       -- 继续添加分区...
   );
   ```

3. **索引优化**
   ```sql
   -- 复合索引
   CREATE INDEX idx_timestamp_device ON sensor_data(timestamp, device_id);
   CREATE INDEX idx_timestamp_column ON sensor_data(timestamp, temperature, pressure);
   ```

### 性能监控

1. **启用性能监控**
   ```bash
   # 在.env中设置
   PROMETHEUS_ENABLED=true
   PROMETHEUS_PORT=9090
   ```

2. **监控指标**
   - 查询响应时间
   - 数据库连接池状态
   - 内存使用情况
   - 异常检测准确率

### 安全配置

1. **数据库安全**
   ```sql
   -- 创建专用用户
   CREATE USER 'mcp_user'@'localhost' IDENTIFIED BY 'secure_password';
   GRANT SELECT, INSERT, UPDATE ON sensor_data.* TO 'mcp_user'@'localhost';
   ```

2. **网络安全**
   ```bash
   # 在.env中设置
   ALLOWED_IPS=127.0.0.1,localhost
   API_KEY=your_secure_api_key
   ```

## 🎤 语音功能配置

### Windows语音配置

1. **安装语音包**
   - 设置 → 时间和语言 → 语音
   - 下载中文语音包

2. **配置语音引擎**
   ```python
   # 在.env中设置
   VOICE_LANGUAGE=zh-CN
   SPEECH_RATE=150
   SPEECH_VOLUME=0.9
   ```

### Linux语音配置

1. **安装依赖**
   ```bash
   sudo apt-get install espeak espeak-data libespeak1 libespeak-dev
   sudo apt-get install festival festvox-kallpc16k
   ```

2. **配置音频**
   ```bash
   sudo apt-get install pulseaudio alsa-utils
   ```

## 📊 使用示例

### 基本查询
```
"分析过去24小时的温度平均值"
"检测压力异常数据，使用混合算法"
"生成温湿度对比柱状图"
```

### 高级分析
```
"预测未来3天的温度趋势，使用集成方法"
"创建温度超过50度的高级提醒规则"
"优化数据库性能并生成报告"
```

### 语音交互
```
"语音查询：过去一周的设备运行状态"
"播报当前系统异常警报"
"语音生成数据分析报告"
```

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   ```bash
   # 检查MySQL服务
   systemctl status mysql
   
   # 测试连接
   mysql -u root -p12369874b -e "SELECT 1"
   ```

2. **MCP连接问题**
   ```bash
   # 检查端口占用
   netstat -an | grep 3000
   
   # 重启服务
   pkill -f enterprise_database_mcp_server
   python enterprise_database_mcp_server.py
   ```

3. **语音功能异常**
   ```bash
   # 检查音频设备
   arecord -l
   aplay -l
   
   # 测试语音识别
   python -c "import speech_recognition as sr; print('语音识别可用')"
   ```

4. **性能问题**
   ```bash
   # 检查系统资源
   top
   free -h
   df -h
   
   # 优化数据库
   python -c "
   import asyncio
   from enterprise_database_mcp_server import optimize_database_performance
   asyncio.run(optimize_database_performance())
   "
   ```

### 日志分析

1. **查看系统日志**
   ```bash
   tail -f mcp_server.log
   ```

2. **MySQL慢查询日志**
   ```sql
   SET GLOBAL slow_query_log = 'ON';
   SET GLOBAL long_query_time = 1;
   ```

## 📈 扩展功能

### 添加新的数据源
```python
# 在enterprise_database_mcp_server.py中添加
@mcp.tool
async def analyze_custom_data_source(
    source_type: str,
    connection_params: Dict[str, Any]
) -> Dict[str, Any]:
    # 实现自定义数据源分析
    pass
```

### 集成外部API
```python
# 添加外部系统集成
@mcp.tool
async def integrate_external_system(
    system_name: str,
    api_endpoint: str,
    auth_token: str
) -> str:
    # 实现外部系统集成
    pass
```

## 🎯 最佳实践

1. **数据备份策略**
   - 每日自动备份
   - 增量备份机制
   - 异地备份存储

2. **监控告警**
   - 设置关键指标监控
   - 配置邮件/短信告警
   - 建立故障响应流程

3. **性能优化**
   - 定期清理历史数据
   - 优化查询语句
   - 监控系统资源使用

4. **安全管理**
   - 定期更新密码
   - 限制网络访问
   - 审计日志记录

## 🆘 技术支持

如遇到问题，请：
1. 查看日志文件 `mcp_server.log`
2. 运行诊断脚本 `python test_direct.py`
3. 检查系统资源使用情况
4. 参考故障排除章节

---

**部署完成后，您将拥有一个完全本地化的企业级数据库分析系统，支持语音交互、实时监控、智能分析等功能！** 🎉
