2025-08-01 14:04:44,950 - __main__ - ERROR - 数据库连接池初始化失败: 1045 (28000): Access denied for user 'root'@'**********' (using password: NO)
2025-08-02 13:07:41,637 - __main__ - ERROR - 数据库连接池初始化失败: 2003 (HY000): Can't connect to MySQL server on 'localhost:3306' (10061)
2025-08-02 13:09:11,653 - __main__ - ERROR - 数据库连接池初始化失败: 1045 (28000): Access denied for user 'root'@'**********' (using password: NO)
2025-08-02 13:14:25,687 - __main__ - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 13:14:25,688 - __main__ - INFO - 密码长度: 6
2025-08-02 13:14:39,297 - __main__ - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 13:14:39,423 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'D:\\Program Files\\conda\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-02 13:14:39,426 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'D:\Program Files\conda\Lib\site-packages\comtypes\gen'
2025-08-02 13:14:39,876 - __main__ - INFO - 语音引擎初始化成功
2025-08-02 13:35:30,257 - __main__ - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 13:35:30,258 - __main__ - INFO - 密码长度: 6
2025-08-02 13:35:46,200 - __main__ - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 13:35:46,671 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'D:\\Program Files\\conda\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-02 13:35:46,778 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'D:\Program Files\conda\Lib\site-packages\comtypes\gen'
2025-08-02 13:35:48,505 - __main__ - INFO - 语音引擎初始化成功
2025-08-02 13:43:20,533 - __main__ - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 13:43:20,534 - __main__ - INFO - 密码长度: 6
2025-08-02 13:43:33,869 - __main__ - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 13:43:33,975 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'D:\\Program Files\\conda\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-02 13:43:33,976 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'D:\Program Files\conda\Lib\site-packages\comtypes\gen'
2025-08-02 13:43:34,328 - __main__ - ERROR - 语音引擎初始化失败: Could not find PyAudio; check installation
2025-08-02 14:03:50,717 - __main__ - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 14:03:50,721 - __main__ - INFO - 密码长度: 6
2025-08-02 14:04:05,267 - __main__ - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 14:04:05,374 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'D:\\Program Files\\conda\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-02 14:04:05,381 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'D:\Program Files\conda\Lib\site-packages\comtypes\gen'
2025-08-02 14:04:05,732 - __main__ - INFO - 语音引擎初始化成功
2025-08-02 14:04:58,324 - __main__ - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 14:04:58,329 - __main__ - INFO - 密码长度: 6
2025-08-02 14:05:12,766 - __main__ - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 14:05:12,877 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'D:\\Program Files\\conda\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-02 14:05:12,884 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'D:\Program Files\conda\Lib\site-packages\comtypes\gen'
2025-08-02 14:05:13,272 - __main__ - INFO - 语音引擎初始化成功
2025-08-02 14:09:46,999 - __main__ - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 14:09:47,000 - __main__ - INFO - 密码长度: 6
2025-08-02 14:10:01,715 - __main__ - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 14:10:01,857 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'D:\\Program Files\\conda\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-02 14:10:01,858 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'D:\Program Files\conda\Lib\site-packages\comtypes\gen'
2025-08-02 14:10:02,194 - __main__ - ERROR - 语音引擎初始化失败: Could not find PyAudio; check installation
2025-08-02 14:18:49,366 - __main__ - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 14:18:49,367 - __main__ - INFO - 密码长度: 6
2025-08-02 14:19:03,859 - __main__ - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 14:19:03,969 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'D:\\Program Files\\conda\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-02 14:19:03,970 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'D:\Program Files\conda\Lib\site-packages\comtypes\gen'
2025-08-02 14:19:04,346 - __main__ - INFO - 语音引擎初始化成功
2025-08-02 14:39:25,718 - __main__ - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 14:39:25,719 - __main__ - INFO - 密码长度: 6
2025-08-02 14:39:40,180 - __main__ - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 14:39:40,263 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'D:\\Program Files\\conda\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-02 14:39:40,265 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'D:\Program Files\conda\Lib\site-packages\comtypes\gen'
2025-08-02 14:39:40,559 - __main__ - INFO - 语音引擎初始化成功
2025-08-02 14:42:49,122 - __main__ - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 14:42:49,122 - __main__ - INFO - 密码长度: 6
2025-08-02 14:43:58,442 - __main__ - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 14:43:58,443 - __main__ - INFO - 密码长度: 6
2025-08-02 14:44:11,998 - __main__ - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 14:44:12,128 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'D:\\Program Files\\conda\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-02 14:44:12,129 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'D:\Program Files\conda\Lib\site-packages\comtypes\gen'
2025-08-02 14:44:12,465 - __main__ - INFO - 语音引擎初始化成功
2025-08-02 17:07:02,382 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
2025-08-02 17:11:48,601 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 429539a0c3624f9daf1843282ce80b23
2025-08-02 17:11:48,633 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 17:11:48,636 - server - INFO - 获取系统状态
2025-08-02 17:11:48,636 - server - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 17:11:48,637 - server - INFO - 密码长度: 6
2025-08-02 17:12:03,312 - server - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 17:12:03,663 - server - INFO - 查询完成: 1 行, 耗时 0.34s
2025-08-02 17:12:03,675 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 17:12:03,692 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 17:12:03,706 - mcp.server.streamable_http - INFO - Terminating session: 429539a0c3624f9daf1843282ce80b23
2025-08-02 17:14:18,848 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager shutting down
2025-08-02 17:21:23,863 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
2025-08-02 17:26:04,493 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 68ed57d1d7cf4dee9c665182a7b5d578
2025-08-02 17:26:04,541 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 17:26:04,551 - server - INFO - 开始智能异常检测: 列=temperature, 方法=hybrid, 时间窗口=24h
2025-08-02 17:26:04,551 - server - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 17:26:04,551 - server - INFO - 密码长度: 6
2025-08-02 17:26:18,593 - server - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 17:26:18,967 - server - INFO - 查询完成: 0 行, 耗时 14.42s
2025-08-02 17:26:18,971 - server - WARNING - 在时间窗口 24h 内没有找到列 temperature 的有效数据
2025-08-02 17:26:18,995 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 17:26:19,041 - mcp.server.streamable_http - INFO - Terminating session: 68ed57d1d7cf4dee9c665182a7b5d578
2025-08-02 17:26:40,227 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 14d237f664984480a245a0a6578c91e2
2025-08-02 17:26:40,256 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 17:26:40,278 - server - INFO - 开始高级统计分析: average 操作，时间范围 2024-01-01 00:00:00 到 2024-01-02 00:00:00
2025-08-02 17:26:40,287 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 17:26:40,293 - server - WARNING - 列 temperature 在指定条件下无有效数据
2025-08-02 17:26:40,300 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 17:26:40,305 - server - WARNING - 列 voltage 在指定条件下无有效数据
2025-08-02 17:26:40,313 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 17:26:40,317 - server - WARNING - 列 power 在指定条件下无有效数据
2025-08-02 17:26:40,318 - server - INFO - 统计分析完成，总耗时: 0.04s
2025-08-02 17:26:40,345 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 17:26:40,368 - mcp.server.streamable_http - INFO - Terminating session: 14d237f664984480a245a0a6578c91e2
2025-08-02 17:28:56,965 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 80283e1b2df54360b3bb31d3f0ba7516
2025-08-02 17:28:56,986 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 17:28:56,987 - server - INFO - 获取系统状态
2025-08-02 17:28:57,000 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 17:28:57,011 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 17:28:57,026 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 17:28:57,038 - mcp.server.streamable_http - INFO - Terminating session: 80283e1b2df54360b3bb31d3f0ba7516
2025-08-02 17:29:07,358 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 8d4730c27a724a89a8cade499e0df2fe
2025-08-02 17:29:07,385 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 17:29:07,396 - mcp.server.streamable_http - INFO - Terminating session: 8d4730c27a724a89a8cade499e0df2fe
2025-08-02 17:35:26,411 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager shutting down
2025-08-02 17:35:41,027 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
2025-08-02 17:40:51,349 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 5878a52302f74f10ad8233afdeb5cc7d
2025-08-02 17:40:51,378 - mcp.server.lowlevel.server - INFO - Processing request of type PingRequest
2025-08-02 17:40:51,386 - mcp.server.streamable_http - INFO - Terminating session: 5878a52302f74f10ad8233afdeb5cc7d
2025-08-02 17:40:57,560 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 11de437cad5946e09b5633b6ac18df72
2025-08-02 17:40:57,591 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 17:40:57,603 - mcp.server.streamable_http - INFO - Terminating session: 11de437cad5946e09b5633b6ac18df72
2025-08-02 17:41:05,828 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: abf00f05086c4895be8015400b817085
2025-08-02 17:41:05,861 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 17:41:05,864 - server - INFO - 获取系统状态
2025-08-02 17:41:05,865 - server - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 17:41:05,865 - server - INFO - 密码长度: 6
2025-08-02 17:41:20,958 - server - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 17:41:21,326 - server - INFO - 查询完成: 1 行, 耗时 0.36s
2025-08-02 17:41:21,340 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 17:41:21,356 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 17:41:21,371 - mcp.server.streamable_http - INFO - Terminating session: abf00f05086c4895be8015400b817085
2025-08-02 17:41:27,638 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 59a6e36699e54a8c9383211859954d96
2025-08-02 17:41:27,666 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 17:41:27,682 - server - INFO - 开始高级统计分析: count 操作，时间范围 2024-08-01 00:00:00 到 2024-08-03 00:00:00
2025-08-02 17:41:27,690 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 17:41:27,696 - server - INFO - 列 temperature 的 count 结果: 0.0
2025-08-02 17:41:27,696 - server - INFO - 统计分析完成，总耗时: 0.01s
2025-08-02 17:41:27,719 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 17:41:27,749 - mcp.server.streamable_http - INFO - Terminating session: 59a6e36699e54a8c9383211859954d96
2025-08-02 17:41:38,148 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 8077c109c389449082c1f042d06cc5d9
2025-08-02 17:41:38,191 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 17:41:38,193 - server - INFO - 获取系统状态
2025-08-02 17:41:38,216 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 17:41:38,231 - mcp.server.streamable_http - INFO - Terminating session: 8077c109c389449082c1f042d06cc5d9
2025-08-02 17:41:43,395 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 5f0231bc59ea47339a607977be9e9d54
2025-08-02 17:41:43,422 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 17:41:43,438 - server - INFO - 开始高级统计分析: count 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 17:41:43,446 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 17:41:43,451 - server - INFO - 列 temperature 的 count 结果: 18.0
2025-08-02 17:41:43,452 - server - INFO - 统计分析完成，总耗时: 0.01s
2025-08-02 17:41:43,480 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 17:41:43,512 - mcp.server.streamable_http - INFO - Terminating session: 5f0231bc59ea47339a607977be9e9d54
2025-08-02 17:48:30,955 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager shutting down
2025-08-02 17:49:16,945 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
2025-08-02 17:50:04,976 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 5cfb82e233314f558763a66e7b97c25d
2025-08-02 17:50:05,022 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 17:50:05,049 - server - INFO - 开始高级统计分析: count 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 17:50:05,050 - server - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 17:50:05,050 - server - INFO - 密码长度: 6
2025-08-02 17:50:19,567 - server - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 17:50:19,914 - server - INFO - 查询完成: 1 行, 耗时 14.86s
2025-08-02 17:50:19,919 - server - INFO - 列 temperature 的 count 结果: 18.0
2025-08-02 17:50:19,920 - server - INFO - 统计分析完成，总耗时: 14.87s
2025-08-02 17:50:19,943 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 17:50:19,968 - mcp.server.streamable_http - INFO - Terminating session: 5cfb82e233314f558763a66e7b97c25d
2025-08-02 17:51:08,489 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 36321862a01d448ba7c65c63758cdc12
2025-08-02 17:51:08,515 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 17:51:08,530 - mcp.server.streamable_http - INFO - Terminating session: 36321862a01d448ba7c65c63758cdc12
2025-08-02 17:51:13,202 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: ff0fd53895c24bd3b3582ff3823f4bd4
2025-08-02 17:51:13,234 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 17:51:13,252 - server - INFO - 开始高级统计分析: average 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 17:51:13,260 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 17:51:13,265 - server - INFO - 列 temperature 的 average 结果: 28.961111
2025-08-02 17:51:13,265 - server - INFO - 统计分析完成，总耗时: 0.01s
2025-08-02 17:51:13,290 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 17:51:13,315 - mcp.server.streamable_http - INFO - Terminating session: ff0fd53895c24bd3b3582ff3823f4bd4
2025-08-02 17:51:34,756 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 852e8586736b499587ffe4978a2fd7fa
2025-08-02 17:51:34,781 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 17:51:34,782 - server - INFO - 获取系统状态
2025-08-02 17:51:34,795 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 17:51:34,809 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 17:51:34,824 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 17:51:34,837 - mcp.server.streamable_http - INFO - Terminating session: 852e8586736b499587ffe4978a2fd7fa
2025-08-02 17:55:47,727 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager shutting down
2025-08-02 18:09:52,379 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
2025-08-02 18:10:20,780 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: c397068f9d404fcf9647d217d420dbe4
2025-08-02 18:10:20,821 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:10:20,838 - server - INFO - 开始高级统计分析: count 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 18:10:20,839 - server - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 18:10:20,839 - server - INFO - 密码长度: 6
2025-08-02 18:10:35,614 - server - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 18:10:35,952 - server - INFO - 查询完成: 1 行, 耗时 15.11s
2025-08-02 18:10:35,957 - server - INFO - 列 temperature 的 count 结果: 18.0
2025-08-02 18:10:35,958 - server - INFO - 统计分析完成，总耗时: 15.12s
2025-08-02 18:10:35,984 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 18:10:36,024 - mcp.server.streamable_http - INFO - Terminating session: c397068f9d404fcf9647d217d420dbe4
2025-08-02 18:12:28,854 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 4bb00ec0556e42c5b0316353ef22fbc4
2025-08-02 18:12:28,901 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:12:28,926 - server - INFO - 开始高级统计分析: count 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 18:12:28,928 - server - INFO - 列 temperature 的 count 结果: 18.0
2025-08-02 18:12:28,928 - server - INFO - 统计分析完成，总耗时: 0.00s
2025-08-02 18:12:28,954 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 18:12:28,987 - mcp.server.streamable_http - INFO - Terminating session: 4bb00ec0556e42c5b0316353ef22fbc4
2025-08-02 18:15:13,077 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: f4abafc4650243aab2e17412bb285889
2025-08-02 18:15:13,123 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:15:13,139 - server - INFO - 开始高级统计分析: count 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 18:15:13,140 - server - INFO - 列 temperature 的 count 结果: 18.0
2025-08-02 18:15:13,140 - server - INFO - 统计分析完成，总耗时: 0.00s
2025-08-02 18:15:13,163 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 18:15:13,188 - mcp.server.streamable_http - INFO - Terminating session: f4abafc4650243aab2e17412bb285889
2025-08-02 18:15:25,885 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: eaf6eec77b884e9d9fb7de9413ae03e2
2025-08-02 18:15:25,913 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:15:25,938 - server - INFO - 开始高级统计分析: average 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 18:15:25,953 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 18:15:25,960 - server - INFO - 列 temperature 的 average 结果: 28.961111
2025-08-02 18:15:25,960 - server - INFO - 统计分析完成，总耗时: 0.02s
2025-08-02 18:15:25,992 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 18:15:26,018 - mcp.server.streamable_http - INFO - Terminating session: eaf6eec77b884e9d9fb7de9413ae03e2
2025-08-02 18:15:27,555 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: a84d4f5b92004f23a295420bbb98cf8c
2025-08-02 18:15:27,587 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:15:27,600 - server - INFO - 开始智能异常检测: 列=temperature, 方法=hybrid, 时间窗口=24h
2025-08-02 18:15:27,614 - server - INFO - 查询完成: 0 行, 耗时 0.01s
2025-08-02 18:15:27,621 - server - WARNING - 在时间窗口 24h 内没有找到列 temperature 的有效数据
2025-08-02 18:15:27,647 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 18:15:27,678 - mcp.server.streamable_http - INFO - Terminating session: a84d4f5b92004f23a295420bbb98cf8c
2025-08-02 18:15:38,158 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: e4867f9159ea4d6ba450bcf2735bd676
2025-08-02 18:15:38,187 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:15:38,188 - server - INFO - 获取系统状态
2025-08-02 18:15:38,206 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 18:15:38,223 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 18:15:38,241 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 18:15:38,254 - mcp.server.streamable_http - INFO - Terminating session: e4867f9159ea4d6ba450bcf2735bd676
2025-08-02 18:16:24,380 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 666b3a688763458287da8f2bc03483cf
2025-08-02 18:16:24,409 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:16:24,417 - server - INFO - 开始智能异常检测: 列=temperature, 方法=hybrid, 时间窗口=24h
2025-08-02 18:16:24,417 - server - WARNING - 在时间窗口 24h 内没有找到列 temperature 的有效数据
2025-08-02 18:16:24,441 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 18:16:24,464 - mcp.server.streamable_http - INFO - Terminating session: 666b3a688763458287da8f2bc03483cf
2025-08-02 18:16:31,889 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 3c0fb524a3c14980b0396d5058d22f4a
2025-08-02 18:16:31,914 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:16:31,937 - server - INFO - 开始高级统计分析: average 操作，时间范围 2024-01-01 00:00:00 到 2024-01-02 00:00:00
2025-08-02 18:16:31,947 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 18:16:31,952 - server - WARNING - 列 temperature 在指定条件下无有效数据
2025-08-02 18:16:31,960 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 18:16:31,965 - server - WARNING - 列 voltage 在指定条件下无有效数据
2025-08-02 18:16:31,973 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 18:16:31,979 - server - WARNING - 列 power 在指定条件下无有效数据
2025-08-02 18:16:31,980 - server - INFO - 统计分析完成，总耗时: 0.04s
2025-08-02 18:16:32,005 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 18:16:32,032 - mcp.server.streamable_http - INFO - Terminating session: 3c0fb524a3c14980b0396d5058d22f4a
2025-08-02 18:16:47,408 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 71b6a37441df4fdda0c96adf348a50d3
2025-08-02 18:16:47,442 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:16:47,463 - server - INFO - 开始高级统计分析: count 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 18:16:47,471 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 18:16:47,476 - server - INFO - 列 temperature 的 count 结果: 18.0
2025-08-02 18:16:47,476 - server - INFO - 统计分析完成，总耗时: 0.01s
2025-08-02 18:16:47,499 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 18:16:47,521 - mcp.server.streamable_http - INFO - Terminating session: 71b6a37441df4fdda0c96adf348a50d3
2025-08-02 18:16:48,987 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 820457c5884f44ccb79537e058aa82c8
2025-08-02 18:16:49,022 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:16:49,043 - server - INFO - 开始高级统计分析: average 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 18:16:49,044 - server - INFO - 列 temperature 的 average 结果: 28.961111
2025-08-02 18:16:49,045 - server - INFO - 统计分析完成，总耗时: 0.00s
2025-08-02 18:16:49,069 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 18:16:49,094 - mcp.server.streamable_http - INFO - Terminating session: 820457c5884f44ccb79537e058aa82c8
2025-08-02 18:27:55,804 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 2f602e2221fd4eaab7da84e343139fae
2025-08-02 18:27:55,849 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:27:55,872 - server - INFO - 开始高级统计分析: count 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 18:27:55,883 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 18:27:55,889 - server - INFO - 列 temperature 的 count 结果: 18.0
2025-08-02 18:27:55,890 - server - INFO - 统计分析完成，总耗时: 0.02s
2025-08-02 18:27:55,917 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 18:27:55,941 - mcp.server.streamable_http - INFO - Terminating session: 2f602e2221fd4eaab7da84e343139fae
2025-08-02 18:41:05,364 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
2025-08-02 18:43:26,323 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
2025-08-02 18:46:13,230 - __main__ - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 18:46:13,230 - __main__ - INFO - 密码长度: 6
2025-08-02 18:46:21,188 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 858623e6b27c4bd3a5c2a5c2008c4036
2025-08-02 18:46:21,215 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:46:21,217 - server - INFO - 获取系统状态
2025-08-02 18:46:21,218 - server - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 18:46:21,218 - server - INFO - 密码长度: 6
2025-08-02 18:46:27,259 - __main__ - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 18:46:27,347 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'D:\\Program Files\\conda\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-02 18:46:27,348 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'D:\Program Files\conda\Lib\site-packages\comtypes\gen'
2025-08-02 18:46:27,644 - __main__ - INFO - 语音引擎初始化成功
2025-08-02 18:46:34,905 - server - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 18:46:35,274 - server - INFO - 查询完成: 1 行, 耗时 0.36s
2025-08-02 18:46:35,285 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 18:46:35,304 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 18:46:35,322 - mcp.server.streamable_http - INFO - Terminating session: 858623e6b27c4bd3a5c2a5c2008c4036
2025-08-02 18:47:56,663 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
2025-08-02 18:48:51,902 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: caab672b7a8b435ba583ec7043a92d18
2025-08-02 18:48:51,932 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:48:51,933 - mcp.server.lowlevel.server - WARNING - Tool 'ai_intelligent_analysis' not listed, no validation will be performed
2025-08-02 18:48:51,941 - mcp.server.streamable_http - INFO - Terminating session: caab672b7a8b435ba583ec7043a92d18
2025-08-02 18:51:21,504 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 0faa02a9f5b44beea9e35266bf06b3e2
2025-08-02 18:51:21,529 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:51:21,530 - mcp.server.lowlevel.server - WARNING - Tool 'ai_intelligent_analysis' not listed, no validation will be performed
2025-08-02 18:51:21,537 - mcp.server.streamable_http - INFO - Terminating session: 0faa02a9f5b44beea9e35266bf06b3e2
2025-08-02 18:53:42,913 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: d0e3ab03c8ff4e3f849588603b52670f
2025-08-02 18:53:42,954 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:53:42,960 - server - INFO - 创建时间提醒: 09:00, 频率: daily
2025-08-02 18:53:42,961 - server - INFO - 时间提醒已创建: {'alert_id': 'time_alert_20250802_185342', 'type': 'time_alert', 'alert_time': '09:00', 'frequency': 'daily', 'message': '每日数据报告', 'created_at': '2025-08-02T18:53:42.961397', 'enabled': True}
2025-08-02 18:53:42,974 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 18:53:42,996 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:53:43,003 - server - INFO - 创建数值提醒: temperature > 35.0, 方式: notification
2025-08-02 18:53:43,004 - server - ERROR - 创建数值提醒失败: 'FunctionTool' object is not callable
2025-08-02 18:53:43,022 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:53:43,023 - server - INFO - 获取活动提醒列表
2025-08-02 18:53:43,024 - server - INFO - 找到 2 个活动提醒
2025-08-02 18:53:43,045 - mcp.server.streamable_http - INFO - Terminating session: d0e3ab03c8ff4e3f849588603b52670f
2025-08-02 18:55:21,958 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
2025-08-02 18:55:21,960 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager shutting down
2025-08-02 18:55:55,684 - __main__ - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 18:55:55,685 - __main__ - INFO - 密码长度: 6
2025-08-02 18:56:09,836 - __main__ - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 18:56:09,962 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'D:\\Program Files\\conda\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-02 18:56:09,963 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'D:\Program Files\conda\Lib\site-packages\comtypes\gen'
2025-08-02 18:56:10,372 - __main__ - INFO - 语音引擎初始化成功
2025-08-02 18:56:25,010 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: f112f9d96d9b4198a716afff90bd783f
2025-08-02 18:56:25,062 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:56:25,068 - server - INFO - 创建时间提醒: 09:00, 频率: daily
2025-08-02 18:56:25,068 - server - INFO - 时间提醒已创建: {'alert_id': 'time_alert_20250802_185625', 'type': 'time_alert', 'alert_time': '09:00', 'frequency': 'daily', 'message': '每日数据报告', 'created_at': '2025-08-02T18:56:25.068576', 'enabled': True}
2025-08-02 18:56:25,083 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 18:56:25,102 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:56:25,108 - server - INFO - 创建数值提醒: temperature > 35.0, 方式: notification
2025-08-02 18:56:25,108 - server - ERROR - 创建数值提醒失败: 'FunctionTool' object is not callable
2025-08-02 18:56:25,124 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:56:25,126 - server - INFO - 获取活动提醒列表
2025-08-02 18:56:25,126 - server - INFO - 找到 2 个活动提醒
2025-08-02 18:56:25,148 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:56:25,161 - server - INFO - 开始综合走势分析: 列=['temperature', 'humidity'], 周期=7d, 类型=['trend', 'correlation']
2025-08-02 18:56:25,162 - server - ERROR - 分析列 temperature 失败: 'coroutine' object does not support the asynchronous context manager protocol
2025-08-02 18:56:25,162 - server - ERROR - 分析列 humidity 失败: 'coroutine' object does not support the asynchronous context manager protocol
2025-08-02 18:56:25,163 - server - INFO - 综合走势分析完成，总耗时: 0.00秒
2025-08-02 18:56:25,184 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:56:25,452 - mcp.server.streamable_http - INFO - Terminating session: f112f9d96d9b4198a716afff90bd783f
2025-08-02 18:58:08,884 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: f43fe5f9e4694bb9ad65837f669daa95
2025-08-02 18:58:08,926 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:58:08,932 - server - INFO - 创建时间提醒: 09:00, 频率: daily
2025-08-02 18:58:08,932 - server - INFO - 时间提醒已创建: {'alert_id': 'time_alert_20250802_185808', 'type': 'time_alert', 'alert_time': '09:00', 'frequency': 'daily', 'message': '每日数据报告', 'created_at': '2025-08-02T18:58:08.932674', 'enabled': True}
2025-08-02 18:58:08,950 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 18:58:08,969 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:58:08,975 - server - INFO - 创建数值提醒: temperature > 35.0, 方式: notification
2025-08-02 18:58:08,975 - server - ERROR - 创建数值提醒失败: 'FunctionTool' object is not callable
2025-08-02 18:58:08,991 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:58:08,993 - server - INFO - 获取活动提醒列表
2025-08-02 18:58:08,993 - server - INFO - 找到 2 个活动提醒
2025-08-02 18:58:09,015 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:58:09,031 - server - INFO - 开始综合走势分析: 列=['temperature', 'humidity'], 周期=7d, 类型=['trend', 'correlation']
2025-08-02 18:58:09,032 - server - ERROR - 分析列 temperature 失败: 'coroutine' object does not support the asynchronous context manager protocol
2025-08-02 18:58:09,033 - server - ERROR - 分析列 humidity 失败: 'coroutine' object does not support the asynchronous context manager protocol
2025-08-02 18:58:09,033 - server - INFO - 综合走势分析完成，总耗时: 0.00秒
2025-08-02 18:58:09,054 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 18:58:09,071 - server - INFO - 生成 line 图表，列: ['temperature'], 时间范围: 24h
2025-08-02 18:58:09,071 - server - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 18:58:09,072 - server - INFO - 密码长度: 6
2025-08-02 18:58:23,589 - server - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 18:58:24,019 - server - INFO - 查询完成: 0 行, 耗时 14.95s
2025-08-02 18:58:24,024 - server - WARNING - 指定条件下没有数据
2025-08-02 18:58:24,039 - mcp.server.streamable_http - INFO - Terminating session: f43fe5f9e4694bb9ad65837f669daa95
2025-08-02 19:00:48,271 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager shutting down
2025-08-02 19:01:00,465 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
2025-08-02 19:02:16,813 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 9b1b1743a23a4d67a89784f5c67894e2
2025-08-02 19:02:16,856 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 19:02:16,859 - mcp.server.lowlevel.server - WARNING - Tool 'ai_intelligent_analysis' not listed, no validation will be performed
2025-08-02 19:02:16,868 - mcp.server.streamable_http - INFO - Terminating session: 9b1b1743a23a4d67a89784f5c67894e2
2025-08-02 19:05:44,966 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
2025-08-02 19:05:44,968 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager shutting down
2025-08-02 19:06:21,949 - __main__ - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 19:06:21,950 - __main__ - INFO - 密码长度: 6
2025-08-02 19:06:36,428 - __main__ - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 19:06:36,546 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'D:\\Program Files\\conda\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-02 19:06:36,547 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'D:\Program Files\conda\Lib\site-packages\comtypes\gen'
2025-08-02 19:06:36,852 - __main__ - INFO - 语音引擎初始化成功
2025-08-02 19:09:27,475 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 95870a36d9ce4d98bc357b84bbe2060a
2025-08-02 19:09:27,519 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 19:09:27,520 - mcp.server.lowlevel.server - WARNING - Tool 'ai_intelligent_analysis' not listed, no validation will be performed
2025-08-02 19:09:27,529 - mcp.server.streamable_http - INFO - Terminating session: 95870a36d9ce4d98bc357b84bbe2060a
2025-08-02 19:11:51,360 - __main__ - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 19:11:51,361 - __main__ - INFO - 密码长度: 6
2025-08-02 19:12:04,840 - __main__ - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 19:12:04,937 - comtypes.client._code_cache - INFO - Imported existing <module 'comtypes.gen' from 'D:\\Program Files\\conda\\Lib\\site-packages\\comtypes\\gen\\__init__.py'>
2025-08-02 19:12:04,938 - comtypes.client._code_cache - INFO - Using writeable comtypes cache directory: 'D:\Program Files\conda\Lib\site-packages\comtypes\gen'
2025-08-02 19:12:05,300 - __main__ - INFO - 语音引擎初始化成功
2025-08-02 19:17:39,366 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
2025-08-02 19:18:13,149 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 3f44d18aa6a643309c988e49efe9b4ce
2025-08-02 19:18:13,185 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 19:18:13,208 - mcp.server.streamable_http - INFO - Terminating session: 3f44d18aa6a643309c988e49efe9b4ce
2025-08-02 19:22:36,765 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 0fc707cfcf8a4c4ebe914632254726da
2025-08-02 19:22:36,805 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 19:22:36,820 - server - INFO - 开始高级统计分析: average 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 19:22:36,821 - server - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 19:22:36,822 - server - INFO - 密码长度: 6
2025-08-02 19:22:51,094 - server - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 19:22:51,422 - server - INFO - 查询完成: 1 行, 耗时 14.60s
2025-08-02 19:22:51,431 - server - INFO - 列 temperature 的 average 结果: 28.961111
2025-08-02 19:22:51,431 - server - INFO - 统计分析完成，总耗时: 14.61s
2025-08-02 19:22:51,461 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 19:22:51,502 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 19:22:51,518 - server - INFO - 开始高级统计分析: count 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 19:22:51,526 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 19:22:51,531 - server - INFO - 列 temperature 的 count 结果: 18.0
2025-08-02 19:22:51,531 - server - INFO - 统计分析完成，总耗时: 0.01s
2025-08-02 19:22:51,575 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 19:22:51,582 - server - INFO - 开始智能异常检测: 列=temperature, 方法=hybrid, 时间窗口=24h
2025-08-02 19:22:51,592 - server - INFO - 查询完成: 0 行, 耗时 0.01s
2025-08-02 19:22:51,597 - server - WARNING - 在时间窗口 24h 内没有找到列 temperature 的有效数据
2025-08-02 19:22:51,640 - mcp.server.streamable_http - INFO - Terminating session: 0fc707cfcf8a4c4ebe914632254726da
2025-08-02 22:18:18,860 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
2025-08-02 22:19:59,631 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 2b6b0f980ce5450ea7a616f9c0e1bf11
2025-08-02 22:19:59,657 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 22:19:59,672 - server - INFO - 开始高级统计分析: average 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 22:19:59,672 - server - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 22:19:59,672 - server - INFO - 密码长度: 6
2025-08-02 22:20:13,653 - server - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 22:20:14,071 - server - INFO - 查询完成: 1 行, 耗时 14.40s
2025-08-02 22:20:14,076 - server - INFO - 列 temperature 的 average 结果: 28.961111
2025-08-02 22:20:14,086 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 22:20:14,093 - server - INFO - 列 humidity 的 average 结果: 67.183333
2025-08-02 22:20:14,100 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 22:20:14,105 - server - INFO - 列 pressure 的 average 结果: 101342.5
2025-08-02 22:20:14,106 - server - INFO - 统计分析完成，总耗时: 14.43s
2025-08-02 22:20:14,129 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 22:20:14,155 - mcp.server.streamable_http - INFO - Terminating session: 2b6b0f980ce5450ea7a616f9c0e1bf11
2025-08-02 22:20:52,814 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 20369a59b6c24f00bdd11d6637f3e02d
2025-08-02 22:20:52,848 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 22:20:52,866 - server - INFO - 开始高级统计分析: count 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 22:20:52,886 - server - INFO - 查询完成: 1 行, 耗时 0.02s
2025-08-02 22:20:52,891 - server - INFO - 列 temperature 的 count 结果: 18.0
2025-08-02 22:20:52,892 - server - INFO - 统计分析完成，总耗时: 0.03s
2025-08-02 22:20:52,917 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 22:20:52,942 - mcp.server.streamable_http - INFO - Terminating session: 20369a59b6c24f00bdd11d6637f3e02d
2025-08-02 22:20:54,373 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 711840b851ef41e4a0bade9a8ccbb57e
2025-08-02 22:20:54,404 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 22:20:54,424 - server - INFO - 开始高级统计分析: average 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 22:20:54,425 - server - INFO - 列 temperature 的 average 结果: 28.961111
2025-08-02 22:20:54,425 - server - INFO - 统计分析完成，总耗时: 0.00s
2025-08-02 22:20:54,447 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 22:20:54,471 - mcp.server.streamable_http - INFO - Terminating session: 711840b851ef41e4a0bade9a8ccbb57e
2025-08-02 22:21:28,251 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: ccb6ed8878cf4fa5bb6e96b448a29c18
2025-08-02 22:21:28,281 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 22:21:28,562 - mcp.server.streamable_http - INFO - Terminating session: ccb6ed8878cf4fa5bb6e96b448a29c18
2025-08-02 22:21:46,177 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: fcc7b0c8506b4966abaecd8be1e6c03c
2025-08-02 22:21:46,217 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 22:21:46,235 - server - INFO - 开始高级统计分析: average 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 22:21:46,236 - server - INFO - 列 temperature 的 average 结果: 28.961111
2025-08-02 22:21:46,237 - server - INFO - 列 humidity 的 average 结果: 67.183333
2025-08-02 22:21:46,238 - server - INFO - 列 pressure 的 average 结果: 101342.5
2025-08-02 22:21:46,239 - server - INFO - 统计分析完成，总耗时: 0.00s
2025-08-02 22:21:46,263 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 22:21:46,291 - mcp.server.streamable_http - INFO - Terminating session: fcc7b0c8506b4966abaecd8be1e6c03c
2025-08-02 22:30:06,659 - mcp.server.streamable_http_manager - INFO - StreamableHTTP session manager started
2025-08-02 22:30:39,119 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 3bb622a2f6204b0091845848e4e033f4
2025-08-02 22:30:39,159 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 22:30:39,176 - server - INFO - 开始高级统计分析: average 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 22:30:39,176 - server - INFO - 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
2025-08-02 22:30:39,177 - server - INFO - 密码长度: 6
2025-08-02 22:30:53,239 - server - INFO - 数据库连接池初始化成功: 10 连接
2025-08-02 22:30:53,580 - server - INFO - 查询完成: 1 行, 耗时 14.40s
2025-08-02 22:30:53,589 - server - INFO - 列 temperature 的 average 结果: 28.961111
2025-08-02 22:30:53,599 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 22:30:53,608 - server - INFO - 列 humidity 的 average 结果: 67.183333
2025-08-02 22:30:53,619 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 22:30:53,625 - server - INFO - 列 pressure 的 average 结果: 101342.5
2025-08-02 22:30:53,625 - server - INFO - 统计分析完成，总耗时: 14.45s
2025-08-02 22:30:53,652 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 22:30:53,682 - mcp.server.streamable_http - INFO - Terminating session: 3bb622a2f6204b0091845848e4e033f4
2025-08-02 22:38:44,775 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 01c1a0db2b7c44ddb2d96afb9a644a50
2025-08-02 22:38:44,798 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 22:38:44,811 - server - INFO - 开始高级统计分析: average 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 22:38:44,819 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 22:38:44,824 - server - INFO - 列 temperature 的 average 结果: 28.961111
2025-08-02 22:38:44,830 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 22:38:44,834 - server - INFO - 列 humidity 的 average 结果: 67.183333
2025-08-02 22:38:44,840 - server - INFO - 查询完成: 1 行, 耗时 0.01s
2025-08-02 22:38:44,844 - server - INFO - 列 pressure 的 average 结果: 101342.5
2025-08-02 22:38:44,845 - server - INFO - 统计分析完成，总耗时: 0.03s
2025-08-02 22:38:44,865 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 22:38:44,890 - mcp.server.streamable_http - INFO - Terminating session: 01c1a0db2b7c44ddb2d96afb9a644a50
2025-08-02 22:39:35,248 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 6ebb77ad25ba41d2b992a0edb4885423
2025-08-02 22:39:35,273 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 22:39:35,288 - server - INFO - 开始高级统计分析: average 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 22:39:35,290 - server - INFO - 列 temperature 的 average 结果: 28.961111
2025-08-02 22:39:35,291 - server - INFO - 列 humidity 的 average 结果: 67.183333
2025-08-02 22:39:35,292 - server - INFO - 列 pressure 的 average 结果: 101342.5
2025-08-02 22:39:35,292 - server - INFO - 统计分析完成，总耗时: 0.00s
2025-08-02 22:39:35,316 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 22:39:35,341 - mcp.server.streamable_http - INFO - Terminating session: 6ebb77ad25ba41d2b992a0edb4885423
2025-08-02 22:40:03,998 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 4838791d5b6344ee80147560e5dab4ff
2025-08-02 22:40:04,023 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 22:40:04,039 - server - INFO - 开始高级统计分析: average 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 22:40:04,040 - server - INFO - 列 temperature 的 average 结果: 28.961111
2025-08-02 22:40:04,041 - server - INFO - 列 humidity 的 average 结果: 67.183333
2025-08-02 22:40:04,042 - server - INFO - 列 pressure 的 average 结果: 101342.5
2025-08-02 22:40:04,043 - server - INFO - 统计分析完成，总耗时: 0.00s
2025-08-02 22:40:04,066 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 22:40:04,091 - mcp.server.streamable_http - INFO - Terminating session: 4838791d5b6344ee80147560e5dab4ff
2025-08-02 22:40:23,874 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: ec3f442416224473b4554c3933f8fdb6
2025-08-02 22:40:23,900 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 22:40:23,916 - server - INFO - 开始高级统计分析: average 操作，时间范围 2020-01-01 00:00:00 到 2030-01-01 00:00:00
2025-08-02 22:40:23,917 - server - INFO - 列 temperature 的 average 结果: 28.961111
2025-08-02 22:40:23,918 - server - INFO - 列 humidity 的 average 结果: 67.183333
2025-08-02 22:40:23,919 - server - INFO - 列 pressure 的 average 结果: 101342.5
2025-08-02 22:40:23,919 - server - INFO - 统计分析完成，总耗时: 0.00s
2025-08-02 22:40:23,943 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 22:40:23,968 - mcp.server.streamable_http - INFO - Terminating session: ec3f442416224473b4554c3933f8fdb6
2025-08-02 22:41:36,750 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 7d38df3731554ea2867ef0f75cf85aa6
2025-08-02 22:41:36,786 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 22:41:37,075 - mcp.server.streamable_http - INFO - Terminating session: 7d38df3731554ea2867ef0f75cf85aa6
2025-08-02 22:52:19,859 - mcp.server.streamable_http_manager - INFO - Created new transport with session ID: 22b7292588e246ee8fa7ae1543b62c69
2025-08-02 22:52:19,899 - mcp.server.lowlevel.server - INFO - Processing request of type CallToolRequest
2025-08-02 22:52:19,924 - server - INFO - 生成 line 图表，列: ['temperature'], 时间范围: 1h
2025-08-02 22:52:19,946 - server - INFO - 查询完成: 0 行, 耗时 0.02s
2025-08-02 22:52:19,952 - server - WARNING - 指定条件下没有数据
2025-08-02 22:52:19,966 - mcp.server.lowlevel.server - INFO - Processing request of type ListToolsRequest
2025-08-02 22:52:19,985 - mcp.server.streamable_http - INFO - Terminating session: 22b7292588e246ee8fa7ae1543b62c69
