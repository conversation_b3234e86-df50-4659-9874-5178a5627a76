-- =====================================================
-- 数据库分析MCP服务器 - 简化数据库初始化脚本
-- =====================================================

-- 创建数据库
CREATE DATABASE IF NOT EXISTS sensor_data 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE sensor_data;

-- =====================================================
-- 主数据表：传感器数据
-- =====================================================
CREATE TABLE IF NOT EXISTS sensor_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    timestamp DATETIME NOT NULL COMMENT '时间戳',
    
    -- 环境传感器数据
    temperature DECIMAL(5,2) COMMENT '温度(°C)',
    humidity DECIMAL(5,2) COMMENT '湿度(%)',
    pressure DECIMAL(8,2) COMMENT '大气压力(Pa)',
    
    -- 流体传感器数据
    flow_rate DECIMAL(8,2) COMMENT '流量(L/min)',
    flow_total DECIMAL(12,2) COMMENT '累计流量(L)',
    
    -- 电气传感器数据
    voltage DECIMAL(6,2) COMMENT '电压(V)',
    current DECIMAL(6,2) COMMENT '电流(A)',
    power DECIMAL(8,2) COMMENT '功率(W)',
    frequency DECIMAL(5,2) COMMENT '频率(Hz)',
    
    -- 机械传感器数据
    vibration DECIMAL(6,3) COMMENT '振动(mm/s)',
    rotation_speed DECIMAL(8,2) COMMENT '转速(RPM)',
    torque DECIMAL(8,2) COMMENT '扭矩(N·m)',
    
    -- 位置传感器数据
    position_x DECIMAL(8,3) COMMENT 'X轴位置(mm)',
    position_y DECIMAL(8,3) COMMENT 'Y轴位置(mm)',
    position_z DECIMAL(8,3) COMMENT 'Z轴位置(mm)',
    
    -- 质量传感器数据
    weight DECIMAL(8,2) COMMENT '重量(kg)',
    density DECIMAL(6,3) COMMENT '密度(kg/m³)',
    
    -- 光学传感器数据
    light_intensity DECIMAL(8,2) COMMENT '光照强度(lux)',
    uv_index DECIMAL(4,2) COMMENT '紫外线指数',
    
    -- 化学传感器数据
    ph_value DECIMAL(4,2) COMMENT 'pH值',
    dissolved_oxygen DECIMAL(6,2) COMMENT '溶解氧(mg/L)',
    conductivity DECIMAL(8,2) COMMENT '电导率(μS/cm)',
    
    -- 状态字段
    device_id VARCHAR(50) COMMENT '设备ID',
    location VARCHAR(100) COMMENT '位置信息',
    status ENUM('normal', 'warning', 'error', 'offline') DEFAULT 'normal' COMMENT '设备状态',
    quality_flag TINYINT DEFAULT 1 COMMENT '数据质量标志(1=好,0=差)',
    
    -- 索引
    INDEX idx_timestamp (timestamp),
    INDEX idx_device_timestamp (device_id, timestamp),
    INDEX idx_status (status),
    INDEX idx_temperature (temperature),
    INDEX idx_pressure (pressure),
    INDEX idx_flow_rate (flow_rate),
    INDEX idx_power (power)
    
) ENGINE=InnoDB 
COMMENT='传感器实时数据表';

-- =====================================================
-- 插入示例数据（最近24小时）
-- =====================================================

-- 插入过去24小时的示例数据，每10分钟一条记录
INSERT INTO sensor_data (
    timestamp, device_id, location, 
    temperature, humidity, pressure,
    flow_rate, flow_total,
    voltage, current, power, frequency,
    vibration, rotation_speed, torque,
    position_x, position_y, position_z,
    weight, density,
    light_intensity, uv_index,
    ph_value, dissolved_oxygen, conductivity,
    status, quality_flag
) VALUES 
-- 24小时前开始
(DATE_SUB(NOW(), INTERVAL 24 HOUR), 'TEMP_001', '车间A-1区', 25.5, 65.2, 101325.0, 150.0, 1000.0, 220.0, 5.5, 1210.0, 50.0, 2.1, 1500.0, 75.0, 10.5, -5.2, 8.3, 250.0, 1.2, 500.0, 3.5, 7.2, 8.5, 450.0, 'normal', 1),
(DATE_SUB(NOW(), INTERVAL 23 HOUR), 'TEMP_001', '车间A-1区', 25.7, 65.0, 101320.0, 152.0, 1001.5, 220.5, 5.6, 1234.8, 50.1, 2.0, 1520.0, 76.0, 10.2, -5.0, 8.1, 252.0, 1.21, 520.0, 3.6, 7.3, 8.4, 455.0, 'normal', 1),
(DATE_SUB(NOW(), INTERVAL 22 HOUR), 'TEMP_001', '车间A-1区', 25.6, 64.8, 101318.0, 151.5, 1003.0, 220.2, 5.5, 1211.1, 49.9, 2.2, 1480.0, 74.5, 10.8, -5.5, 8.5, 248.0, 1.19, 480.0, 3.4, 7.1, 8.6, 445.0, 'normal', 1),
(DATE_SUB(NOW(), INTERVAL 21 HOUR), 'TEMP_001', '车间A-1区', 25.8, 65.1, 101322.0, 153.0, 1004.5, 220.8, 5.7, 1258.6, 50.2, 1.9, 1550.0, 77.0, 9.8, -4.8, 7.9, 255.0, 1.22, 540.0, 3.7, 7.4, 8.3, 460.0, 'normal', 1),
(DATE_SUB(NOW(), INTERVAL 20 HOUR), 'TEMP_001', '车间A-1区', 25.9, 64.9, 101315.0, 154.0, 1006.0, 221.0, 5.8, 1281.8, 50.3, 2.3, 1600.0, 78.0, 11.2, -5.8, 8.7, 258.0, 1.23, 560.0, 3.8, 7.5, 8.2, 465.0, 'normal', 1),

-- 继续插入更多数据点...
(DATE_SUB(NOW(), INTERVAL 19 HOUR), 'PRESS_001', '车间A-2区', 26.1, 66.0, 101330.0, 148.0, 1007.5, 219.5, 5.4, 1185.3, 49.8, 2.0, 1450.0, 73.0, 10.0, -4.5, 8.0, 245.0, 1.18, 490.0, 3.3, 7.0, 8.7, 440.0, 'normal', 1),
(DATE_SUB(NOW(), INTERVAL 18 HOUR), 'PRESS_001', '车间A-2区', 26.3, 66.5, 101335.0, 149.0, 1009.0, 219.8, 5.3, 1164.9, 49.7, 1.8, 1420.0, 72.0, 9.5, -4.2, 7.8, 242.0, 1.17, 470.0, 3.2, 6.9, 8.8, 435.0, 'normal', 1),
(DATE_SUB(NOW(), INTERVAL 17 HOUR), 'FLOW_001', '管道B-1', 26.0, 65.8, 101325.0, 155.0, 1010.5, 220.3, 5.9, 1295.7, 50.0, 2.4, 1580.0, 79.0, 11.5, -6.0, 8.9, 260.0, 1.24, 580.0, 3.9, 7.6, 8.1, 470.0, 'normal', 1),
(DATE_SUB(NOW(), INTERVAL 16 HOUR), 'POWER_001', '配电室-1', 25.4, 64.5, 101310.0, 147.0, 1012.0, 219.0, 5.2, 1138.8, 49.5, 1.7, 1380.0, 71.0, 9.0, -3.8, 7.5, 238.0, 1.15, 450.0, 3.0, 6.8, 9.0, 430.0, 'normal', 1),
(DATE_SUB(NOW(), INTERVAL 15 HOUR), 'VIB_001', '设备C-1', 25.2, 64.0, 101305.0, 146.0, 1013.5, 218.5, 5.1, 1112.4, 49.3, 1.6, 1350.0, 70.0, 8.5, -3.5, 7.2, 235.0, 1.14, 430.0, 2.9, 6.7, 9.1, 425.0, 'normal', 1),

-- 最近几小时的数据，包含一些异常值
(DATE_SUB(NOW(), INTERVAL 5 HOUR), 'TEMP_001', '车间A-1区', 45.2, 75.0, 101400.0, 180.0, 1050.0, 225.0, 6.5, 1462.5, 52.0, 4.5, 2000.0, 95.0, 15.0, -8.0, 10.5, 300.0, 1.35, 750.0, 5.0, 8.5, 7.0, 550.0, 'warning', 1),
(DATE_SUB(NOW(), INTERVAL 4 HOUR), 'TEMP_001', '车间A-1区', 52.8, 78.0, 101450.0, 185.0, 1052.0, 228.0, 7.0, 1596.0, 53.0, 5.2, 2200.0, 98.0, 16.0, -9.0, 11.0, 320.0, 1.40, 800.0, 5.5, 9.0, 6.5, 580.0, 'error', 0),
(DATE_SUB(NOW(), INTERVAL 3 HOUR), 'PRESS_001', '车间A-2区', 26.5, 66.8, 105000.0, 200.0, 1054.0, 230.0, 7.5, 1725.0, 54.0, 6.0, 2500.0, 100.0, 17.0, -10.0, 12.0, 350.0, 1.45, 850.0, 6.0, 9.5, 6.0, 600.0, 'warning', 1),
(DATE_SUB(NOW(), INTERVAL 2 HOUR), 'TEMP_001', '车间A-1区', 28.0, 67.0, 101350.0, 160.0, 1056.0, 222.0, 6.0, 1332.0, 51.0, 2.8, 1700.0, 85.0, 12.0, -6.5, 9.0, 280.0, 1.30, 650.0, 4.2, 7.8, 8.0, 520.0, 'normal', 1),
(DATE_SUB(NOW(), INTERVAL 1 HOUR), 'TEMP_001', '车间A-1区', 26.8, 65.5, 101340.0, 158.0, 1058.0, 221.5, 5.9, 1306.9, 50.5, 2.5, 1650.0, 82.0, 11.5, -6.0, 8.8, 275.0, 1.28, 620.0, 4.0, 7.6, 8.2, 510.0, 'normal', 1),

-- 最新数据
(NOW(), 'TEMP_001', '车间A-1区', 26.2, 65.0, 101330.0, 155.0, 1060.0, 221.0, 5.8, 1281.8, 50.2, 2.3, 1600.0, 80.0, 11.0, -5.8, 8.5, 270.0, 1.26, 600.0, 3.8, 7.4, 8.4, 500.0, 'normal', 1);

-- =====================================================
-- 创建视图：最新数据概览
-- =====================================================
CREATE VIEW latest_sensor_data AS
SELECT 
    device_id,
    location,
    timestamp,
    temperature,
    humidity,
    pressure,
    flow_rate,
    voltage,
    current,
    power,
    status
FROM sensor_data
WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR)
ORDER BY timestamp DESC;

-- =====================================================
-- 完成信息
-- =====================================================
SELECT '简化数据库初始化完成！' AS message;
SELECT COUNT(*) AS '插入的数据行数' FROM sensor_data;
SELECT 
    MIN(timestamp) AS '最早时间',
    MAX(timestamp) AS '最新时间'
FROM sensor_data;
