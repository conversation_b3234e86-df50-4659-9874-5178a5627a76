#!/usr/bin/env python3
"""
使用FastMCP CLI启动HTTP服务器
基于FastMCP文档的CLI方法
"""

import sys
import os
import subprocess
from pathlib import Path

def start_with_fastmcp_cli():
    """使用FastMCP CLI启动HTTP服务器"""
    print("🚀 使用FastMCP CLI启动HTTP服务器")
    print("=" * 50)
    
    # 服务器文件路径
    server_path = Path(__file__).parent.parent / "enterprise_database_mcp_server.py"
    
    if not server_path.exists():
        print(f"❌ 服务器文件不存在: {server_path}")
        return False
    
    print(f"📁 服务器文件: {server_path}")
    
    # 设置环境变量
    env_vars = {
        "DB_HOST": "localhost",
        "DB_PORT": "3306",
        "DB_USER": "root",
        "DB_PASSWORD": "123456",
        "DB_NAME": "sensor_data",
        "DB_CHARSET": "utf8mb4",
        "PYTHONIOENCODING": "utf-8"
    }
    
    print("\n🌍 设置环境变量:")
    for key, value in env_vars.items():
        print(f"  {key} = {value}")
    
    # 构建FastMCP CLI命令
    cmd = [
        sys.executable, "-m", "fastmcp", "run",
        str(server_path),
        "--transport", "http",
        "--host", "127.0.0.1",
        "--port", "8000"
    ]
    
    print(f"\n🔧 执行命令:")
    print(f"  {' '.join(cmd)}")
    
    print("\n🌐 启动HTTP服务器...")
    print("📍 地址: http://127.0.0.1:8000/mcp/")
    print("⏹️ 按 Ctrl+C 停止服务器")
    print("🔄 服务器启动后，请在新终端运行第2步")
    print("-" * 50)
    
    try:
        # 设置环境变量并运行
        env = os.environ.copy()
        env.update(env_vars)
        
        # 运行FastMCP CLI命令
        result = subprocess.run(cmd, env=env)
        
        if result.returncode == 0:
            print("✅ 服务器正常退出")
            return True
        else:
            print(f"❌ 服务器退出码: {result.returncode}")
            return False
            
    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
        return True
    except FileNotFoundError:
        print("❌ FastMCP CLI未找到，尝试安装:")
        print("   pip install fastmcp")
        return False
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False

def start_with_python_module():
    """使用Python模块方式启动"""
    print("\n🔄 尝试Python模块方式...")
    
    try:
        # 直接运行服务器文件
        server_path = Path(__file__).parent.parent / "enterprise_database_mcp_server.py"
        
        # 设置环境变量
        env_vars = {
            "DB_HOST": "localhost",
            "DB_PORT": "3306",
            "DB_USER": "root",
            "DB_PASSWORD": "123456",
            "DB_NAME": "sensor_data",
            "DB_CHARSET": "utf8mb4",
            "PYTHONIOENCODING": "utf-8"
        }
        
        for key, value in env_vars.items():
            os.environ[key] = value
        
        # 修改sys.argv来模拟命令行参数
        original_argv = sys.argv.copy()
        sys.argv = [str(server_path), "--transport", "http", "--host", "127.0.0.1", "--port", "8000"]
        
        try:
            # 导入并运行服务器
            import importlib.util
            spec = importlib.util.spec_from_file_location("server", server_path)
            server_module = importlib.util.module_from_spec(spec)
            spec.loader.exec_module(server_module)
            
            # 如果服务器有main函数，调用它
            if hasattr(server_module, 'main'):
                server_module.main()
            elif hasattr(server_module, 'mcp'):
                # 直接运行MCP实例
                server_module.mcp.run(
                    transport="http",
                    host="127.0.0.1",
                    port=8000,
                    log_level="INFO"
                )
            
        finally:
            # 恢复原始argv
            sys.argv = original_argv
            
        return True
        
    except Exception as e:
        print(f"❌ Python模块方式失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🤖 FastMCP HTTP服务器启动器")
    print("=" * 50)
    
    # 首先尝试FastMCP CLI
    if start_with_fastmcp_cli():
        return
    
    # 如果CLI失败，尝试Python模块方式
    print("\n" + "="*50)
    print("🔄 FastMCP CLI失败，尝试备用方法")
    
    if start_with_python_module():
        return
    
    # 如果都失败了
    print("\n" + "="*50)
    print("❌ 所有启动方法都失败了")
    print("\n💡 解决方案:")
    print("1. 安装FastMCP: pip install fastmcp")
    print("2. 检查服务器文件是否存在")
    print("3. 检查数据库连接配置")
    print("4. 使用simple_http_server.py作为备用")

if __name__ == "__main__":
    main()
