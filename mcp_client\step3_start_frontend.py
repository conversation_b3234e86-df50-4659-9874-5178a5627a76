#!/usr/bin/env python3
"""
第3步：启动前端界面（连接到HTTP客户端）
基于Streamlit的Web界面
"""

import asyncio
import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
from datetime import datetime
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from fastmcp import Client
from fastmcp.client.transports import StreamableHttpTransport

class HTTPFrontend:
    """HTTP前端界面"""
    
    def __init__(self):
        self.server_url = "http://127.0.0.1:8000/mcp/"
        self.client = None
        self.setup_page()
    
    def setup_page(self):
        """设置页面配置"""
        st.set_page_config(
            page_title="企业级数据库分析系统",
            page_icon="📊",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # 自定义CSS
        st.markdown("""
        <style>
        .main-header {
            font-size: 2.5rem;
            color: #1f77b4;
            text-align: center;
            margin-bottom: 2rem;
        }
        .status-healthy { color: #28a745; }
        .status-warning { color: #ffc107; }
        .status-error { color: #dc3545; }
        .metric-card {
            background: white;
            padding: 1rem;
            border-radius: 0.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 0.5rem 0;
        }
        </style>
        """, unsafe_allow_html=True)
    
    def initialize_client(self):
        """初始化HTTP客户端"""
        try:
            transport = StreamableHttpTransport(url=self.server_url)
            self.client = Client(transport)
            return True

        except Exception as e:
            st.error(f"❌ 创建HTTP客户端失败: {e}")
            st.info("💡 请确保第1步的HTTP服务器正在运行")
            return False
    
    def render_header(self):
        """渲染页面头部"""
        st.markdown('<h1 class="main-header">📊 企业级数据库分析系统</h1>', unsafe_allow_html=True)
        st.markdown("### 🌐 HTTP模式 - 分布式架构")
        st.markdown("---")
        
        # 连接状态
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            st.info(f"🌐 服务器地址: {self.server_url}")
        
        with col2:
            if st.button("🔄 刷新连接"):
                st.rerun()
        
        with col3:
            if st.button("🔍 测试连接"):
                if self.test_connection():
                    st.success("✅ 连接正常")
                else:
                    st.error("❌ 连接失败")
    
    def test_connection(self):
        """测试连接"""
        try:
            if not self.client:
                transport = StreamableHttpTransport(url=self.server_url)
                self.client = Client(transport)

            # 使用同步方式测试连接
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            async def _test():
                async with self.client:
                    await self.client.ping()
                    return True

            result = loop.run_until_complete(_test())
            loop.close()
            return result
        except:
            return False
    
    def get_system_status(self):
        """获取系统状态"""
        try:
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            async def _get_status():
                async with self.client:
                    result = await self.client.call_tool("get_system_status")
                    return result

            result = loop.run_until_complete(_get_status())
            loop.close()
            return result
        except Exception as e:
            return {"error": str(e)}
    
    def render_system_overview(self):
        """渲染系统概览"""
        st.subheader("🖥️ 系统概览")

        try:
            status = self.get_system_status()
            
            if "error" not in status:
                # 提取数据
                if hasattr(status, 'data') and status.data:
                    data = status.data
                elif hasattr(status, 'content') and status.content:
                    data = status.content[0] if status.content else {}
                else:
                    data = status
                
                # 系统指标
                col1, col2, col3, col4 = st.columns(4)
                
                with col1:
                    db_status = data.get("database_status", "unknown")
                    status_color = "🟢" if db_status == "healthy" else "🔴"
                    st.metric("数据库状态", f"{status_color} {db_status}")
                
                with col2:
                    total_rows = data.get("total_data_rows", 0)
                    st.metric("数据总量", f"{total_rows:,}")
                
                with col3:
                    pool_size = data.get("connection_pool_size", 0)
                    st.metric("连接池", pool_size)
                
                with col4:
                    uptime = data.get("uptime", "unknown")
                    st.metric("运行时间", uptime)
                
                # 详细信息
                with st.expander("📋 详细系统信息"):
                    st.json(data)
            
            else:
                st.error(f"❌ 获取系统状态失败: {status['error']}")
                
        except Exception as e:
            st.error(f"❌ 系统概览错误: {e}")
    
    def render_tools_panel(self):
        """渲染工具面板"""
        st.subheader("🛠️ 可用工具")

        try:
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

            async def _get_tools():
                async with self.client:
                    tools = await self.client.list_tools()
                    return tools

            tools = loop.run_until_complete(_get_tools())
            loop.close()

            if tools:
                st.success(f"✅ 找到 {len(tools)} 个工具")

                for tool in tools:
                    with st.expander(f"🔧 {tool.name}"):
                        st.write(f"**描述**: {tool.description}")
                        if hasattr(tool, 'inputSchema') and tool.inputSchema:
                            st.write("**参数**:")
                            st.json(tool.inputSchema)
            else:
                st.warning("⚠️ 未找到可用工具")

        except Exception as e:
            st.error(f"❌ 获取工具列表失败: {e}")
    
    def render_quick_actions(self):
        """渲染快速操作"""
        st.subheader("⚡ 快速操作")

        col1, col2 = st.columns(2)

        with col1:
            if st.button("📊 获取系统状态", use_container_width=True):
                with st.spinner("正在获取系统状态..."):
                    status = self.get_system_status()
                    st.json(status)
        
        with col2:
            if st.button("📈 温度统计分析", use_container_width=True):
                with st.spinner("正在分析温度数据..."):
                    try:
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        async def _analyze():
                            async with self.client:
                                result = await self.client.call_tool("advanced_statistical_analysis", {
                                    "start_time": "2024-01-01 00:00:00",
                                    "end_time": "2024-01-02 00:00:00",
                                    "columns": ["temperature"],
                                    "operation": "average"
                                })
                                return result

                        result = loop.run_until_complete(_analyze())
                        loop.close()
                        st.json(result)
                    except Exception as e:
                        st.error(f"分析失败: {e}")
        
        col3, col4 = st.columns(2)
        
        with col3:
            if st.button("🚨 异常检测", use_container_width=True):
                with st.spinner("正在检测异常..."):
                    try:
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        async def _detect():
                            async with self.client:
                                result = await self.client.call_tool("intelligent_anomaly_detection", {
                                    "column": "temperature",
                                    "method": "hybrid",
                                    "time_window": "24h"
                                })
                                return result

                        result = loop.run_until_complete(_detect())
                        loop.close()
                        st.json(result)
                    except Exception as e:
                        st.error(f"检测失败: {e}")
        
        with col4:
            if st.button("📊 生成图表", use_container_width=True):
                with st.spinner("正在生成图表..."):
                    try:
                        import asyncio
                        loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(loop)

                        async def _generate():
                            async with self.client:
                                result = await self.client.call_tool("generate_advanced_chart", {
                                    "chart_type": "line",
                                    "columns": ["temperature"],
                                    "time_range": "24h",
                                    "title": "温度趋势图"
                                })
                                return result

                        result = loop.run_until_complete(_generate())
                        loop.close()
                        st.json(result)
                    except Exception as e:
                        st.error(f"生成失败: {e}")
    
    def render_sidebar(self):
        """渲染侧边栏"""
        with st.sidebar:
            st.header("🎛️ 控制面板")
            
            # 连接信息
            st.subheader("🔗 连接信息")
            st.write(f"**服务器**: {self.server_url}")
            st.write(f"**模式**: HTTP分布式")
            
            # 连接测试
            if st.button("🔍 测试连接", use_container_width=True):
                if self.test_connection():
                    st.success("✅ 连接正常")
                else:
                    st.error("❌ 连接失败")
            
            st.divider()
            
            # 系统信息
            st.subheader("ℹ️ 系统信息")
            st.write("**架构**: 三层分离")
            st.write("**服务器**: HTTP MCP Server")
            st.write("**客户端**: HTTP MCP Client")
            st.write("**前端**: Streamlit Web UI")
            
            st.divider()
            
            # 操作指南
            st.subheader("📖 操作指南")
            st.write("1. 确保HTTP服务器运行")
            st.write("2. 测试客户端连接")
            st.write("3. 使用前端界面操作")
            
            # 状态指示
            st.subheader("🚦 状态指示")
            if self.test_connection():
                st.success("🟢 系统正常")
            else:
                st.error("🔴 连接异常")
    
    def run(self):
        """运行前端界面"""
        # 初始化客户端
        if not self.initialize_client():
            st.error("❌ 无法连接到HTTP MCP服务器")
            st.info("💡 请确保:")
            st.info("1. 第1步的HTTP服务器正在运行")
            st.info("2. 服务器地址正确: http://127.0.0.1:8000/mcp/")
            st.info("3. 防火墙允许连接")
            return

        # 渲染界面
        self.render_header()
        self.render_sidebar()

        # 主要内容
        tab1, tab2, tab3 = st.tabs(["🖥️ 系统概览", "🛠️ 工具面板", "⚡ 快速操作"])

        with tab1:
            self.render_system_overview()

        with tab2:
            self.render_tools_panel()

        with tab3:
            self.render_quick_actions()

def main():
    """主函数"""
    frontend = HTTPFrontend()
    frontend.run()

if __name__ == "__main__":
    main()
