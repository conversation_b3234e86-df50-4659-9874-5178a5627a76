#!/usr/bin/env python3
"""
MySQL数据库分析MCP服务器
基于FastMCP 2.0框架构建，提供完整的数据分析功能

功能特性：
1. 统计分析：按时间段求和、求平均
2. 异常检测：基于经验值检测异常数据
3. 智能提醒：时间和数值阈值触发
4. 数据可视化：柱状图、饼状图等
5. 趋势分析：数据走势预测
6. 语音交互：语音查询和播报
7. 实时监控：大数据量实时处理
"""

import asyncio
import json
import logging
import os
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Literal
from dataclasses import dataclass
from pathlib import Path

import mysql.connector
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import speech_recognition as sr
import pyttsx3
from pydantic import BaseModel, Field

from fastmcp import FastMCP
# 移除上下文导入，使用简化版本
from fastmcp.server.middleware.timing import TimingMiddleware
from fastmcp.server.middleware.logging import StructuredLoggingMiddleware

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据模型定义
@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = os.getenv("DB_HOST", "localhost")
    port: int = int(os.getenv("DB_PORT", "3306"))
    user: str = os.getenv("DB_USER", "root")
    password: str = os.getenv("DB_PASSWORD", "")
    database: str = os.getenv("DB_NAME", "sensor_data")
    charset: str = os.getenv("DB_CHARSET", "utf8mb4")

class StatisticalResult(BaseModel):
    """统计分析结果"""
    operation: str = Field(description="统计操作类型")
    column: str = Field(description="分析的列名")
    start_time: str = Field(description="开始时间")
    end_time: str = Field(description="结束时间")
    result: float = Field(description="统计结果")
    count: int = Field(description="数据点数量")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外元数据")

class AnomalyResult(BaseModel):
    """异常检测结果"""
    column: str = Field(description="检测的列名")
    anomalies: List[Dict[str, Any]] = Field(description="异常数据点")
    threshold: float = Field(description="阈值")
    total_points: int = Field(description="总数据点")
    anomaly_count: int = Field(description="异常点数量")
    anomaly_rate: float = Field(description="异常率")

class AlertRule(BaseModel):
    """提醒规则"""
    id: str = Field(description="规则ID")
    column: str = Field(description="监控列名")
    condition: Literal["greater_than", "less_than", "range", "change_rate"] = Field(description="条件类型")
    threshold: float = Field(description="阈值")
    enabled: bool = Field(default=True, description="是否启用")
    last_triggered: Optional[str] = Field(default=None, description="最后触发时间")

# 创建FastMCP服务器实例
mcp = FastMCP(
    name="数据库分析助手",
    instructions="""
    我是一个专业的MySQL数据库分析助手，能够：
    1. 执行复杂的统计分析和数据挖掘
    2. 实时监控数据异常并提供智能预警
    3. 生成各种类型的数据可视化图表
    4. 支持语音交互和自然语言查询
    5. 提供数据趋势分析和预测
    
    我专门针对实时传感器数据进行优化，支持大数据量处理。
    """,
    dependencies=[
        "mysql-connector-python>=8.0.0",
        "pandas>=2.0.0", 
        "numpy>=1.24.0",
        "matplotlib>=3.7.0",
        "plotly>=5.15.0",
        "speechrecognition>=3.10.0",
        "pyttsx3>=2.90",
        "scikit-learn>=1.3.0"
    ]
)

# 添加中间件
mcp.add_middleware(TimingMiddleware())
mcp.add_middleware(StructuredLoggingMiddleware())

# 全局变量
db_config = DatabaseConfig()
alert_rules: Dict[str, AlertRule] = {}
tts_engine = None

def init_tts_engine():
    """初始化语音合成引擎"""
    global tts_engine
    if tts_engine is None:
        tts_engine = pyttsx3.init()
        tts_engine.setProperty('rate', int(os.getenv('SPEECH_RATE', '150')))
        voices = tts_engine.getProperty('voices')
        # 尝试设置中文语音
        for voice in voices:
            if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                tts_engine.setProperty('voice', voice.id)
                break

async def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(
            host=db_config.host,
            port=db_config.port,
            user=db_config.user,
            password=db_config.password,
            database=db_config.database,
            charset=db_config.charset,
            autocommit=True
        )
        return connection
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        raise

async def execute_query(query: str, params: tuple = None) -> pd.DataFrame:
    """执行SQL查询并返回DataFrame"""
    logger.debug(f"执行查询: {query}")

    connection = await get_db_connection()
    try:
        df = pd.read_sql(query, connection, params=params)
        logger.info(f"查询返回 {len(df)} 行数据")
        return df
    finally:
        connection.close()

def create_chart_base64(fig, format='png') -> str:
    """将图表转换为base64编码"""
    import io
    
    if hasattr(fig, 'to_html'):  # Plotly图表
        return fig.to_html(include_plotlyjs='cdn')
    else:  # Matplotlib图表
        buffer = io.BytesIO()
        fig.savefig(buffer, format=format, dpi=300, bbox_inches='tight')
        buffer.seek(0)
        img_base64 = base64.b64encode(buffer.getvalue()).decode()
        buffer.close()
        return f"data:image/{format};base64,{img_base64}"

# 统计分析工具
@mcp.tool
async def statistical_analysis(
    start_time: str = Field(description="开始时间 (YYYY-MM-DD HH:MM:SS)"),
    end_time: str = Field(description="结束时间 (YYYY-MM-DD HH:MM:SS)"),
    columns: List[str] = Field(description="要分析的列名列表"),
    operation: Literal["sum", "average", "count", "min", "max", "std"] = Field(description="统计操作类型")
) -> List[StatisticalResult]:
    """
    按时间段进行数据统计分析
    
    支持的操作：
    - sum: 求和
    - average: 平均值
    - count: 计数
    - min: 最小值
    - max: 最大值
    - std: 标准差
    """
    logger.info(f"开始统计分析: {operation} 操作，时间范围 {start_time} 到 {end_time}")
    
    results = []
    
    for column in columns:
        try:
            # 构建查询
            if operation == "sum":
                query = f"SELECT SUM({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
            elif operation == "average":
                query = f"SELECT AVG({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
            elif operation == "count":
                query = f"SELECT COUNT({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
            elif operation == "min":
                query = f"SELECT MIN({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
            elif operation == "max":
                query = f"SELECT MAX({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
            elif operation == "std":
                query = f"SELECT STDDEV({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
            
            df = await execute_query(query, (start_time, end_time))
            
            if not df.empty and df.iloc[0]['result'] is not None:
                result = StatisticalResult(
                    operation=operation,
                    column=column,
                    start_time=start_time,
                    end_time=end_time,
                    result=float(df.iloc[0]['result']),
                    count=int(df.iloc[0]['count']),
                    metadata={
                        "query_time": datetime.now().isoformat(),
                        "data_range": f"{start_time} - {end_time}"
                    }
                )
                results.append(result)
                await ctx.info(f"列 {column} 的 {operation} 结果: {result.result}")
            else:
                await ctx.warning(f"列 {column} 在指定时间范围内无有效数据")
                
        except Exception as e:
            await ctx.error(f"分析列 {column} 时出错: {str(e)}")
            continue
    
    return results

# 异常检测工具
@mcp.tool
async def anomaly_detection(
    column: str = Field(description="要检测的列名"),
    threshold_type: Literal["static", "dynamic", "statistical"] = Field(description="阈值类型"),
    threshold_value: Optional[float] = Field(default=None, description="静态阈值（仅static类型需要）"),
    time_window: str = Field(default="24h", description="时间窗口 (如: 1h, 24h, 7d)"),
    sensitivity: float = Field(default=2.0, description="敏感度（标准差倍数，仅statistical类型）")
) -> AnomalyResult:
    """
    基于不同策略检测数据异常

    阈值类型：
    - static: 使用固定阈值
    - dynamic: 基于历史数据动态计算阈值
    - statistical: 基于统计学方法（均值±N倍标准差）
    """
    ctx = get_context()
    await ctx.info(f"开始异常检测: 列={column}, 类型={threshold_type}, 时间窗口={time_window}")

    # 解析时间窗口
    now = datetime.now()
    if time_window.endswith('h'):
        hours = int(time_window[:-1])
        start_time = now - timedelta(hours=hours)
    elif time_window.endswith('d'):
        days = int(time_window[:-1])
        start_time = now - timedelta(days=days)
    else:
        start_time = now - timedelta(hours=24)  # 默认24小时

    # 获取数据
    query = f"""
    SELECT timestamp, {column}
    FROM sensor_data
    WHERE timestamp >= %s AND {column} IS NOT NULL
    ORDER BY timestamp
    """
    df = await execute_query(query, (start_time.strftime('%Y-%m-%d %H:%M:%S'),))

    if df.empty:
        await ctx.warning(f"在时间窗口 {time_window} 内没有找到列 {column} 的有效数据")
        return AnomalyResult(
            column=column,
            anomalies=[],
            threshold=0.0,
            total_points=0,
            anomaly_count=0,
            anomaly_rate=0.0
        )

    values = df[column].values
    anomalies = []

    # 根据阈值类型计算阈值
    if threshold_type == "static":
        if threshold_value is None:
            raise ValueError("静态阈值类型需要提供threshold_value参数")
        threshold = threshold_value
        anomaly_mask = np.abs(values) > threshold

    elif threshold_type == "dynamic":
        # 使用滑动窗口计算动态阈值
        window_size = min(50, len(values) // 4)  # 动态窗口大小
        rolling_mean = pd.Series(values).rolling(window=window_size, center=True).mean()
        rolling_std = pd.Series(values).rolling(window=window_size, center=True).std()
        threshold = rolling_std.mean() * 2  # 平均标准差的2倍
        anomaly_mask = np.abs(values - rolling_mean) > (rolling_std * 2)

    elif threshold_type == "statistical":
        mean_val = np.mean(values)
        std_val = np.std(values)
        threshold = std_val * sensitivity
        anomaly_mask = np.abs(values - mean_val) > threshold

    # 收集异常点
    anomaly_indices = np.where(anomaly_mask)[0]
    for idx in anomaly_indices:
        anomaly_data = {
            "timestamp": df.iloc[idx]['timestamp'].isoformat(),
            "value": float(values[idx]),
            "deviation": float(abs(values[idx] - np.mean(values))),
            "index": int(idx)
        }
        anomalies.append(anomaly_data)

    result = AnomalyResult(
        column=column,
        anomalies=anomalies,
        threshold=float(threshold),
        total_points=len(values),
        anomaly_count=len(anomalies),
        anomaly_rate=len(anomalies) / len(values) if len(values) > 0 else 0.0
    )

    await ctx.info(f"检测完成: 发现 {len(anomalies)} 个异常点，异常率 {result.anomaly_rate:.2%}")
    return result

# 提醒系统工具
@mcp.tool
async def set_alert_rule(
    rule_id: str = Field(description="规则唯一标识"),
    column: str = Field(description="监控的列名"),
    condition: Literal["greater_than", "less_than", "range", "change_rate"] = Field(description="触发条件"),
    threshold: float = Field(description="阈值"),
    enabled: bool = Field(default=True, description="是否启用规则")
) -> str:
    """
    设置数据监控提醒规则

    条件类型：
    - greater_than: 大于阈值时触发
    - less_than: 小于阈值时触发
    - range: 超出正常范围时触发
    - change_rate: 变化率超过阈值时触发
    """
    ctx = get_context()

    rule = AlertRule(
        id=rule_id,
        column=column,
        condition=condition,
        threshold=threshold,
        enabled=enabled
    )

    alert_rules[rule_id] = rule

    await ctx.info(f"提醒规则已设置: {rule_id} - 监控列 {column}, 条件 {condition}, 阈值 {threshold}")
    return f"提醒规则 '{rule_id}' 设置成功"

@mcp.tool
async def check_alerts() -> List[Dict[str, Any]]:
    """检查所有启用的提醒规则并返回触发的警报"""
    ctx = get_context()
    await ctx.info("开始检查提醒规则...")

    triggered_alerts = []

    for rule_id, rule in alert_rules.items():
        if not rule.enabled:
            continue

        try:
            # 获取最新数据点
            query = f"""
            SELECT {rule.column}, timestamp
            FROM sensor_data
            WHERE {rule.column} IS NOT NULL
            ORDER BY timestamp DESC
            LIMIT 1
            """
            df = await execute_query(query)

            if df.empty:
                continue

            current_value = float(df.iloc[0][rule.column])
            timestamp = df.iloc[0]['timestamp']

            triggered = False
            message = ""

            if rule.condition == "greater_than" and current_value > rule.threshold:
                triggered = True
                message = f"数值 {current_value} 超过上限阈值 {rule.threshold}"

            elif rule.condition == "less_than" and current_value < rule.threshold:
                triggered = True
                message = f"数值 {current_value} 低于下限阈值 {rule.threshold}"

            elif rule.condition == "range":
                # 假设阈值为允许的最大偏差
                mean_query = f"SELECT AVG({rule.column}) as avg_val FROM sensor_data WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 1 HOUR)"
                mean_df = await execute_query(mean_query)
                if not mean_df.empty:
                    mean_val = float(mean_df.iloc[0]['avg_val'])
                    if abs(current_value - mean_val) > rule.threshold:
                        triggered = True
                        message = f"数值 {current_value} 偏离均值 {mean_val} 超过 {rule.threshold}"

            if triggered:
                alert = {
                    "rule_id": rule_id,
                    "column": rule.column,
                    "current_value": current_value,
                    "threshold": rule.threshold,
                    "condition": rule.condition,
                    "message": message,
                    "timestamp": timestamp.isoformat(),
                    "severity": "high" if abs(current_value - rule.threshold) > rule.threshold * 0.5 else "medium"
                }
                triggered_alerts.append(alert)

                # 更新最后触发时间
                alert_rules[rule_id].last_triggered = datetime.now().isoformat()

                await ctx.warning(f"触发警报: {message}")

        except Exception as e:
            await ctx.error(f"检查规则 {rule_id} 时出错: {str(e)}")
            continue

    await ctx.info(f"检查完成，触发了 {len(triggered_alerts)} 个警报")
    return triggered_alerts

# 数据可视化工具
@mcp.tool
async def generate_chart(
    chart_type: Literal["line", "bar", "pie", "scatter", "heatmap", "histogram"] = Field(description="图表类型"),
    columns: List[str] = Field(description="要绘制的列名"),
    time_range: str = Field(default="24h", description="时间范围"),
    title: str = Field(default="数据图表", description="图表标题"),
    group_by: Optional[str] = Field(default=None, description="分组字段（可选）")
) -> str:
    """
    生成各种类型的数据可视化图表

    支持的图表类型：
    - line: 折线图（时间序列）
    - bar: 柱状图
    - pie: 饼状图
    - scatter: 散点图
    - heatmap: 热力图
    - histogram: 直方图
    """
    ctx = get_context()
    await ctx.info(f"生成 {chart_type} 图表，列: {columns}, 时间范围: {time_range}")

    # 解析时间范围
    now = datetime.now()
    if time_range.endswith('h'):
        hours = int(time_range[:-1])
        start_time = now - timedelta(hours=hours)
    elif time_range.endswith('d'):
        days = int(time_range[:-1])
        start_time = now - timedelta(days=days)
    else:
        start_time = now - timedelta(hours=24)

    # 构建查询
    columns_str = ", ".join(columns)
    query = f"""
    SELECT timestamp, {columns_str}
    FROM sensor_data
    WHERE timestamp >= %s
    ORDER BY timestamp
    """

    df = await execute_query(query, (start_time.strftime('%Y-%m-%d %H:%M:%S'),))

    if df.empty:
        await ctx.warning("指定时间范围内没有数据")
        return "无数据可显示"

    # 创建图表
    if chart_type == "line":
        fig = go.Figure()
        for col in columns:
            fig.add_trace(go.Scatter(
                x=df['timestamp'],
                y=df[col],
                mode='lines+markers',
                name=col,
                line=dict(width=2),
                marker=dict(size=4)
            ))
        fig.update_layout(
            title=title,
            xaxis_title="时间",
            yaxis_title="数值",
            hovermode='x unified'
        )

    elif chart_type == "bar":
        if len(columns) == 1:
            # 按时间分组的柱状图
            df_grouped = df.groupby(df['timestamp'].dt.hour)[columns[0]].mean().reset_index()
            fig = px.bar(df_grouped, x='timestamp', y=columns[0], title=title)
        else:
            # 多列对比柱状图
            fig = go.Figure()
            for col in columns:
                fig.add_trace(go.Bar(name=col, x=['平均值'], y=[df[col].mean()]))
            fig.update_layout(title=title, barmode='group')

    elif chart_type == "pie":
        if len(columns) == 1:
            # 将数值分段显示饼图
            col = columns[0]
            values = df[col].dropna()
            bins = pd.cut(values, bins=5, labels=['很低', '低', '中等', '高', '很高'])
            pie_data = bins.value_counts()
            fig = px.pie(values=pie_data.values, names=pie_data.index, title=title)
        else:
            # 多列平均值饼图
            avg_values = [df[col].mean() for col in columns]
            fig = px.pie(values=avg_values, names=columns, title=title)

    elif chart_type == "scatter":
        if len(columns) >= 2:
            fig = px.scatter(df, x=columns[0], y=columns[1],
                           color='timestamp' if len(columns) == 2 else columns[2],
                           title=title)
        else:
            fig = px.scatter(df, x='timestamp', y=columns[0], title=title)

    elif chart_type == "heatmap":
        # 创建相关性热力图
        corr_matrix = df[columns].corr()
        fig = px.imshow(corr_matrix,
                       labels=dict(color="相关系数"),
                       x=columns, y=columns,
                       title=f"{title} - 相关性热力图")

    elif chart_type == "histogram":
        fig = go.Figure()
        for col in columns:
            fig.add_trace(go.Histogram(x=df[col], name=col, opacity=0.7))
        fig.update_layout(title=title, barmode='overlay')

    # 设置通用布局
    fig.update_layout(
        template="plotly_white",
        font=dict(family="Arial, sans-serif", size=12),
        showlegend=True,
        width=800,
        height=600
    )

    # 转换为HTML
    chart_html = create_chart_base64(fig)

    await ctx.info(f"{chart_type} 图表生成完成")
    return chart_html

# 趋势分析工具
@mcp.tool
async def trend_analysis(
    columns: List[str] = Field(description="要分析的列名"),
    time_range: str = Field(default="7d", description="历史数据时间范围"),
    prediction_days: int = Field(default=3, description="预测天数"),
    method: Literal["linear", "polynomial", "moving_average"] = Field(default="linear", description="预测方法")
) -> Dict[str, Any]:
    """
    数据趋势分析和预测

    预测方法：
    - linear: 线性回归
    - polynomial: 多项式回归
    - moving_average: 移动平均
    """
    ctx = get_context()
    await ctx.info(f"开始趋势分析: 列={columns}, 方法={method}, 预测{prediction_days}天")

    # 解析时间范围
    now = datetime.now()
    if time_range.endswith('d'):
        days = int(time_range[:-1])
        start_time = now - timedelta(days=days)
    elif time_range.endswith('h'):
        hours = int(time_range[:-1])
        start_time = now - timedelta(hours=hours)
    else:
        start_time = now - timedelta(days=7)

    # 获取历史数据
    columns_str = ", ".join(columns)
    query = f"""
    SELECT timestamp, {columns_str}
    FROM sensor_data
    WHERE timestamp >= %s
    ORDER BY timestamp
    """

    df = await execute_query(query, (start_time.strftime('%Y-%m-%d %H:%M:%S'),))

    if df.empty or len(df) < 10:
        await ctx.warning("历史数据不足，无法进行趋势分析")
        return {"error": "数据不足"}

    results = {}

    for column in columns:
        try:
            # 准备数据
            data = df[[column, 'timestamp']].dropna()
            if len(data) < 5:
                continue

            # 将时间转换为数值（小时数）
            data['hours'] = (data['timestamp'] - data['timestamp'].min()).dt.total_seconds() / 3600
            X = data['hours'].values.reshape(-1, 1)
            y = data[column].values

            # 根据方法进行预测
            if method == "linear":
                from sklearn.linear_model import LinearRegression
                model = LinearRegression()
                model.fit(X, y)

                # 预测未来数据点
                future_hours = np.arange(X.max()[0], X.max()[0] + prediction_days * 24, 1).reshape(-1, 1)
                predictions = model.predict(future_hours)
                trend_slope = model.coef_[0]

            elif method == "polynomial":
                from sklearn.preprocessing import PolynomialFeatures
                from sklearn.linear_model import LinearRegression
                from sklearn.pipeline import Pipeline

                poly_model = Pipeline([
                    ('poly', PolynomialFeatures(degree=2)),
                    ('linear', LinearRegression())
                ])
                poly_model.fit(X, y)

                future_hours = np.arange(X.max()[0], X.max()[0] + prediction_days * 24, 1).reshape(-1, 1)
                predictions = poly_model.predict(future_hours)

                # 计算趋势（最后几个点的斜率）
                recent_pred = poly_model.predict(X[-5:])
                trend_slope = (recent_pred[-1] - recent_pred[0]) / 5

            elif method == "moving_average":
                # 简单移动平均
                window = min(24, len(y) // 4)  # 24小时窗口或数据的1/4
                ma = pd.Series(y).rolling(window=window).mean()
                last_ma = ma.iloc[-1]

                # 预测为最后的移动平均值
                predictions = np.full(prediction_days * 24, last_ma)
                trend_slope = (ma.iloc[-1] - ma.iloc[-window]) / window

            # 计算统计指标
            current_value = float(y[-1])
            predicted_value = float(predictions[-1]) if len(predictions) > 0 else current_value
            change_rate = ((predicted_value - current_value) / current_value * 100) if current_value != 0 else 0

            # 趋势判断
            if trend_slope > 0.1:
                trend_direction = "上升"
            elif trend_slope < -0.1:
                trend_direction = "下降"
            else:
                trend_direction = "稳定"

            # 生成预测时间点
            last_timestamp = data['timestamp'].max()
            future_timestamps = [
                (last_timestamp + timedelta(hours=i)).isoformat()
                for i in range(1, prediction_days * 24 + 1)
            ]

            results[column] = {
                "current_value": current_value,
                "predicted_value": predicted_value,
                "change_rate": change_rate,
                "trend_direction": trend_direction,
                "trend_slope": float(trend_slope),
                "method": method,
                "predictions": [float(p) for p in predictions[:24]],  # 只返回第一天的预测
                "prediction_timestamps": future_timestamps[:24],
                "confidence": "high" if len(data) > 100 else "medium" if len(data) > 50 else "low",
                "data_points": len(data)
            }

            await ctx.info(f"列 {column} 趋势分析完成: {trend_direction}趋势，变化率 {change_rate:.2f}%")

        except Exception as e:
            await ctx.error(f"分析列 {column} 时出错: {str(e)}")
            results[column] = {"error": str(e)}

    return {
        "analysis_time": datetime.now().isoformat(),
        "time_range": time_range,
        "prediction_days": prediction_days,
        "method": method,
        "results": results
    }

# 语音交互工具
@mcp.tool
async def voice_query(
    audio_file_path: str = Field(description="音频文件路径"),
    language: str = Field(default="zh-CN", description="语音识别语言")
) -> str:
    """
    语音查询数据 - 将语音转换为文本查询
    """
    ctx = get_context()
    await ctx.info(f"开始语音识别: {audio_file_path}")

    try:
        r = sr.Recognizer()

        # 读取音频文件
        with sr.AudioFile(audio_file_path) as source:
            audio = r.record(source)

        # 语音识别
        try:
            text = r.recognize_google(audio, language=language)
            await ctx.info(f"识别结果: {text}")

            # 简单的语音命令解析
            text_lower = text.lower()

            if "温度" in text and "平均" in text:
                # 执行温度平均值查询
                result = await statistical_analysis(
                    start_time=(datetime.now() - timedelta(hours=24)).strftime('%Y-%m-%d %H:%M:%S'),
                    end_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    columns=["temperature"],
                    operation="average"
                )
                return f"过去24小时温度平均值: {result[0].result:.2f}°C"

            elif "异常" in text:
                # 执行异常检测
                result = await anomaly_detection(
                    column="temperature",
                    threshold_type="statistical",
                    time_window="24h"
                )
                return f"发现 {result.anomaly_count} 个温度异常点，异常率 {result.anomaly_rate:.2%}"

            elif "图表" in text or "图" in text:
                # 生成图表
                chart = await generate_chart(
                    chart_type="line",
                    columns=["temperature", "pressure"],
                    time_range="24h",
                    title="过去24小时数据趋势"
                )
                return "已生成数据趋势图表"

            else:
                return f"语音识别成功: '{text}'，但未匹配到具体操作。请说出具体的查询需求，如'查询温度平均值'、'检测异常数据'等。"

        except sr.UnknownValueError:
            await ctx.warning("无法识别语音内容")
            return "语音识别失败，请重新录制"

        except sr.RequestError as e:
            await ctx.error(f"语音识别服务错误: {e}")
            return f"语音识别服务错误: {e}"

    except Exception as e:
        await ctx.error(f"语音处理错误: {str(e)}")
        return f"语音处理错误: {str(e)}"

@mcp.tool
async def text_to_speech(
    text: str = Field(description="要转换为语音的文本"),
    output_path: str = Field(default="output.wav", description="输出音频文件路径")
) -> str:
    """
    文本转语音播报
    """
    ctx = get_context()
    await ctx.info(f"开始文本转语音: {text[:50]}...")

    try:
        init_tts_engine()

        # 保存到文件
        tts_engine.save_to_file(text, output_path)
        tts_engine.runAndWait()

        await ctx.info(f"语音文件已保存到: {output_path}")
        return f"语音文件已生成: {output_path}"

    except Exception as e:
        await ctx.error(f"文本转语音错误: {str(e)}")
        return f"文本转语音错误: {str(e)}"

# 数据库管理工具
@mcp.tool
async def get_table_info() -> Dict[str, Any]:
    """获取数据库表结构信息"""
    ctx = get_context()
    await ctx.info("获取数据库表结构信息")

    try:
        # 获取表结构
        query = "DESCRIBE sensor_data"
        df = await execute_query(query)

        columns_info = []
        for _, row in df.iterrows():
            columns_info.append({
                "field": row['Field'],
                "type": row['Type'],
                "null": row['Null'],
                "key": row['Key'],
                "default": row['Default'],
                "extra": row['Extra']
            })

        # 获取数据统计
        count_query = "SELECT COUNT(*) as total_rows FROM sensor_data"
        count_df = await execute_query(count_query)
        total_rows = int(count_df.iloc[0]['total_rows'])

        # 获取时间范围
        time_range_query = """
        SELECT
            MIN(timestamp) as earliest,
            MAX(timestamp) as latest
        FROM sensor_data
        """
        time_df = await execute_query(time_range_query)

        return {
            "table_name": "sensor_data",
            "columns": columns_info,
            "total_rows": total_rows,
            "time_range": {
                "earliest": time_df.iloc[0]['earliest'].isoformat() if time_df.iloc[0]['earliest'] else None,
                "latest": time_df.iloc[0]['latest'].isoformat() if time_df.iloc[0]['latest'] else None
            },
            "database_config": {
                "host": db_config.host,
                "database": db_config.database,
                "charset": db_config.charset
            }
        }

    except Exception as e:
        await ctx.error(f"获取表信息错误: {str(e)}")
        return {"error": str(e)}

@mcp.tool
async def execute_custom_query(
    sql_query: str = Field(description="要执行的SQL查询语句"),
    limit: int = Field(default=100, description="结果限制行数")
) -> Dict[str, Any]:
    """
    执行自定义SQL查询（只读操作）
    """
    ctx = get_context()
    await ctx.info(f"执行自定义查询: {sql_query[:100]}...")

    # 安全检查 - 只允许SELECT查询
    query_upper = sql_query.upper().strip()
    if not query_upper.startswith('SELECT'):
        await ctx.error("只允许执行SELECT查询")
        return {"error": "只允许执行SELECT查询"}

    # 检查危险关键词
    dangerous_keywords = ['DELETE', 'UPDATE', 'INSERT', 'DROP', 'ALTER', 'CREATE']
    for keyword in dangerous_keywords:
        if keyword in query_upper:
            await ctx.error(f"查询包含危险关键词: {keyword}")
            return {"error": f"查询包含危险关键词: {keyword}"}

    try:
        # 添加LIMIT限制
        if 'LIMIT' not in query_upper:
            sql_query += f" LIMIT {limit}"

        df = await execute_query(sql_query)

        # 转换为字典格式
        result = {
            "columns": df.columns.tolist(),
            "rows": df.to_dict('records'),
            "row_count": len(df),
            "query": sql_query
        }

        await ctx.info(f"查询完成，返回 {len(df)} 行数据")
        return result

    except Exception as e:
        await ctx.error(f"查询执行错误: {str(e)}")
        return {"error": str(e)}

# 服务器启动和配置
if __name__ == "__main__":
    # 设置matplotlib中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 初始化语音引擎
    init_tts_engine()

    # 启动服务器
    print("🚀 数据库分析MCP服务器启动中...")
    print(f"📊 数据库连接: {db_config.host}:{db_config.port}/{db_config.database}")
    print("🎯 支持功能:")
    print("   • 统计分析 (statistical_analysis)")
    print("   • 异常检测 (anomaly_detection)")
    print("   • 智能提醒 (set_alert_rule, check_alerts)")
    print("   • 数据可视化 (generate_chart)")
    print("   • 趋势分析 (trend_analysis)")
    print("   • 语音交互 (voice_query, text_to_speech)")
    print("   • 数据库管理 (get_table_info, execute_custom_query)")
    print("\n🔧 使用STDIO传输，适合本地Claude Desktop连接")
    print("💡 安装命令: fastmcp install claude-desktop database_analysis_server.py")

    # 使用STDIO传输（本地连接）
    mcp.run()
