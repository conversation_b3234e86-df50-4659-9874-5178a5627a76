
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
        <style>
            body { margin: 0; padding: 10px; font-family: Arial, sans-serif; }
            #chart { width: 100%; height: 600px; }
        </style>
    </head>
    <body>
        <div id="chart"></div>
        <script>
            var chartDom = document.getElementById('chart');
            var myChart = echarts.init(chartDom);
            var option = {
  "title": {
    "text": "温度分布直方图",
    "left": "center",
    "textStyle": {
      "fontSize": 16,
      "fontWeight": "bold"
    }
  },
  "tooltip": {
    "trigger": "item",
    "axisPointer": {
      "type": "shadow"
    }
  },
  "legend": {
    "top": "bottom",
    "data": [
      "temperature"
    ]
  },
  "grid": {
    "left": "3%",
    "right": "4%",
    "bottom": "15%",
    "containLabel": true
  },
  "color": [
    "#5470c6",
    "#91cc75",
    "#fac858",
    "#ee6666",
    "#73c0de",
    "#3ba272",
    "#fc8452",
    "#9a60b4",
    "#ea7ccc",
    "#ff9f7f"
  ],
  "xAxis": {
    "type": "category",
    "data": [
      "8.48-9.76",
      "9.76-11.01",
      "11.01-12.27",
      "12.27-13.52",
      "13.52-14.77",
      "14.77-16.03",
      "16.03-17.28",
      "17.28-18.53",
      "18.53-19.79",
      "19.79-21.04",
      "21.04-22.29",
      "22.29-23.54",
      "23.54-24.80",
      "24.80-26.05",
      "26.05-27.30",
      "27.30-28.56",
      "28.56-29.81",
      "29.81-31.06",
      "31.06-32.32",
      "32.32-33.57"
    ],
    "axisLabel": {
      "rotate": 45
    }
  },
  "yAxis": {
    "type": "value",
    "name": "频次"
  },
  "series": [
    {
      "name": "temperature",
      "type": "bar",
      "data": [
        3,
        6,
        7,
        3,
        4,
        3,
        3,
        3,
        6,
        1,
        3,
        5,
        8,
        9,
        5,
        13,
        7,
        5,
        3,
        3
      ],
      "barWidth": "80%"
    }
  ]
};
            
            myChart.setOption(option);
            
            // 响应式调整
            window.addEventListener('resize', function() {
                myChart.resize();
            });
            
            // 错误处理
            myChart.on('error', function(params) {
                console.error('ECharts 渲染错误:', params);
            });
        </script>
    </body>
    </html>
    