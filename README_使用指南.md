# 🏭 企业级AI数据分析平台使用指南

基于FastMCP的智能数据分析和异常检测系统

## 🚀 快速开始

### 1. 启动MCP服务器
```bash
python mcp_client/simple_http_server.py
```
或双击 `start_mcp_server.bat`

### 2. 启动前端界面

#### 方法1：企业版前端（推荐）
```bash
streamlit run mcp_client/enterprise_ai_frontend.py --server.port=8501
```
或双击 `start_enterprise_frontend.bat`

#### 方法2：调试版前端（用于测试）
```bash
streamlit run mcp_client/simple_debug.py --server.port=8503
```
或双击 `start_debug_frontend.bat`

## 📊 功能特色

### 🏭 企业版前端 (http://localhost:8501)
- ✅ **快速开始面板** - 3个示例问题，一键测试
- ✅ **实时监控** - 数据可视化图表
- ✅ **AI分析** - 智能洞察、异常调查、预测维护
- ✅ **数据可视化** - 温度趋势图、统计仪表板
- ✅ **语音功能** - 语音识别和播报
- ✅ **全面调试** - 详细的错误信息和日志

### 🔧 调试版前端 (http://localhost:8503)
- ✅ **连接测试** - 逐步验证MCP连接
- ✅ **工具测试** - 可视化测试每个MCP工具
- ✅ **示例问题** - 预设的测试场景
- ✅ **详细日志** - 分级日志显示
- ✅ **故障排除** - 快速定位问题

## 💡 示例问题

### 📊 数据统计
1. **查看数据总量**
   - 工具：`advanced_statistical_analysis`
   - 操作：`count`
   - 结果：显示数据库中的记录总数

2. **分析温度数据**
   - 工具：`advanced_statistical_analysis`
   - 操作：`average`
   - 结果：计算平均温度

### 🔍 异常检测
3. **温度异常检测**
   - 工具：`intelligent_anomaly_detection`
   - 方法：`hybrid`（混合方法）
   - 结果：检测温度数据中的异常值

### ⚡ 系统监控
4. **系统状态检查**
   - 工具：`get_system_status`
   - 结果：显示MCP服务器和数据库状态

## 🔧 故障排除

### 常见问题

#### 1. 连接失败
**症状**：前端显示"MCP服务器连接失败"
**解决方案**：
- 检查MCP服务器是否运行在 http://127.0.0.1:8000/mcp/
- 确认端口8000没有被其他程序占用
- 查看服务器终端的错误信息

#### 2. 工具调用失败
**症状**：工具调用返回错误或空结果
**解决方案**：
- 检查参数格式是否正确
- 验证数据库连接状态
- 查看服务器日志获取详细错误信息

#### 3. 数据查询无结果
**症状**：统计分析返回0或空值
**解决方案**：
- 检查时间范围是否包含数据
- 验证列名是否正确（temperature, humidity等）
- 确认数据库中有测试数据

#### 4. 语音功能不工作
**症状**：语音播报或识别失败
**解决方案**：
- 检查是否安装了语音依赖包
- 确认系统音频设备正常
- 在调试信息中查看语音引擎状态

## 📈 系统状态监控

### 正常运行指标
- ✅ MCP服务器：FastMCP 2.10.5 运行中
- ✅ 数据库连接：连接池10个连接
- ✅ 工具调用：正常响应
- ✅ 前端界面：可访问

### 日志示例
```
[17:41:05] INFO: 获取系统状态
[17:41:05] INFO: 数据库配置: host=localhost, port=3306, user=root, database=sensor_data
[17:41:20] INFO: 数据库连接池初始化成功: 10 连接
[17:41:21] INFO: 查询完成: 1 行, 耗时 0.36s
```

## 🛠️ 开发信息

### 技术栈
- **后端**：FastMCP 2.10.5
- **前端**：Streamlit
- **数据库**：MySQL
- **可视化**：Plotly
- **语音**：pyttsx3, speech_recognition

### 文件结构
```
mcp_client/
├── enterprise_ai_frontend.py    # 企业版前端
├── simple_debug.py             # 调试版前端
├── simple_http_server.py       # MCP服务器
└── ...

start_enterprise_frontend.bat   # 企业版启动脚本
start_debug_frontend.bat        # 调试版启动脚本
README_使用指南.md              # 本文档
```

## 📞 技术支持

如果遇到问题：
1. 首先使用调试版前端进行诊断
2. 查看服务器和前端的日志信息
3. 检查系统状态和连接情况
4. 参考故障排除部分的解决方案

---

**版本**：v1.0  
**更新时间**：2025-08-02  
**作者**：Claude 4.0 Sonnet  
**基于**：FastMCP 2.10.5
