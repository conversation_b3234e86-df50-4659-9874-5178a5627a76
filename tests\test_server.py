#!/usr/bin/env python3
"""
数据库分析MCP服务器测试脚本
用于验证所有功能是否正常工作
"""

import asyncio
import json
import os
from datetime import datetime, timedelta
from pathlib import Path

# 设置环境变量（测试用）
os.environ.update({
    "DB_HOST": "localhost",
    "DB_PORT": "3306",
    "DB_USER": "root",
    "DB_PASSWORD": "12369874b",
    "DB_NAME": "sensor_data",
    "DB_CHARSET": "utf8mb4"
})

# 导入服务器模块
from database_analysis_server import (
    statistical_analysis,
    anomaly_detection,
    set_alert_rule,
    check_alerts,
    generate_chart,
    trend_analysis,
    get_table_info,
    execute_custom_query
)

class TestResults:
    """测试结果收集器"""
    def __init__(self):
        self.passed = 0
        self.failed = 0
        self.errors = []
    
    def assert_test(self, condition: bool, test_name: str, error_msg: str = ""):
        """断言测试结果"""
        if condition:
            print(f"✅ {test_name}")
            self.passed += 1
        else:
            print(f"❌ {test_name}: {error_msg}")
            self.failed += 1
            self.errors.append(f"{test_name}: {error_msg}")
    
    def summary(self):
        """打印测试总结"""
        total = self.passed + self.failed
        print(f"\n{'='*50}")
        print(f"测试总结: {self.passed}/{total} 通过")
        if self.failed > 0:
            print(f"失败的测试:")
            for error in self.errors:
                print(f"  - {error}")
        print(f"{'='*50}")

async def test_database_connection():
    """测试数据库连接"""
    print("🔍 测试数据库连接...")
    results = TestResults()
    
    try:
        table_info = await get_table_info()
        results.assert_test(
            "error" not in table_info,
            "数据库连接测试",
            table_info.get("error", "")
        )
        
        if "error" not in table_info:
            results.assert_test(
                table_info["total_rows"] > 0,
                "数据表包含数据",
                f"数据行数: {table_info['total_rows']}"
            )
            
            results.assert_test(
                len(table_info["columns"]) > 0,
                "数据表结构正常",
                f"列数: {len(table_info['columns'])}"
            )
            
            print(f"  📊 数据库: {table_info['database_config']['database']}")
            print(f"  📈 数据行数: {table_info['total_rows']}")
            print(f"  🏗️ 列数: {len(table_info['columns'])}")
            
    except Exception as e:
        results.assert_test(False, "数据库连接测试", str(e))
    
    return results

async def test_statistical_analysis():
    """测试统计分析功能"""
    print("\n📊 测试统计分析功能...")
    results = TestResults()
    
    try:
        # 测试温度平均值
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=24)
        
        stats = await statistical_analysis(
            start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
            end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
            columns=["temperature"],
            operation="average"
        )
        
        results.assert_test(
            len(stats) > 0,
            "温度平均值计算",
            "未返回统计结果"
        )
        
        if len(stats) > 0:
            temp_avg = stats[0]
            results.assert_test(
                temp_avg.result > 0,
                "温度平均值合理性检查",
                f"平均值: {temp_avg.result}"
            )
            print(f"  🌡️ 24小时温度平均值: {temp_avg.result:.2f}°C")
            print(f"  📊 数据点数量: {temp_avg.count}")
        
        # 测试多列统计
        multi_stats = await statistical_analysis(
            start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
            end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
            columns=["temperature", "pressure", "humidity"],
            operation="max"
        )
        
        results.assert_test(
            len(multi_stats) >= 2,
            "多列统计分析",
            f"返回结果数: {len(multi_stats)}"
        )
        
    except Exception as e:
        results.assert_test(False, "统计分析功能测试", str(e))
    
    return results

async def test_anomaly_detection():
    """测试异常检测功能"""
    print("\n🚨 测试异常检测功能...")
    results = TestResults()
    
    try:
        # 测试统计学异常检测
        anomaly_result = await anomaly_detection(
            column="temperature",
            threshold_type="statistical",
            time_window="24h",
            sensitivity=2.0
        )
        
        results.assert_test(
            anomaly_result.total_points > 0,
            "异常检测数据获取",
            f"数据点数: {anomaly_result.total_points}"
        )
        
        results.assert_test(
            anomaly_result.anomaly_rate >= 0,
            "异常率计算",
            f"异常率: {anomaly_result.anomaly_rate:.2%}"
        )
        
        print(f"  📊 检测数据点: {anomaly_result.total_points}")
        print(f"  🚨 异常点数量: {anomaly_result.anomaly_count}")
        print(f"  📈 异常率: {anomaly_result.anomaly_rate:.2%}")
        print(f"  🎯 检测阈值: {anomaly_result.threshold:.2f}")
        
        # 测试静态阈值检测
        static_result = await anomaly_detection(
            column="temperature",
            threshold_type="static",
            threshold_value=50.0,
            time_window="12h"
        )
        
        results.assert_test(
            static_result.threshold == 50.0,
            "静态阈值设置",
            f"设置阈值: {static_result.threshold}"
        )
        
    except Exception as e:
        results.assert_test(False, "异常检测功能测试", str(e))
    
    return results

async def test_alert_system():
    """测试提醒系统"""
    print("\n⚠️ 测试提醒系统...")
    results = TestResults()
    
    try:
        # 设置测试警报规则
        rule_result = await set_alert_rule(
            rule_id="test_temp_high",
            column="temperature",
            condition="greater_than",
            threshold=45.0,
            enabled=True
        )
        
        results.assert_test(
            "成功" in rule_result,
            "警报规则设置",
            rule_result
        )
        
        # 检查警报
        alerts = await check_alerts()
        
        results.assert_test(
            isinstance(alerts, list),
            "警报检查功能",
            f"返回类型: {type(alerts)}"
        )
        
        print(f"  📋 设置规则: test_temp_high (温度 > 45°C)")
        print(f"  🔍 当前触发警报数: {len(alerts)}")
        
        if alerts:
            for alert in alerts[:3]:  # 只显示前3个
                print(f"    🚨 {alert['column']}: {alert['current_value']:.2f} > {alert['threshold']:.2f}")
        
    except Exception as e:
        results.assert_test(False, "提醒系统测试", str(e))
    
    return results

async def test_visualization():
    """测试数据可视化功能"""
    print("\n📈 测试数据可视化功能...")
    results = TestResults()
    
    try:
        # 测试折线图生成
        line_chart = await generate_chart(
            chart_type="line",
            columns=["temperature", "humidity"],
            time_range="12h",
            title="温湿度趋势图"
        )
        
        results.assert_test(
            len(line_chart) > 100,  # HTML内容应该比较长
            "折线图生成",
            f"返回内容长度: {len(line_chart)}"
        )
        
        results.assert_test(
            "plotly" in line_chart.lower() or "data:image" in line_chart,
            "图表格式检查",
            "返回内容不包含预期的图表格式"
        )
        
        # 测试柱状图生成
        bar_chart = await generate_chart(
            chart_type="bar",
            columns=["pressure"],
            time_range="6h",
            title="压力分布图"
        )
        
        results.assert_test(
            len(bar_chart) > 50,
            "柱状图生成",
            f"返回内容长度: {len(bar_chart)}"
        )
        
        print(f"  📊 折线图生成成功 (长度: {len(line_chart)})")
        print(f"  📊 柱状图生成成功 (长度: {len(bar_chart)})")
        
    except Exception as e:
        results.assert_test(False, "数据可视化测试", str(e))
    
    return results

async def test_trend_analysis():
    """测试趋势分析功能"""
    print("\n📈 测试趋势分析功能...")
    results = TestResults()
    
    try:
        # 测试线性回归趋势分析
        trend_result = await trend_analysis(
            columns=["temperature"],
            time_range="24h",
            prediction_days=1,
            method="linear"
        )
        
        results.assert_test(
            "results" in trend_result,
            "趋势分析结果结构",
            "缺少results字段"
        )
        
        if "results" in trend_result and "temperature" in trend_result["results"]:
            temp_trend = trend_result["results"]["temperature"]
            
            results.assert_test(
                "error" not in temp_trend,
                "温度趋势分析",
                temp_trend.get("error", "")
            )
            
            if "error" not in temp_trend:
                results.assert_test(
                    "trend_direction" in temp_trend,
                    "趋势方向判断",
                    "缺少trend_direction字段"
                )
                
                print(f"  🌡️ 当前温度: {temp_trend.get('current_value', 'N/A'):.2f}°C")
                print(f"  📊 预测温度: {temp_trend.get('predicted_value', 'N/A'):.2f}°C")
                print(f"  📈 趋势方向: {temp_trend.get('trend_direction', 'N/A')}")
                print(f"  📉 变化率: {temp_trend.get('change_rate', 'N/A'):.2f}%")
                print(f"  🎯 置信度: {temp_trend.get('confidence', 'N/A')}")
        
        # 测试移动平均方法
        ma_result = await trend_analysis(
            columns=["pressure"],
            time_range="12h",
            prediction_days=1,
            method="moving_average"
        )
        
        results.assert_test(
            "results" in ma_result,
            "移动平均趋势分析",
            "移动平均分析失败"
        )
        
    except Exception as e:
        results.assert_test(False, "趋势分析测试", str(e))
    
    return results

async def test_custom_query():
    """测试自定义查询功能"""
    print("\n🔍 测试自定义查询功能...")
    results = TestResults()
    
    try:
        # 测试简单查询
        query_result = await execute_custom_query(
            sql_query="SELECT COUNT(*) as total_count FROM sensor_data",
            limit=10
        )
        
        results.assert_test(
            "error" not in query_result,
            "自定义查询执行",
            query_result.get("error", "")
        )
        
        if "error" not in query_result:
            results.assert_test(
                len(query_result["rows"]) > 0,
                "查询结果返回",
                f"返回行数: {len(query_result['rows'])}"
            )
            
            print(f"  📊 查询返回行数: {query_result['row_count']}")
            print(f"  🏗️ 列名: {query_result['columns']}")
            if query_result["rows"]:
                print(f"  📈 总数据量: {query_result['rows'][0].get('total_count', 'N/A')}")
        
        # 测试安全检查（应该被拒绝）
        dangerous_query = await execute_custom_query(
            sql_query="DELETE FROM sensor_data WHERE id = 1",
            limit=10
        )
        
        results.assert_test(
            "error" in dangerous_query,
            "危险查询安全检查",
            "危险查询未被拒绝"
        )
        
    except Exception as e:
        results.assert_test(False, "自定义查询测试", str(e))
    
    return results

async def main():
    """主测试流程"""
    print("🧪 数据库分析MCP服务器功能测试")
    print("=" * 60)
    
    # 收集所有测试结果
    all_results = TestResults()
    
    # 执行各项测试
    test_functions = [
        test_database_connection,
        test_statistical_analysis,
        test_anomaly_detection,
        test_alert_system,
        test_visualization,
        test_trend_analysis,
        test_custom_query
    ]
    
    for test_func in test_functions:
        try:
            result = await test_func()
            all_results.passed += result.passed
            all_results.failed += result.failed
            all_results.errors.extend(result.errors)
        except Exception as e:
            print(f"❌ 测试 {test_func.__name__} 时发生错误: {e}")
            all_results.failed += 1
            all_results.errors.append(f"{test_func.__name__}: {e}")
    
    # 打印总结
    all_results.summary()
    
    # 生成测试报告
    report = {
        "test_time": datetime.now().isoformat(),
        "total_tests": all_results.passed + all_results.failed,
        "passed": all_results.passed,
        "failed": all_results.failed,
        "success_rate": all_results.passed / (all_results.passed + all_results.failed) * 100,
        "errors": all_results.errors
    }
    
    with open("test_report.json", "w", encoding="utf-8") as f:
        json.dump(report, f, ensure_ascii=False, indent=2)
    
    print(f"\n📄 详细测试报告已保存到: test_report.json")
    
    if all_results.failed == 0:
        print("🎉 所有测试通过！服务器功能正常。")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查配置和数据库连接。")
        return 1

if __name__ == "__main__":
    import sys
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
