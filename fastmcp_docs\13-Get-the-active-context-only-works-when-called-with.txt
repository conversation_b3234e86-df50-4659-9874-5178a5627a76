# FastMCP 文档 - 第 13 部分
# 主要内容: Get the active context - only works when called within a request
# 包含段落: 75 个
# 总行数: 1017

================================================================================

## Get the active context - only works when called within a request
类型: docs, 行数: 6

    # Get the active context - only works when called within a request
    ctx = get_context()    
    await ctx.info(f"Processing {len(data)} data points")
    
@mcp.tool
async def analyze_dataset(dataset_name: str) -> dict:

------------------------------------------------------------

## Call utility function that uses context internally
类型: api, 行数: 10

    # Call utility function that uses context internally
    data = load_data(dataset_name)
    await process_data(data)
```

**Important Notes:**

* The `get_context` function should only be used within the context of a server request. Calling it outside of a request will raise a `RuntimeError`.
* The `get_context` function is server-only and should not be used in client code.


------------------------------------------------------------

## Context Capabilities
类型: docs, 行数: 4

## Context Capabilities

FastMCP provides several advanced capabilities through the context object. Each capability has dedicated documentation with comprehensive examples and best practices:


------------------------------------------------------------

## Logging
类型: docs, 行数: 13

### Logging

Send debug, info, warning, and error messages back to the MCP client for visibility into function execution.

```python
await ctx.debug("Starting analysis")
await ctx.info(f"Processing {len(data)} items") 
await ctx.warning("Deprecated parameter used")
await ctx.error("Processing failed")
```

See [Server Logging](/servers/logging) for complete documentation and examples.


------------------------------------------------------------

## Client Elicitation
类型: docs, 行数: 14

### Client Elicitation

<VersionBadge version="2.10.0" />

Request structured input from clients during tool execution, enabling interactive workflows and progressive disclosure. This is a new feature in the 6/18/2025 MCP spec.

```python
result = await ctx.elicit("Enter your name:", response_type=str)
if result.action == "accept":
    name = result.data
```

See [User Elicitation](/servers/elicitation) for detailed examples and supported response types.


------------------------------------------------------------

## LLM Sampling
类型: docs, 行数: 12

### LLM Sampling

<VersionBadge version="2.0.0" />

Request the client's LLM to generate text based on provided messages, useful for leveraging AI capabilities within your tools.

```python
response = await ctx.sample("Analyze this data", temperature=0.7)
```

See [LLM Sampling](/servers/sampling) for comprehensive usage and advanced techniques.


------------------------------------------------------------

## Progress Reporting
类型: docs, 行数: 10

### Progress Reporting

Update clients on the progress of long-running operations, enabling progress indicators and better user experience.

```python
await ctx.report_progress(progress=50, total=100)  # 50% complete
```

See [Progress Reporting](/servers/progress) for detailed patterns and examples.


------------------------------------------------------------

## Resource Access
类型: docs, 行数: 13

### Resource Access

Read data from resources registered with your FastMCP server, allowing access to files, configuration, or dynamic content.

```python
content_list = await ctx.read_resource("resource://config")
content = content_list[0].content
```

**Method signature:**

* **`ctx.read_resource(uri: str | AnyUrl) -> list[ReadResourceContents]`**: Returns a list of resource content parts


------------------------------------------------------------

## Change Notifications
类型: docs, 行数: 10

### Change Notifications

<VersionBadge version="2.9.1" />

FastMCP automatically sends list change notifications when components (such as tools, resources, or prompts) are added, removed, enabled, or disabled. In rare cases where you need to manually trigger these notifications, you can use the context methods:

```python
@mcp.tool
async def custom_tool_management(ctx: Context) -> str:
    """Example of manual notification after custom tool changes."""

------------------------------------------------------------

## After making custom changes to tools
类型: docs, 行数: 9

    # After making custom changes to tools
    await ctx.send_tool_list_changed()
    await ctx.send_resource_list_changed()
    await ctx.send_prompt_list_changed()
    return "Notifications sent"
```

These methods are primarily used internally by FastMCP's automatic notification system and most users will not need to invoke them directly.


------------------------------------------------------------

## FastMCP Server
类型: docs, 行数: 7

### FastMCP Server

To access the underlying FastMCP server instance, you can use the `ctx.fastmcp` property:

```python
@mcp.tool
async def my_tool(ctx: Context) -> None:

------------------------------------------------------------

## Access the FastMCP server instance
类型: docs, 行数: 5

    # Access the FastMCP server instance
    server_name = ctx.fastmcp.name
    ...
```


------------------------------------------------------------

## MCP Request
类型: docs, 行数: 25

### MCP Request

Access metadata about the current request and client.

```python
@mcp.tool
async def request_info(ctx: Context) -> dict:
    """Return information about the current request."""
    return {
        "request_id": ctx.request_id,
        "client_id": ctx.client_id or "Unknown client"
    }
```

**Available Properties:**

* **`ctx.request_id -> str`**: Get the unique ID for the current MCP request
* **`ctx.client_id -> str | None`**: Get the ID of the client making the request, if provided during initialization
* **`ctx.session_id -> str | None`**: Get the MCP session ID for session-based data sharing (HTTP transports only)

<Warning>
  The MCP request is part of the low-level MCP SDK and intended for advanced use cases. Most users will not need to use it directly.
</Warning>



------------------------------------------------------------

## User Elicitation
类型: docs, 行数: 22

# User Elicitation
Source: https://gofastmcp.com/servers/elicitation

Request structured input from users during tool execution through the MCP context.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

<VersionBadge version="2.10.0" />

User elicitation allows MCP servers to request structured input from users during tool execution. Instead of requiring all inputs upfront, tools can interactively ask for missing parameters, clarification, or additional context as needed.

<Tip>
  Most of the examples in this document assume you have a FastMCP server instance named `mcp` and show how to use the `ctx.elicit` method to request user input from an `@mcp.tool`-decorated function.
</Tip>


------------------------------------------------------------

## What is Elicitation?
类型: docs, 行数: 11

## What is Elicitation?

Elicitation enables tools to pause execution and request specific information from users. This is particularly useful for:

* **Missing parameters**: Ask for required information not provided initially
* **Clarification requests**: Get user confirmation or choices for ambiguous scenarios
* **Progressive disclosure**: Collect complex information step-by-step
* **Dynamic workflows**: Adapt tool behavior based on user responses

For example, a file management tool might ask "Which directory should I create?" or a data analysis tool might request "What date range should I analyze?"


------------------------------------------------------------

## Basic Usage
类型: docs, 行数: 32

### Basic Usage

Use the `ctx.elicit()` method within any tool function to request user input:

```python {14-17}
from fastmcp import FastMCP, Context
from dataclasses import dataclass

mcp = FastMCP("Elicitation Server")

@dataclass
class UserInfo:
    name: str
    age: int

@mcp.tool
async def collect_user_info(ctx: Context) -> str:
    """Collect user information through interactive prompts."""
    result = await ctx.elicit(
        message="Please provide your information",
        response_type=UserInfo
    )
    
    if result.action == "accept":
        user = result.data
        return f"Hello {user.name}, you are {user.age} years old"
    elif result.action == "decline":
        return "Information not provided"
    else:  # cancel
        return "Operation cancelled"
```


------------------------------------------------------------

## Method Signature
类型: api, 行数: 32

## Method Signature

<Card icon="code" title="Context Elicitation Method">
  <ResponseField name="ctx.elicit" type="async method">
    <Expandable title="Parameters">
      <ResponseField name="message" type="str">
        The prompt message to display to the user
      </ResponseField>

      <ResponseField name="response_type" type="type" default="None">
        The Python type defining the expected response structure (dataclass, primitive type, etc.) Note that elicitation responses are subject to a restricted subset of JSON Schema types. See [Supported Response Types](#supported-response-types) for more details.
      </ResponseField>
    </Expandable>

    <Expandable title="Response">
      <ResponseField name="ElicitationResult" type="object">
        Result object containing the user's response

        <Expandable title="properties">
          <ResponseField name="action" type="Literal['accept', 'decline', 'cancel']">
            How the user responded to the request
          </ResponseField>

          <ResponseField name="data" type="response_type | None">
            The user's input data (only present when action is "accept")
          </ResponseField>
        </Expandable>
      </ResponseField>
    </Expandable>
  </ResponseField>
</Card>


------------------------------------------------------------

## Elicitation Actions
类型: docs, 行数: 43

## Elicitation Actions

The elicitation result contains an `action` field indicating how the user responded:

* **`accept`**: User provided valid input - data is available in the `data` field
* **`decline`**: User chose not to provide the requested information and the data field is `None`
* **`cancel`**: User cancelled the entire operation and the data field is `None`

```python {5, 7}
@mcp.tool
async def my_tool(ctx: Context) -> str:
    result = await ctx.elicit("Choose an action")

    if result.action == "accept":
        return "Accepted!"
    elif result.action == "decline":
        return "Declined!"
    else:
        return "Cancelled!"
```

FastMCP also provides typed result classes for pattern matching on the `action` field:

```python {1-5, 12, 14, 16}
from fastmcp.server.elicitation import (
    AcceptedElicitation, 
    DeclinedElicitation, 
    CancelledElicitation,
)

@mcp.tool
async def pattern_example(ctx: Context) -> str:
    result = await ctx.elicit("Enter your name:", response_type=str)
    
    match result:
        case AcceptedElicitation(data=name):
            return f"Hello {name}!"
        case DeclinedElicitation():
            return "No name provided"
        case CancelledElicitation():
            return "Operation cancelled"
```


------------------------------------------------------------

## Response Types
类型: docs, 行数: 8

## Response Types

The server must send a schema to the client indicating the type of data it expects in response to the elicitation request. If the request is `accept`-ed, the client must send a response that matches the schema.

The MCP spec only supports a limited subset of JSON Schema types for elicitation responses. Specifically, it only supports JSON  **objects** with **primitive** properties including `string`, `number` (or `integer`), `boolean` and `enum` fields.

FastMCP makes it easy to request a broader range of types, including scalars (e.g. `str`) or no response at all, by automatically wrapping them in MCP-compatible object schemas.


------------------------------------------------------------

## Scalar Types
类型: docs, 行数: 43

### Scalar Types

You can request simple scalar data types for basic input, such as a string, integer, or boolean.

When you request a scalar type, FastMCP automatically wraps it in an object schema for MCP spec compatibility. Clients will see a corresponding schema requesting a single "value" field of the requested type. Once clients respond, the provided object is "unwrapped" and the scalar value is returned to your tool function as the `data` field of the `ElicitationResult` object.

As a developer, this means you do not have to worry about creating or accessing a structured object when you only need a scalar value.

<CodeGroup>
  ```python {4} title="Request a string"
  @mcp.tool
  async def get_user_name(ctx: Context) -> str:
      """Get the user's name."""
      result = await ctx.elicit("What's your name?", response_type=str)
      
      if result.action == "accept":
          return f"Hello, {result.data}!"
      return "No name provided"
  ```

  ```python {4} title="Request an integer"
  @mcp.tool
  async def pick_a_number(ctx: Context) -> str:
      """Pick a number."""
      result = await ctx.elicit("Pick a number!", response_type=int)
      
      if result.action == "accept":
          return f"You picked {result.data}"
      return "No number provided"
  ```

  ```python {4} title="Request a boolean"
  @mcp.tool
  async def pick_a_boolean(ctx: Context) -> str:
      """Pick a boolean."""
      result = await ctx.elicit("True or false?", response_type=bool)
      
      if result.action == "accept":
          return f"You picked {result.data}"
      return "No boolean provided"
  ```
</CodeGroup>


------------------------------------------------------------

## No Response
类型: docs, 行数: 16

### No Response

Sometimes, the goal of an elicitation is to simply get a user to approve or reject an action. In this case, you can pass `None` as the response type to indicate that no response is expected. In order to comply with the MCP spec, the client will see a schema requesting an empty object in response. In this case, the `data` field of the `ElicitationResult` object will be `None` when the user accepts the elicitation.

```python {4} title="No response"
@mcp.tool
async def approve_action(ctx: Context) -> str:
    """Approve an action."""
    result = await ctx.elicit("Approve this action?", response_type=None)

    if result.action == "accept":
        return do_action()
    else:
        raise ValueError("Action rejected")
```


------------------------------------------------------------

## Constrained Options
类型: docs, 行数: 53

### Constrained Options

Often you'll want to constrain the user's response to a specific set of values. You can do this by using a `Literal` type or a Python enum as the response type, or by passing a list of strings to the `response_type` parameter as a convenient shortcut.

<CodeGroup>
  ```python {6} title="Using a list of strings"
  @mcp.tool
  async def set_priority(ctx: Context) -> str:
      """Set task priority level."""
      result = await ctx.elicit(
          "What priority level?", 
          response_type=["low", "medium", "high"],
      )
      
      if result.action == "accept":
          return f"Priority set to: {result.data}"
  ```

  ```python {1, 8} title="Using a Literal type"
  from typing import Literal

  @mcp.tool
  async def set_priority(ctx: Context) -> str:
      """Set task priority level."""
      result = await ctx.elicit(
          "What priority level?", 
          response_type=Literal["low", "medium", "high"]
      )
      
      if result.action == "accept":
          return f"Priority set to: {result.data}"
      return "No priority set"
  ```

  ```python {1, 11} title="Using a Python enum"
  from enum import Enum

  class Priority(Enum):
      LOW = "low"
      MEDIUM = "medium"
      HIGH = "high"   

  @mcp.tool
  async def set_priority(ctx: Context) -> str:
      """Set task priority level."""
      result = await ctx.elicit("What priority level?", response_type=Priority)
      
      if result.action == "accept":
          return f"Priority set to: {result.data.value}"
      return "No priority set"
  ```
</CodeGroup>


------------------------------------------------------------

## Structured Responses
类型: docs, 行数: 29

### Structured Responses

You can request structured data with multiple fields by using a dataclass, typed dict, or Pydantic model as the response type. Note that the MCP spec only supports shallow objects with scalar (string, number, boolean) or enum properties.

```python {1, 16, 20}
from dataclasses import dataclass
from typing import Literal

@dataclass
class TaskDetails:
    title: str
    description: str
    priority: Literal["low", "medium", "high"]
    due_date: str

@mcp.tool
async def create_task(ctx: Context) -> str:
    """Create a new task with user-provided details."""
    result = await ctx.elicit(
        "Please provide task details",
        response_type=TaskDetails
    )
    
    if result.action == "accept":
        task = result.data
        return f"Created task: {task.title} (Priority: {task.priority})"
    return "Task creation cancelled"
```


------------------------------------------------------------

## Multi-Turn Elicitation
类型: docs, 行数: 9

## Multi-Turn Elicitation

Tools can make multiple elicitation calls to gather information progressively:

```python {6, 11, 16-19}
@mcp.tool
async def plan_meeting(ctx: Context) -> str:
    """Plan a meeting by gathering details step by step."""
    

------------------------------------------------------------

## Get meeting title
类型: docs, 行数: 5

    # Get meeting title
    title_result = await ctx.elicit("What's the meeting title?", response_type=str)
    if title_result.action != "accept":
        return "Meeting planning cancelled"
    

------------------------------------------------------------

## Get duration
类型: docs, 行数: 5

    # Get duration
    duration_result = await ctx.elicit("Duration in minutes?", response_type=int)
    if duration_result.action != "accept":
        return "Meeting planning cancelled"
    

------------------------------------------------------------

## Get priority
类型: docs, 行数: 12

    # Get priority
    priority_result = await ctx.elicit(
        "Is this urgent?", 
        response_type=Literal["yes", "no"]
    )
    if priority_result.action != "accept":
        return "Meeting planning cancelled"
    
    urgent = priority_result.data == "yes"
    return f"Meeting '{title_result.data}' planned for {duration_result.data} minutes (Urgent: {urgent})"
```


------------------------------------------------------------

## Client Requirements
类型: docs, 行数: 7

## Client Requirements

Elicitation requires the client to implement an elicitation handler. See [Client Elicitation](/clients/elicitation) for details on how clients can handle these requests.

If a client doesn't support elicitation, calls to `ctx.elicit()` will raise an error indicating that elicitation is not supported.



------------------------------------------------------------

## Server Logging
类型: docs, 行数: 20

# Server Logging
Source: https://gofastmcp.com/servers/logging

Send log messages back to MCP clients through the context.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

<Tip>
  This documentation covers **MCP client logging** - sending messages from your server to MCP clients. For standard server-side logging (e.g., writing to files, console), use `fastmcp.utilities.logging.get_logger()` or Python's built-in `logging` module.
</Tip>

Server logging allows MCP tools to send debug, info, warning, and error messages back to the client. This provides visibility into function execution and helps with debugging during development and operation.


------------------------------------------------------------

## Why Use Server Logging?
类型: docs, 行数: 11

## Why Use Server Logging?

Server logging is essential for:

* **Debugging**: Send detailed execution information to help diagnose issues
* **Progress visibility**: Keep users informed about what the tool is doing
* **Error reporting**: Communicate problems and their context to clients
* **Audit trails**: Create records of tool execution for compliance or analysis

Unlike standard Python logging, MCP server logging sends messages directly to the client, making them visible in the client's interface or logs.


------------------------------------------------------------

## Basic Usage
类型: docs, 行数: 29

### Basic Usage

Use the context logging methods within any tool function:

```python {8-9, 13, 17, 21}
from fastmcp import FastMCP, Context

mcp = FastMCP("LoggingDemo")

@mcp.tool
async def analyze_data(data: list[float], ctx: Context) -> dict:
    """Analyze numerical data with comprehensive logging."""
    await ctx.debug("Starting analysis of numerical data")
    await ctx.info(f"Analyzing {len(data)} data points")
    
    try:
        if not data:
            await ctx.warning("Empty data list provided")
            return {"error": "Empty data list"}
        
        result = sum(data) / len(data)
        await ctx.info(f"Analysis complete, average: {result}")
        return {"average": result, "count": len(data)}
        
    except Exception as e:
        await ctx.error(f"Analysis failed: {str(e)}")
        raise
```


------------------------------------------------------------

## Logging Methods
类型: api, 行数: 62

## Logging Methods

<Card icon="code" title="Context Logging Methods">
  <ResponseField name="ctx.debug" type="async method">
    Send debug-level messages for detailed execution information

    <Expandable title="parameters">
      <ResponseField name="message" type="str">
        The debug message to send to the client
      </ResponseField>
    </Expandable>
  </ResponseField>

  <ResponseField name="ctx.info" type="async method">
    Send informational messages about normal execution

    <Expandable title="parameters">
      <ResponseField name="message" type="str">
        The information message to send to the client
      </ResponseField>
    </Expandable>
  </ResponseField>

  <ResponseField name="ctx.warning" type="async method">
    Send warning messages for potential issues that didn't prevent execution

    <Expandable title="parameters">
      <ResponseField name="message" type="str">
        The warning message to send to the client
      </ResponseField>
    </Expandable>
  </ResponseField>

  <ResponseField name="ctx.error" type="async method">
    Send error messages for problems that occurred during execution

    <Expandable title="parameters">
      <ResponseField name="message" type="str">
        The error message to send to the client
      </ResponseField>
    </Expandable>
  </ResponseField>

  <ResponseField name="ctx.log" type="async method">
    Generic logging method with custom level and logger name

    <Expandable title="parameters">
      <ResponseField name="level" type="Literal['debug', 'info', 'warning', 'error']">
        The log level for the message
      </ResponseField>

      <ResponseField name="message" type="str">
        The message to send to the client
      </ResponseField>

      <ResponseField name="logger_name" type="str | None" default="None">
        Optional custom logger name for categorizing messages
      </ResponseField>
    </Expandable>
  </ResponseField>
</Card>


------------------------------------------------------------

## Log Levels
类型: docs, 行数: 2

## Log Levels


------------------------------------------------------------

## Debug
类型: docs, 行数: 11

### Debug

Use for detailed information that's typically only useful when diagnosing problems:

```python
@mcp.tool
async def process_file(file_path: str, ctx: Context) -> str:
    """Process a file with detailed debug logging."""
    await ctx.debug(f"Starting to process file: {file_path}")
    await ctx.debug("Checking file permissions")
    

------------------------------------------------------------

## File processing logic
类型: docs, 行数: 5

    # File processing logic
    await ctx.debug("File processing completed successfully")
    return "File processed"
```


------------------------------------------------------------

## Info
类型: docs, 行数: 14

### Info

Use for general information about normal program execution:

```python
@mcp.tool
async def backup_database(ctx: Context) -> str:
    """Backup database with progress information."""
    await ctx.info("Starting database backup")
    await ctx.info("Connecting to database")
    await ctx.info("Backup completed successfully")
    return "Database backed up"
```


------------------------------------------------------------

## Warning
类型: docs, 行数: 17

### Warning

Use for potentially harmful situations that don't prevent execution:

```python
@mcp.tool
async def validate_config(config: dict, ctx: Context) -> dict:
    """Validate configuration with warnings for deprecated options."""
    if "old_api_key" in config:
        await ctx.warning("Using deprecated 'old_api_key' field. Please use 'api_key' instead")
    
    if config.get("timeout", 30) > 300:
        await ctx.warning("Timeout value is very high (>5 minutes), this may cause issues")
    
    return {"status": "valid", "warnings": "see logs"}
```


------------------------------------------------------------

## Error
类型: docs, 行数: 13

### Error

Use for error events that might still allow the application to continue:

```python
@mcp.tool
async def batch_process(items: list[str], ctx: Context) -> dict:
    """Process multiple items, logging errors for failed items."""
    successful = 0
    failed = 0
    
    for item in items:
        try:

------------------------------------------------------------

## Process item
类型: docs, 行数: 9

            # Process item
            successful += 1
        except Exception as e:
            await ctx.error(f"Failed to process item '{item}': {str(e)}")
            failed += 1
    
    return {"successful": successful, "failed": failed}
```


------------------------------------------------------------

## Client Handling
类型: docs, 行数: 11

## Client Handling

Log messages are sent to the client through the MCP protocol. How clients handle these messages depends on their implementation:

* **Development clients**: May display logs in real-time for debugging
* **Production clients**: May store logs for later analysis or display to users
* **Integration clients**: May forward logs to external logging systems

See [Client Logging](/clients/logging) for details on how clients can handle server log messages.



------------------------------------------------------------

## MCP Middleware
类型: docs, 行数: 26

# MCP Middleware
Source: https://gofastmcp.com/servers/middleware

Add cross-cutting functionality to your MCP server with middleware that can inspect, modify, and respond to all MCP requests and responses.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

<VersionBadge version="2.9.0" />

MCP middleware is a powerful concept that allows you to add cross-cutting functionality to your FastMCP server. Unlike traditional web middleware, MCP middleware is designed specifically for the Model Context Protocol, providing hooks for different types of MCP operations like tool calls, resource reads, and prompt requests.

<Tip>
  MCP middleware is a FastMCP-specific concept and is not part of the official MCP protocol specification. This middleware system is designed to work with FastMCP servers and may not be compatible with other MCP implementations.
</Tip>

<Warning>
  MCP middleware is a brand new concept and may be subject to breaking changes in future versions.
</Warning>


------------------------------------------------------------

## What is MCP Middleware?
类型: docs, 行数: 13

## What is MCP Middleware?

MCP middleware lets you intercept and modify MCP requests and responses as they flow through your server. Think of it as a pipeline where each piece of middleware can inspect what's happening, make changes, and then pass control to the next middleware in the chain.

Common use cases for MCP middleware include:

* **Authentication and Authorization**: Verify client permissions before executing operations
* **Logging and Monitoring**: Track usage patterns and performance metrics
* **Rate Limiting**: Control request frequency per client or operation type
* **Request/Response Transformation**: Modify data before it reaches tools or after it leaves
* **Caching**: Store frequently requested data to improve performance
* **Error Handling**: Provide consistent error responses across your server


------------------------------------------------------------

## How Middleware Works
类型: docs, 行数: 23

## How Middleware Works

FastMCP middleware operates on a pipeline model. When a request comes in, it flows through your middleware in the order they were added to the server. Each middleware can:

1. **Inspect the incoming request** and its context
2. **Modify the request** before passing it to the next middleware or handler
3. **Execute the next middleware/handler** in the chain by calling `call_next()`
4. **Inspect and modify the response** before returning it
5. **Handle errors** that occur during processing

The key insight is that middleware forms a chain where each piece decides whether to continue processing or stop the chain entirely.

If you're familiar with ASGI middleware, the basic structure of FastMCP middleware will feel familiar. At its core, middleware is a callable class that receives a context object containing information about the current JSON-RPC message and a handler function to continue the middleware chain.

It's important to understand that MCP operates on the [JSON-RPC specification](https://spec.modelcontextprotocol.io/specification/basic/transports/). While FastMCP presents requests and responses in a familiar way, these are fundamentally JSON-RPC messages, not HTTP request/response pairs like you might be used to in web applications. FastMCP middleware works with all [transport types](/clients/transports), including local stdio transport and HTTP transports, though not all middleware implementations are compatible across all transports (e.g., middleware that inspects HTTP headers won't work with stdio transport).

The most fundamental way to implement middleware is by overriding the `__call__` method on the `Middleware` base class:

```python
from fastmcp.server.middleware import Middleware, MiddlewareContext

class RawMiddleware(Middleware):
    async def __call__(self, context: MiddlewareContext, call_next):

------------------------------------------------------------

## This method receives ALL messages regardless of type
类型: api, 行数: 9

        # This method receives ALL messages regardless of type
        print(f"Raw middleware processing: {context.method}")
        result = await call_next(context)
        print(f"Raw middleware completed: {context.method}")
        return result
```

This gives you complete control over every message that flows through your server, but requires you to handle all message types manually.


------------------------------------------------------------

## Middleware Hooks
类型: docs, 行数: 4

## Middleware Hooks

To make it easier for users to target specific types of messages, FastMCP middleware provides a variety of specialized hooks. Instead of implementing the raw `__call__` method, you can override specific hook methods that are called only for certain types of operations, allowing you to target exactly the level of specificity you need for your middleware logic.


------------------------------------------------------------

## Hook Hierarchy and Execution Order
类型: docs, 行数: 21

### Hook Hierarchy and Execution Order

FastMCP provides multiple hooks that are called with varying levels of specificity. Understanding this hierarchy is crucial for effective middleware design.

When a request comes in, **multiple hooks may be called for the same request**, going from general to specific:

1. **`on_message`** - Called for ALL MCP messages (both requests and notifications)
2. **`on_request` or `on_notification`** - Called based on the message type
3. **Operation-specific hooks** - Called for specific MCP operations like `on_call_tool`

For example, when a client calls a tool, your middleware will receive **multiple hook calls**:

1. `on_message` and `on_request` for any initial tool discovery operations (list\_tools)
2. `on_message` (because it's any MCP message) for the tool call itself
3. `on_request` (because tool calls expect responses) for the tool call itself
4. `on_call_tool` (because it's specifically a tool execution) for the tool call itself

Note that the MCP SDK may perform additional operations like listing tools for caching purposes, which will trigger additional middleware calls beyond just the direct tool execution.

This hierarchy allows you to target your middleware logic with the right level of specificity. Use `on_message` for broad concerns like logging, `on_request` for authentication, and `on_call_tool` for tool-specific logic like performance monitoring.


------------------------------------------------------------

## Available Hooks
类型: docs, 行数: 13

### Available Hooks

* `on_message`: Called for all MCP messages (requests and notifications)
* `on_request`: Called specifically for MCP requests (that expect responses)
* `on_notification`: Called specifically for MCP notifications (fire-and-forget)
* `on_call_tool`: Called when tools are being executed
* `on_read_resource`: Called when resources are being read
* `on_get_prompt`: Called when prompts are being retrieved
* `on_list_tools`: Called when listing available tools
* `on_list_resources`: Called when listing available resources
* `on_list_resource_templates`: Called when listing resource templates
* `on_list_prompts`: Called when listing available prompts


------------------------------------------------------------

## Component Access in Middleware
类型: docs, 行数: 4

## Component Access in Middleware

Understanding how to access component information (tools, resources, prompts) in middleware is crucial for building powerful middleware functionality. The access patterns differ significantly between listing operations and execution operations.


------------------------------------------------------------

## Listing Operations vs Execution Operations
类型: docs, 行数: 17

### Listing Operations vs Execution Operations

FastMCP middleware handles two types of operations differently:

**Listing Operations** (`on_list_tools`, `on_list_resources`, `on_list_prompts`, etc.):

* Middleware receives **FastMCP component objects** with full metadata
* These objects include FastMCP-specific properties like `tags` that aren't part of the MCP specification
* The result contains complete component information before it's converted to MCP format
* Tags and other metadata are stripped when finally returned to the MCP client

**Execution Operations** (`on_call_tool`, `on_read_resource`, `on_get_prompt`):

* Middleware runs **before** the component is executed
* The middleware result is either the execution result or an error if the component wasn't found
* Component metadata isn't directly available in the hook parameters


------------------------------------------------------------

## Accessing Component Metadata During Execution
类型: docs, 行数: 10

### Accessing Component Metadata During Execution

If you need to check component properties (like tags) during execution operations, use the FastMCP server instance available through the context:

```python
from fastmcp.server.middleware import Middleware, MiddlewareContext
from fastmcp.exceptions import ToolError

class TagBasedMiddleware(Middleware):
    async def on_call_tool(self, context: MiddlewareContext, call_next):

------------------------------------------------------------

## Access the tool object to check its metadata
类型: docs, 行数: 5

        # Access the tool object to check its metadata
        if context.fastmcp_context:
            try:
                tool = await context.fastmcp_context.fastmcp.get_tool(context.message.name)
                

------------------------------------------------------------

## Check if this tool has a "private" tag
类型: docs, 行数: 4

                # Check if this tool has a "private" tag
                if "private" in tool.tags:
                    raise ToolError("Access denied: private tool")
                    

------------------------------------------------------------

## Check if tool is enabled
类型: docs, 行数: 5

                # Check if tool is enabled
                if not tool.enabled:
                    raise ToolError("Tool is currently disabled")
                    
            except Exception:

------------------------------------------------------------

## Tool not found or other error - let execution continue
类型: docs, 行数: 1

                # Tool not found or other error - let execution continue

------------------------------------------------------------

## and handle the error naturally
类型: docs, 行数: 34

                # and handle the error naturally
                pass
        
        return await call_next(context)
```

The same pattern works for resources and prompts:

```python
from fastmcp.server.middleware import Middleware, MiddlewareContext
from fastmcp.exceptions import ResourceError, PromptError

class ComponentAccessMiddleware(Middleware):
    async def on_read_resource(self, context: MiddlewareContext, call_next):
        if context.fastmcp_context:
            try:
                resource = await context.fastmcp_context.fastmcp.get_resource(context.message.uri)
                if "restricted" in resource.tags:
                    raise ResourceError("Access denied: restricted resource")
            except Exception:
                pass
        return await call_next(context)
    
    async def on_get_prompt(self, context: MiddlewareContext, call_next):
        if context.fastmcp_context:
            try:
                prompt = await context.fastmcp_context.fastmcp.get_prompt(context.message.name)
                if not prompt.enabled:
                    raise PromptError("Prompt is currently disabled")
            except Exception:
                pass
        return await call_next(context)
```


------------------------------------------------------------

## Working with Listing Results
类型: docs, 行数: 11

### Working with Listing Results

For listing operations, the middleware `call_next` function returns a list of FastMCP components prior to being converted to MCP format. You can filter or modify this list and return it to the client. For example:

```python
from fastmcp.server.middleware import Middleware, MiddlewareContext

class ListingFilterMiddleware(Middleware):
    async def on_list_tools(self, context: MiddlewareContext, call_next):
        result = await call_next(context)
        

------------------------------------------------------------

## Filter out tools with "private" tag
类型: docs, 行数: 6

        # Filter out tools with "private" tag
        filtered_tools = [
            tool for tool in result 
            if "private" not in tool.tags
        ]
        

------------------------------------------------------------

## Return modified list
类型: docs, 行数: 6

        # Return modified list
        return filtered_tools
```

This filtering happens before the components are converted to MCP format and returned to the client, so the tags (which are FastMCP-specific) are naturally stripped in the final response.


------------------------------------------------------------

## Anatomy of a Hook
类型: docs, 行数: 6

### Anatomy of a Hook

Every middleware hook follows the same pattern. Let's examine the `on_message` hook to understand the structure:

```python
async def on_message(self, context: MiddlewareContext, call_next):

------------------------------------------------------------

## 1. Pre-processing: Inspect and optionally modify the request
类型: docs, 行数: 3

    # 1. Pre-processing: Inspect and optionally modify the request
    print(f"Processing {context.method}")
    

------------------------------------------------------------

## 2. Chain continuation: Call the next middleware/handler
类型: docs, 行数: 3

    # 2. Chain continuation: Call the next middleware/handler
    result = await call_next(context)
    

------------------------------------------------------------

## 3. Post-processing: Inspect and optionally modify the response
类型: docs, 行数: 3

    # 3. Post-processing: Inspect and optionally modify the response
    print(f"Completed {context.method}")
    

------------------------------------------------------------

## 4. Return the result (potentially modified)
类型: docs, 行数: 4

    # 4. Return the result (potentially modified)
    return result
```


------------------------------------------------------------

## Hook Parameters
类型: docs, 行数: 14

### Hook Parameters

Every hook receives two parameters:

1. **`context: MiddlewareContext`** - Contains information about the current request:
   * `context.method` - The MCP method name (e.g., "tools/call")
   * `context.source` - Where the request came from ("client" or "server")
   * `context.type` - Message type ("request" or "notification")
   * `context.message` - The MCP message data
   * `context.timestamp` - When the request was received
   * `context.fastmcp_context` - FastMCP Context object (if available)

2. **`call_next`** - A function that continues the middleware chain. You **must** call this to proceed, unless you want to stop processing entirely.


------------------------------------------------------------

## Control Flow
类型: docs, 行数: 10

### Control Flow

You have complete control over the request flow:

* **Continue processing**: Call `await call_next(context)` to proceed
* **Modify the request**: Change the context before calling `call_next`
* **Modify the response**: Change the result after calling `call_next`
* **Stop the chain**: Don't call `call_next` (rarely needed)
* **Handle errors**: Wrap `call_next` in try/catch blocks


------------------------------------------------------------

## Creating Middleware
类型: docs, 行数: 20

## Creating Middleware

FastMCP middleware is implemented by subclassing the `Middleware` base class and overriding the hooks you need. You only need to implement the hooks that are relevant to your use case.

```python
from fastmcp import FastMCP
from fastmcp.server.middleware import Middleware, MiddlewareContext

class LoggingMiddleware(Middleware):
    """Middleware that logs all MCP operations."""
    
    async def on_message(self, context: MiddlewareContext, call_next):
        """Called for all MCP messages."""
        print(f"Processing {context.method} from {context.source}")
        
        result = await call_next(context)
        
        print(f"Completed {context.method}")
        return result


------------------------------------------------------------

## Add middleware to your server
类型: docs, 行数: 7

# Add middleware to your server
mcp = FastMCP("MyServer")
mcp.add_middleware(LoggingMiddleware())
```

This creates a basic logging middleware that will print information about every request that flows through your server.


------------------------------------------------------------

## Adding Middleware to Your Server
类型: docs, 行数: 2

## Adding Middleware to Your Server


------------------------------------------------------------

## Single Middleware
类型: docs, 行数: 9

### Single Middleware

Adding middleware to your server is straightforward:

```python
mcp = FastMCP("MyServer")
mcp.add_middleware(LoggingMiddleware())
```


------------------------------------------------------------

## Multiple Middleware
类型: docs, 行数: 22

### Multiple Middleware

Middleware executes in the order it's added to the server. The first middleware added runs first on the way in, and last on the way out:

```python
mcp = FastMCP("MyServer")

mcp.add_middleware(AuthenticationMiddleware("secret-token"))
mcp.add_middleware(PerformanceMiddleware())
mcp.add_middleware(LoggingMiddleware())
```

This creates the following execution flow:

1. AuthenticationMiddleware (pre-processing)
2. PerformanceMiddleware (pre-processing)
3. LoggingMiddleware (pre-processing)
4. Actual tool/resource handler
5. LoggingMiddleware (post-processing)
6. PerformanceMiddleware (post-processing)
7. AuthenticationMiddleware (post-processing)


------------------------------------------------------------

## Server Composition and Middleware
类型: docs, 行数: 11

## Server Composition and Middleware

When using [Server Composition](/servers/composition) with `mount` or `import_server`, middleware behavior follows these rules:

1. **Parent server middleware** runs for all requests, including those routed to mounted servers
2. **Mounted server middleware** only runs for requests handled by that specific server
3. **Middleware order** is preserved within each server

This allows you to create layered middleware architectures where parent servers handle cross-cutting concerns like authentication, while child servers focus on domain-specific middleware.

```python

------------------------------------------------------------

## Parent server with middleware
类型: docs, 行数: 4

# Parent server with middleware
parent = FastMCP("Parent")
parent.add_middleware(AuthenticationMiddleware("token"))


------------------------------------------------------------

## Child server with its own middleware
类型: docs, 行数: 8

# Child server with its own middleware  
child = FastMCP("Child")
child.add_middleware(LoggingMiddleware())

@child.tool
def child_tool() -> str:
    return "from child"


------------------------------------------------------------

## Mount the child server
类型: docs, 行数: 6

# Mount the child server
parent.mount(child, prefix="child")
```

When a client calls "child\_tool", the request will flow through the parent's authentication middleware first, then route to the child server where it will go through the child's logging middleware.


------------------------------------------------------------

## Built-in Middleware Examples
类型: tutorial, 行数: 4

## Built-in Middleware Examples

FastMCP includes several middleware implementations that demonstrate best practices and provide immediately useful functionality. Let's explore how each type works by building simplified versions, then see how to use the full implementations.


------------------------------------------------------------

