# FastMCP 文档 - 第 3 部分
# 主要内容: Response Actions
# 包含段落: 111 个
# 总行数: 1029

================================================================================

## Response Actions
类型: docs, 行数: 20

### Response Actions

The handler can return data directly (which implicitly accepts the elicitation) or an `ElicitResult` object for more control over the response action:

<Card icon="code" title="ElicitResult Structure">
  <ResponseField name="action" type="Literal['accept', 'decline', 'cancel']">
    How the user responded to the elicitation request
  </ResponseField>

  <ResponseField name="content" type="dataclass instance | dict | None">
    The user's input data (required for "accept", omitted for "decline"/"cancel")
  </ResponseField>
</Card>

**Action Types:**

* **`accept`**: User provided valid input - include their data in the `content` field
* **`decline`**: User chose not to provide the requested information - omit `content`
* **`cancel`**: User cancelled the entire operation - omit `content`


------------------------------------------------------------

## Basic Example
类型: tutorial, 行数: 9

## Basic Example

```python
from fastmcp import Client
from fastmcp.client.elicitation import ElicitResult

async def basic_elicitation_handler(message: str, response_type: type, params, context):
    print(f"Server asks: {message}")
    

------------------------------------------------------------

## Simple text input for demonstration
类型: docs, 行数: 4

    # Simple text input for demonstration
    user_response = input("Your response: ")
    
    if not user_response:

------------------------------------------------------------

## For non-acceptance, use ElicitResult explicitly
类型: docs, 行数: 3

        # For non-acceptance, use ElicitResult explicitly
        return ElicitResult(action="decline")
    

------------------------------------------------------------

## Use the response_type dataclass to create a properly structured response
类型: api, 行数: 1

    # Use the response_type dataclass to create a properly structured response

------------------------------------------------------------

## FastMCP handles the conversion from JSON schema to Python type
类型: changelog, 行数: 1

    # FastMCP handles the conversion from JSON schema to Python type

------------------------------------------------------------

## Return data directly - FastMCP will implicitly accept the elicitation
类型: docs, 行数: 10

    # Return data directly - FastMCP will implicitly accept the elicitation
    return response_type(value=user_response)

client = Client(
    "my_mcp_server.py", 
    elicitation_handler=basic_elicitation_handler
)
```



------------------------------------------------------------

## Server Logging
类型: docs, 行数: 18

# Server Logging
Source: https://gofastmcp.com/clients/logging

Receive and handle log messages from MCP servers.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

<VersionBadge version="2.0.0" />

MCP servers can emit log messages to clients. The client can handle these logs through a log handler callback.


------------------------------------------------------------

## Log Handler
类型: docs, 行数: 20

## Log Handler

Provide a `log_handler` function when creating the client:

```python
from fastmcp import Client
from fastmcp.client.logging import LogMessage

async def log_handler(message: LogMessage):
    level = message.level.upper()
    logger = message.logger or 'server'
    data = message.data
    print(f"[{level}] {logger}: {data}")

client = Client(
    "my_mcp_server.py",
    log_handler=log_handler,
)
```


------------------------------------------------------------

## Handler Parameters
类型: docs, 行数: 32

### Handler Parameters

The `log_handler` is called every time a log message is received. It receives a `LogMessage` object:

<Card icon="code" title="Log Handler Parameters">
  <ResponseField name="LogMessage" type="Log Message Object">
    <Expandable title="attributes">
      <ResponseField name="level" type="Literal[&#x22;debug&#x22;, &#x22;info&#x22;, &#x22;notice&#x22;, &#x22;warning&#x22;, &#x22;error&#x22;, &#x22;critical&#x22;, &#x22;alert&#x22;, &#x22;emergency&#x22;]">
        The log level
      </ResponseField>

      <ResponseField name="logger" type="str | None">
        The logger name (optional, may be None)
      </ResponseField>

      <ResponseField name="data" type="Any">
        The actual log message content
      </ResponseField>
    </Expandable>
  </ResponseField>
</Card>

```python
async def detailed_log_handler(message: LogMessage):
    if message.level == "error":
        print(f"ERROR: {message.data}")
    elif message.level == "warning":
        print(f"WARNING: {message.data}")
    else:
        print(f"{message.level.upper()}: {message.data}")
```


------------------------------------------------------------

## Default Log Handling
类型: docs, 行数: 8

## Default Log Handling

If you don't provide a custom `log_handler`, FastMCP uses a default handler that emits a DEBUG-level FastMCP log for every log message received from the server, which is useful for visibility without polluting your own logs.

```python
client = Client("my_mcp_server.py")

async with client:

------------------------------------------------------------

## Server logs will be emitted at DEBUG level automatically
类型: docs, 行数: 5

    # Server logs will be emitted at DEBUG level automatically
    await client.call_tool("some_tool")
```



------------------------------------------------------------

## Message Handling
类型: docs, 行数: 18

# Message Handling
Source: https://gofastmcp.com/clients/messages

Handle MCP messages, requests, and notifications with custom message handlers.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

<VersionBadge version="2.9.1" />

MCP clients can receive various types of messages from servers, including requests that need responses and notifications that don't. The message handler provides a unified way to process all these messages.


------------------------------------------------------------

## Function-Based Handler
类型: api, 行数: 13

## Function-Based Handler

The simplest way to handle messages is with a function that receives all messages:

```python
from fastmcp import Client

async def message_handler(message):
    """Handle all MCP messages from the server."""
    if hasattr(message, 'root'):
        method = message.root.method
        print(f"Received: {method}")
        

------------------------------------------------------------

## Handle specific notifications
类型: docs, 行数: 12

        # Handle specific notifications
        if method == "notifications/tools/list_changed":
            print("Tools have changed - might want to refresh tool cache")
        elif method == "notifications/resources/list_changed":
            print("Resources have changed")

client = Client(
    "my_mcp_server.py",
    message_handler=message_handler,
)
```


------------------------------------------------------------

## Message Handler Class
类型: api, 行数: 22

## Message Handler Class

For fine-grained targeting, FastMCP provides a `MessageHandler` class you can subclass to take advantage of specific hooks:

```python
from fastmcp import Client
from fastmcp.client.messages import MessageHandler
import mcp.types

class MyMessageHandler(MessageHandler):
    async def on_tool_list_changed(
        self, notification: mcp.types.ToolListChangedNotification
    ) -> None:
        """Handle tool list changes specifically."""
        print("Tool list changed - refreshing available tools")

client = Client(
    "my_mcp_server.py",
    message_handler=MyMessageHandler(),
)
```


------------------------------------------------------------

## Available Handler Methods
类型: api, 行数: 38

### Available Handler Methods

All handler methods receive a single argument - the specific message type:

<Card icon="code" title="Message Handler Methods">
  <ResponseField name="on_message(message)" type="Any MCP message">
    Called for ALL messages (requests and notifications)
  </ResponseField>

  <ResponseField name="on_request(request)" type="mcp.types.ClientRequest">
    Called for requests that expect responses
  </ResponseField>

  <ResponseField name="on_notification(notification)" type="mcp.types.ServerNotification">
    Called for notifications (fire-and-forget)
  </ResponseField>

  <ResponseField name="on_tool_list_changed(notification)" type="mcp.types.ToolListChangedNotification">
    Called when the server's tool list changes
  </ResponseField>

  <ResponseField name="on_resource_list_changed(notification)" type="mcp.types.ResourceListChangedNotification">
    Called when the server's resource list changes
  </ResponseField>

  <ResponseField name="on_prompt_list_changed(notification)" type="mcp.types.PromptListChangedNotification">
    Called when the server's prompt list changes
  </ResponseField>

  <ResponseField name="on_progress(notification)" type="mcp.types.ProgressNotification">
    Called for progress updates during long-running operations
  </ResponseField>

  <ResponseField name="on_logging_message(notification)" type="mcp.types.LoggingMessageNotification">
    Called for log messages from the server
  </ResponseField>
</Card>


------------------------------------------------------------

## Example: Handling Tool Changes
类型: tutorial, 行数: 22

## Example: Handling Tool Changes

Here's a practical example of handling tool list changes:

```python
from fastmcp.client.messages import MessageHandler
import mcp.types

class ToolCacheHandler(MessageHandler):
    def __init__(self):
        self.cached_tools = []
    
    async def on_tool_list_changed(
        self, notification: mcp.types.ToolListChangedNotification
    ) -> None:
        """Clear tool cache when tools change."""
        print("Tools changed - clearing cache")
        self.cached_tools = []  # Force refresh on next access

client = Client("server.py", message_handler=ToolCacheHandler())
```


------------------------------------------------------------

## Handling Requests
类型: docs, 行数: 11

## Handling Requests

While the message handler receives server-initiated requests, for most use cases you should use the dedicated callback parameters instead:

* **Sampling requests**: Use [`sampling_handler`](/clients/sampling)
* **Progress requests**: Use [`progress_handler`](/clients/progress)
* **Log requests**: Use [`log_handler`](/clients/logging)

The message handler is primarily for monitoring and handling notifications rather than responding to requests.



------------------------------------------------------------

## Progress Monitoring
类型: docs, 行数: 18

# Progress Monitoring
Source: https://gofastmcp.com/clients/progress

Handle progress notifications from long-running server operations.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

<VersionBadge version="2.3.5" />

MCP servers can report progress during long-running operations. The client can receive these updates through a progress handler.


------------------------------------------------------------

## Progress Handler
类型: docs, 行数: 24

## Progress Handler

Set a progress handler when creating the client:

```python
from fastmcp import Client

async def my_progress_handler(
    progress: float, 
    total: float | None, 
    message: str | None
) -> None:
    if total is not None:
        percentage = (progress / total) * 100
        print(f"Progress: {percentage:.1f}% - {message or ''}")
    else:
        print(f"Progress: {progress} - {message or ''}")

client = Client(
    "my_mcp_server.py",
    progress_handler=my_progress_handler
)
```


------------------------------------------------------------

## Handler Parameters
类型: docs, 行数: 18

### Handler Parameters

The progress handler receives three parameters:

<Card icon="code" title="Progress Handler Parameters">
  <ResponseField name="progress" type="float">
    Current progress value
  </ResponseField>

  <ResponseField name="total" type="float | None">
    Expected total value (may be None)
  </ResponseField>

  <ResponseField name="message" type="str | None">
    Optional status message (may be None)
  </ResponseField>
</Card>


------------------------------------------------------------

## Per-Call Progress Handler
类型: docs, 行数: 6

## Per-Call Progress Handler

Override the progress handler for specific tool calls:

```python
async with client:

------------------------------------------------------------

## Override with specific progress handler for this call
类型: docs, 行数: 9

    # Override with specific progress handler for this call
    result = await client.call_tool(
        "long_running_task", 
        {"param": "value"}, 
        progress_handler=my_progress_handler
    )
```



------------------------------------------------------------

## Prompts
类型: docs, 行数: 18

# Prompts
Source: https://gofastmcp.com/clients/prompts

Use server-side prompt templates with automatic argument serialization.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

<VersionBadge version="2.0.0" />

Prompts are reusable message templates exposed by MCP servers. They can accept arguments to generate personalized message sequences for LLM interactions.


------------------------------------------------------------

## Listing Prompts
类型: docs, 行数: 7

## Listing Prompts

Use `list_prompts()` to retrieve all available prompt templates:

```python
async with client:
    prompts = await client.list_prompts()

------------------------------------------------------------

## prompts -> list[mcp.types.Prompt]
类型: docs, 行数: 9

    # prompts -> list[mcp.types.Prompt]
    
    for prompt in prompts:
        print(f"Prompt: {prompt.name}")
        print(f"Description: {prompt.description}")
        if prompt.arguments:
            print(f"Arguments: {[arg.name for arg in prompt.arguments]}")
```


------------------------------------------------------------

## Using Prompts
类型: docs, 行数: 2

## Using Prompts


------------------------------------------------------------

## Basic Usage
类型: docs, 行数: 6

### Basic Usage

Request a rendered prompt using `get_prompt()` with the prompt name and arguments:

```python
async with client:

------------------------------------------------------------

## Simple prompt without arguments
类型: docs, 行数: 2

    # Simple prompt without arguments
    result = await client.get_prompt("welcome_message")

------------------------------------------------------------

## result -> mcp.types.GetPromptResult
类型: docs, 行数: 2

    # result -> mcp.types.GetPromptResult
    

------------------------------------------------------------

## Access the generated messages
类型: docs, 行数: 6

    # Access the generated messages
    for message in result.messages:
        print(f"Role: {message.role}")
        print(f"Content: {message.content}")
```


------------------------------------------------------------

## Prompts with Arguments
类型: docs, 行数: 6

### Prompts with Arguments

Pass arguments as a dictionary to customize the prompt:

```python
async with client:

------------------------------------------------------------

## Prompt with simple arguments
类型: docs, 行数: 6

    # Prompt with simple arguments
    result = await client.get_prompt("user_greeting", {
        "name": "Alice",
        "role": "administrator"
    })
    

------------------------------------------------------------

## Access the personalized messages
类型: docs, 行数: 5

    # Access the personalized messages
    for message in result.messages:
        print(f"Generated message: {message.content}")
```


------------------------------------------------------------

## Automatic Argument Serialization
类型: docs, 行数: 15

## Automatic Argument Serialization

<VersionBadge version="2.9.0" />

FastMCP automatically serializes complex arguments to JSON strings as required by the MCP specification. This allows you to pass typed objects directly:

```python
from dataclasses import dataclass

@dataclass
class UserData:
    name: str
    age: int

async with client:

------------------------------------------------------------

## Complex arguments are automatically serialized
类型: docs, 行数: 11

    # Complex arguments are automatically serialized
    result = await client.get_prompt("analyze_user", {
        "user": UserData(name="Alice", age=30),     # Automatically serialized to JSON
        "preferences": {"theme": "dark"},           # Dict serialized to JSON string
        "scores": [85, 92, 78],                     # List serialized to JSON string
        "simple_name": "Bob"                        # Strings passed through unchanged
    })
```

The client handles serialization using `pydantic_core.to_json()` for consistent formatting. FastMCP servers can automatically deserialize these JSON strings back to the expected types.


------------------------------------------------------------

## Serialization Examples
类型: tutorial, 行数: 5

### Serialization Examples

```python
async with client:
    result = await client.get_prompt("data_analysis", {

------------------------------------------------------------

## These will be automatically serialized to JSON strings:
类型: docs, 行数: 10

        # These will be automatically serialized to JSON strings:
        "config": {
            "format": "csv",
            "include_headers": True,
            "delimiter": ","
        },
        "filters": [
            {"field": "age", "operator": ">", "value": 18},
            {"field": "status", "operator": "==", "value": "active"}
        ],

------------------------------------------------------------

## This remains a string:
类型: docs, 行数: 5

        # This remains a string:
        "report_title": "Monthly Analytics Report"
    })
```


------------------------------------------------------------

## Working with Prompt Results
类型: docs, 行数: 8

## Working with Prompt Results

The `get_prompt()` method returns a `GetPromptResult` object containing a list of messages:

```python
async with client:
    result = await client.get_prompt("conversation_starter", {"topic": "climate"})
    

------------------------------------------------------------

## Access individual messages
类型: docs, 行数: 7

    # Access individual messages
    for i, message in enumerate(result.messages):
        print(f"Message {i + 1}:")
        print(f"  Role: {message.role}")
        print(f"  Content: {message.content.text if hasattr(message.content, 'text') else message.content}")
```


------------------------------------------------------------

## Raw MCP Protocol Access
类型: docs, 行数: 6

## Raw MCP Protocol Access

For access to the complete MCP protocol objects, use the `*_mcp` methods:

```python
async with client:

------------------------------------------------------------

## Raw MCP method returns full protocol object
类型: api, 行数: 2

    # Raw MCP method returns full protocol object
    prompts_result = await client.list_prompts_mcp()

------------------------------------------------------------

## prompts_result -> mcp.types.ListPromptsResult
类型: docs, 行数: 3

    # prompts_result -> mcp.types.ListPromptsResult
    
    prompt_result = await client.get_prompt_mcp("example_prompt", {"arg": "value"})

------------------------------------------------------------

## prompt_result -> mcp.types.GetPromptResult
类型: docs, 行数: 3

    # prompt_result -> mcp.types.GetPromptResult
```


------------------------------------------------------------

## Multi-Server Clients
类型: docs, 行数: 6

## Multi-Server Clients

When using multi-server clients, prompts are accessible without prefixing (unlike tools):

```python
async with client:  # Multi-server client

------------------------------------------------------------

## Prompts from any server are directly accessible
类型: docs, 行数: 5

    # Prompts from any server are directly accessible
    result1 = await client.get_prompt("weather_prompt", {"city": "London"})
    result2 = await client.get_prompt("assistant_prompt", {"query": "help"})
```


------------------------------------------------------------

## Common Prompt Patterns
类型: docs, 行数: 2

## Common Prompt Patterns


------------------------------------------------------------

## System Messages
类型: docs, 行数: 11

### System Messages

Many prompts generate system messages for LLM configuration:

```python
async with client:
    result = await client.get_prompt("system_configuration", {
        "role": "helpful assistant",
        "expertise": "python programming"
    })
    

------------------------------------------------------------

## Typically returns messages with role="system"
类型: docs, 行数: 5

    # Typically returns messages with role="system"
    system_message = result.messages[0]
    print(f"System prompt: {system_message.content}")
```


------------------------------------------------------------

## Conversation Templates
类型: docs, 行数: 11

### Conversation Templates

Prompts can generate multi-turn conversation templates:

```python
async with client:
    result = await client.get_prompt("interview_template", {
        "candidate_name": "Alice",
        "position": "Senior Developer"
    })
    

------------------------------------------------------------

## Multiple messages for a conversation flow
类型: docs, 行数: 10

    # Multiple messages for a conversation flow
    for message in result.messages:
        print(f"{message.role}: {message.content}")
```

<Tip>
  Prompt arguments and their expected types depend on the specific prompt implementation. Check the server's documentation or use `list_prompts()` to see available arguments for each prompt.
</Tip>



------------------------------------------------------------

## Resource Operations
类型: docs, 行数: 18

# Resource Operations
Source: https://gofastmcp.com/clients/resources

Access static and templated resources from MCP servers.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

<VersionBadge version="2.0.0" />

Resources are data sources exposed by MCP servers. They can be static files or dynamic templates that generate content based on parameters.


------------------------------------------------------------

## Types of Resources
类型: docs, 行数: 7

## Types of Resources

MCP servers expose two types of resources:

* **Static Resources**: Fixed content accessible via URI (e.g., configuration files, documentation)
* **Resource Templates**: Dynamic resources that accept parameters to generate content (e.g., API endpoints, database queries)


------------------------------------------------------------

## Listing Resources
类型: docs, 行数: 2

## Listing Resources


------------------------------------------------------------

## Static Resources
类型: docs, 行数: 7

### Static Resources

Use `list_resources()` to retrieve all static resources available on the server:

```python
async with client:
    resources = await client.list_resources()

------------------------------------------------------------

## resources -> list[mcp.types.Resource]
类型: docs, 行数: 9

    # resources -> list[mcp.types.Resource]
    
    for resource in resources:
        print(f"Resource URI: {resource.uri}")
        print(f"Name: {resource.name}")
        print(f"Description: {resource.description}")
        print(f"MIME Type: {resource.mimeType}")
```


------------------------------------------------------------

## Resource Templates
类型: docs, 行数: 7

### Resource Templates

Use `list_resource_templates()` to retrieve available resource templates:

```python
async with client:
    templates = await client.list_resource_templates()

------------------------------------------------------------

## templates -> list[mcp.types.ResourceTemplate]
类型: docs, 行数: 8

    # templates -> list[mcp.types.ResourceTemplate]
    
    for template in templates:
        print(f"Template URI: {template.uriTemplate}")
        print(f"Name: {template.name}")
        print(f"Description: {template.description}")
```


------------------------------------------------------------

## Reading Resources
类型: docs, 行数: 2

## Reading Resources


------------------------------------------------------------

## Static Resources
类型: docs, 行数: 6

### Static Resources

Read a static resource using its URI:

```python
async with client:

------------------------------------------------------------

## Read a static resource
类型: docs, 行数: 2

    # Read a static resource
    content = await client.read_resource("file:///path/to/README.md")

------------------------------------------------------------

## content -> list[mcp.types.TextResourceContents | mcp.types.BlobResourceContents]
类型: docs, 行数: 2

    # content -> list[mcp.types.TextResourceContents | mcp.types.BlobResourceContents]
    

------------------------------------------------------------

## Access text content
类型: docs, 行数: 4

    # Access text content
    if hasattr(content[0], 'text'):
        print(content[0].text)
    

------------------------------------------------------------

## Access binary content
类型: docs, 行数: 5

    # Access binary content
    if hasattr(content[0], 'blob'):
        print(f"Binary data: {len(content[0].blob)} bytes")
```


------------------------------------------------------------

## Resource Templates
类型: docs, 行数: 6

### Resource Templates

Read from a resource template by providing the URI with parameters:

```python
async with client:

------------------------------------------------------------

## Read a resource generated from a template
类型: docs, 行数: 1

    # Read a resource generated from a template

------------------------------------------------------------

## For example, a template like "weather://{{city}}/current"
类型: tutorial, 行数: 3

    # For example, a template like "weather://{{city}}/current"
    weather_content = await client.read_resource("weather://london/current")
    

------------------------------------------------------------

## Access the generated content
类型: docs, 行数: 4

    # Access the generated content
    print(weather_content[0].text)  # Assuming text JSON response
```


------------------------------------------------------------

## Content Types
类型: docs, 行数: 4

## Content Types

Resources can return different content types:


------------------------------------------------------------

## Text Resources
类型: docs, 行数: 12

### Text Resources

```python
async with client:
    content = await client.read_resource("resource://config/settings.json")
    
    for item in content:
        if hasattr(item, 'text'):
            print(f"Text content: {item.text}")
            print(f"MIME type: {item.mimeType}")
```


------------------------------------------------------------

## Binary Resources
类型: docs, 行数: 11

### Binary Resources

```python
async with client:
    content = await client.read_resource("resource://images/logo.png")
    
    for item in content:
        if hasattr(item, 'blob'):
            print(f"Binary content: {len(item.blob)} bytes")
            print(f"MIME type: {item.mimeType}")
            

------------------------------------------------------------

## Save to file
类型: docs, 行数: 5

            # Save to file
            with open("downloaded_logo.png", "wb") as f:
                f.write(item.blob)
```


------------------------------------------------------------

## Working with Multi-Server Clients
类型: docs, 行数: 6

## Working with Multi-Server Clients

When using multi-server clients, resource URIs are automatically prefixed with the server name:

```python
async with client:  # Multi-server client

------------------------------------------------------------

## Access resources from different servers
类型: docs, 行数: 8

    # Access resources from different servers
    weather_icons = await client.read_resource("weather://weather/icons/sunny")
    templates = await client.read_resource("resource://assistant/templates/list")
    
    print(f"Weather icon: {weather_icons[0].blob}")
    print(f"Templates: {templates[0].text}")
```


------------------------------------------------------------

## Raw MCP Protocol Access
类型: docs, 行数: 6

## Raw MCP Protocol Access

For access to the complete MCP protocol objects, use the `*_mcp` methods:

```python
async with client:

------------------------------------------------------------

## Raw MCP methods return full protocol objects
类型: api, 行数: 2

    # Raw MCP methods return full protocol objects
    resources_result = await client.list_resources_mcp()

------------------------------------------------------------

## resources_result -> mcp.types.ListResourcesResult
类型: docs, 行数: 3

    # resources_result -> mcp.types.ListResourcesResult
    
    templates_result = await client.list_resource_templates_mcp()

------------------------------------------------------------

## templates_result -> mcp.types.ListResourceTemplatesResult
类型: docs, 行数: 3

    # templates_result -> mcp.types.ListResourceTemplatesResult
    
    content_result = await client.read_resource_mcp("resource://example")

------------------------------------------------------------

## content_result -> mcp.types.ReadResourceResult
类型: docs, 行数: 3

    # content_result -> mcp.types.ReadResourceResult
```


------------------------------------------------------------

## Common Resource URI Patterns
类型: docs, 行数: 5

## Common Resource URI Patterns

Different MCP servers may use various URI schemes:

```python

------------------------------------------------------------

## File system resources
类型: docs, 行数: 3

# File system resources
"file:///path/to/file.txt"


------------------------------------------------------------

## Custom protocol resources
类型: docs, 行数: 4

# Custom protocol resources  
"weather://london/current"
"database://users/123"


------------------------------------------------------------

## Generic resource protocol
类型: docs, 行数: 10

# Generic resource protocol
"resource://config/settings"
"resource://templates/email"
```

<Tip>
  Resource URIs and their formats depend on the specific MCP server implementation. Check the server's documentation for available resources and their URI patterns.
</Tip>



------------------------------------------------------------

## Client Roots
类型: docs, 行数: 18

# Client Roots
Source: https://gofastmcp.com/clients/roots

Provide local context and resource boundaries to MCP servers.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

<VersionBadge version="2.0.0" />

Roots are a way for clients to inform servers about the resources they have access to. Servers can use this information to adjust behavior or provide more relevant responses.


------------------------------------------------------------

## Setting Static Roots
类型: docs, 行数: 30

## Setting Static Roots

Provide a list of roots when creating the client:

<CodeGroup>
  ```python Static Roots
  from fastmcp import Client

  client = Client(
      "my_mcp_server.py", 
      roots=["/path/to/root1", "/path/to/root2"]
  )
  ```

  ```python Dynamic Roots Callback
  from fastmcp import Client
  from fastmcp.client.roots import RequestContext

  async def roots_callback(context: RequestContext) -> list[str]:
      print(f"Server requested roots (Request ID: {context.request_id})")
      return ["/path/to/root1", "/path/to/root2"]

  client = Client(
      "my_mcp_server.py", 
      roots=roots_callback
  )
  ```
</CodeGroup>



------------------------------------------------------------

## LLM Sampling
类型: docs, 行数: 18

# LLM Sampling
Source: https://gofastmcp.com/clients/sampling

Handle server-initiated LLM sampling requests.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

<VersionBadge version="2.0.0" />

MCP servers can request LLM completions from clients. The client handles these requests through a sampling handler callback.


------------------------------------------------------------

## Sampling Handler
类型: docs, 行数: 17

## Sampling Handler

Provide a `sampling_handler` function when creating the client:

```python
from fastmcp import Client
from fastmcp.client.sampling import (
    SamplingMessage,
    SamplingParams,
    RequestContext,
)

async def sampling_handler(
    messages: list[SamplingMessage],
    params: SamplingParams,
    context: RequestContext
) -> str:

------------------------------------------------------------

## Your LLM integration logic here
类型: docs, 行数: 1

    # Your LLM integration logic here

------------------------------------------------------------

## Extract text from messages and generate a response
类型: docs, 行数: 9

    # Extract text from messages and generate a response
    return "Generated response based on the messages"

client = Client(
    "my_mcp_server.py",
    sampling_handler=sampling_handler,
)
```


------------------------------------------------------------

## Handler Parameters
类型: docs, 行数: 84

### Handler Parameters

The sampling handler receives three parameters:

<Card icon="code" title="Sampling Handler Parameters">
  <ResponseField name="SamplingMessage" type="Sampling Message Object">
    <Expandable title="attributes">
      <ResponseField name="role" type="Literal[&#x22;user&#x22;, &#x22;assistant&#x22;]">
        The role of the message.
      </ResponseField>

      <ResponseField name="content" type="TextContent | ImageContent | AudioContent">
        The content of the message.

        TextContent is most common, and has a `.text` attribute.
      </ResponseField>
    </Expandable>
  </ResponseField>

  <ResponseField name="SamplingParams" type="Sampling Parameters Object">
    <Expandable title="attributes">
      <ResponseField name="messages" type="list[SamplingMessage]">
        The messages to sample from
      </ResponseField>

      <ResponseField name="modelPreferences" type="ModelPreferences | None">
        The server's preferences for which model to select. The client MAY ignore
        these preferences.

        <Expandable title="attributes">
          <ResponseField name="hints" type="list[ModelHint] | None">
            The hints to use for model selection.
          </ResponseField>

          <ResponseField name="costPriority" type="float | None">
            The cost priority for model selection.
          </ResponseField>

          <ResponseField name="speedPriority" type="float | None">
            The speed priority for model selection.
          </ResponseField>

          <ResponseField name="intelligencePriority" type="float | None">
            The intelligence priority for model selection.
          </ResponseField>
        </Expandable>
      </ResponseField>

      <ResponseField name="systemPrompt" type="str | None">
        An optional system prompt the server wants to use for sampling.
      </ResponseField>

      <ResponseField name="includeContext" type="IncludeContext | None">
        A request to include context from one or more MCP servers (including the caller), to
        be attached to the prompt.
      </ResponseField>

      <ResponseField name="temperature" type="float | None">
        The sampling temperature.
      </ResponseField>

      <ResponseField name="maxTokens" type="int">
        The maximum number of tokens to sample.
      </ResponseField>

      <ResponseField name="stopSequences" type="list[str] | None">
        The stop sequences to use for sampling.
      </ResponseField>

      <ResponseField name="metadata" type="dict[str, Any] | None">
        Optional metadata to pass through to the LLM provider.
      </ResponseField>
    </Expandable>
  </ResponseField>

  <ResponseField name="RequestContext" type="Request Context Object">
    <Expandable title="attributes">
      <ResponseField name="request_id" type="RequestId">
        Unique identifier for the MCP request
      </ResponseField>
    </Expandable>
  </ResponseField>
</Card>


------------------------------------------------------------

## Basic Example
类型: tutorial, 行数: 11

## Basic Example

```python
from fastmcp import Client
from fastmcp.client.sampling import SamplingMessage, SamplingParams, RequestContext

async def basic_sampling_handler(
    messages: list[SamplingMessage],
    params: SamplingParams,
    context: RequestContext
) -> str:

------------------------------------------------------------

## Extract message content
类型: docs, 行数: 6

    # Extract message content
    conversation = []
    for message in messages:
        content = message.content.text if hasattr(message.content, 'text') else str(message.content)
        conversation.append(f"{message.role}: {content}")


------------------------------------------------------------

## Use the system prompt if provided
类型: docs, 行数: 3

    # Use the system prompt if provided
    system_prompt = params.systemPrompt or "You are a helpful assistant."


------------------------------------------------------------

## Here you would integrate with your preferred LLM service
类型: docs, 行数: 1

    # Here you would integrate with your preferred LLM service

------------------------------------------------------------

## This is just a placeholder response
类型: docs, 行数: 10

    # This is just a placeholder response
    return f"Response based on conversation: {' | '.join(conversation)}"

client = Client(
    "my_mcp_server.py",
    sampling_handler=basic_sampling_handler
)
```



------------------------------------------------------------

## Tool Operations
类型: docs, 行数: 18

# Tool Operations
Source: https://gofastmcp.com/clients/tools

Discover and execute server-side tools with the FastMCP client.

export const VersionBadge = ({version}) => {
  return <code className="version-badge-container">
            <p className="version-badge">
                <span className="version-badge-label">New in version:</span> 
                <code className="version-badge-version">{version}</code>
            </p>
        </code>;
};

<VersionBadge version="2.0.0" />

Tools are executable functions exposed by MCP servers. The FastMCP client provides methods to discover available tools and execute them with arguments.


------------------------------------------------------------

## Discovering Tools
类型: docs, 行数: 7

## Discovering Tools

Use `list_tools()` to retrieve all tools available on the server:

```python
async with client:
    tools = await client.list_tools()

------------------------------------------------------------

## tools -> list[mcp.types.Tool]
类型: docs, 行数: 9

    # tools -> list[mcp.types.Tool]
    
    for tool in tools:
        print(f"Tool: {tool.name}")
        print(f"Description: {tool.description}")
        if tool.inputSchema:
            print(f"Parameters: {tool.inputSchema}")
```


------------------------------------------------------------

## Executing Tools
类型: docs, 行数: 2

## Executing Tools


------------------------------------------------------------

## Basic Execution
类型: docs, 行数: 6

### Basic Execution

Execute a tool using `call_tool()` with the tool name and arguments:

```python
async with client:

------------------------------------------------------------

## Simple tool call
类型: docs, 行数: 2

    # Simple tool call
    result = await client.call_tool("add", {"a": 5, "b": 3})

------------------------------------------------------------

## result -> CallToolResult with structured and unstructured data
类型: docs, 行数: 2

    # result -> CallToolResult with structured and unstructured data
    

------------------------------------------------------------

## Access structured data (automatically deserialized)
类型: docs, 行数: 3

    # Access structured data (automatically deserialized)
    print(result.data)  # 8 (int) or {"result": 8} for primitive types
    

------------------------------------------------------------

## Access traditional content blocks
类型: docs, 行数: 4

    # Access traditional content blocks  
    print(result.content[0].text)  # "8" (TextContent)
```


------------------------------------------------------------

## Advanced Execution Options
类型: docs, 行数: 6

### Advanced Execution Options

The `call_tool()` method supports additional parameters for timeout control and progress monitoring:

```python
async with client:

------------------------------------------------------------

## With timeout (aborts if execution takes longer than 2 seconds)
类型: docs, 行数: 7

    # With timeout (aborts if execution takes longer than 2 seconds)
    result = await client.call_tool(
        "long_running_task", 
        {"param": "value"}, 
        timeout=2.0
    )
    

------------------------------------------------------------

## With progress handler (to track execution progress)
类型: docs, 行数: 15

    # With progress handler (to track execution progress)
    result = await client.call_tool(
        "long_running_task",
        {"param": "value"},
        progress_handler=my_progress_handler
    )
```

**Parameters:**

* `name`: The tool name (string)
* `arguments`: Dictionary of arguments to pass to the tool (optional)
* `timeout`: Maximum execution time in seconds (optional, overrides client-level timeout)
* `progress_handler`: Progress callback function (optional, overrides client-level handler)


------------------------------------------------------------

## Handling Results
类型: docs, 行数: 6

## Handling Results

<VersionBadge version="2.10.0" />

Tool execution returns a `CallToolResult` object with both structured and traditional content. FastMCP's standout feature is the `.data` property, which doesn't just provide raw JSON but actually hydrates complete Python objects including complex types like datetimes, UUIDs, and custom classes.


------------------------------------------------------------

## CallToolResult Properties
类型: docs, 行数: 20

### CallToolResult Properties

<Card icon="code" title="CallToolResult Properties">
  <ResponseField name=".data" type="Any">
    **FastMCP exclusive**: Fully hydrated Python objects with complex type support (datetimes, UUIDs, custom classes). Goes beyond JSON to provide complete object reconstruction from output schemas.
  </ResponseField>

  <ResponseField name=".content" type="list[mcp.types.ContentBlock]">
    Standard MCP content blocks (`TextContent`, `ImageContent`, `AudioContent`, etc.) available from all MCP servers.
  </ResponseField>

  <ResponseField name=".structured_content" type="dict[str, Any] | None">
    Standard MCP structured JSON data as sent by the server, available from all MCP servers that support structured outputs.
  </ResponseField>

  <ResponseField name=".is_error" type="bool">
    Boolean indicating if the tool execution failed.
  </ResponseField>
</Card>


------------------------------------------------------------

