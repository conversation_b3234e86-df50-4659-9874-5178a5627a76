# 🚀 快速开始指南

## 📋 系统要求

- Python 3.8+
- Windows/Linux/macOS
- 8GB+ RAM (推荐)
- 企业级数据库分析MCP服务器

## ⚡ 快速安装

### 1. 安装依赖
```bash
cd mcp_client
pip install -r requirements.txt
```

### 2. 配置服务器路径
编辑 `config.py` 文件，确保 `server_path` 指向正确的MCP服务器文件：

```python
# 在config.py中修改
server_path=str(PROJECT_ROOT / "enterprise_database_mcp_server.py")
```

### 3. 配置数据库连接
设置环境变量或修改 `config.py` 中的数据库配置：

```bash
# 环境变量方式
export DB_HOST=localhost
export DB_PORT=3306
export DB_USER=root
export DB_PASSWORD=123456
export DB_NAME=sensor_data
```

## 🎯 启动方式

### 方式1: 命令行界面
```bash
python run.py cli
```
- ✅ 简单直接
- ✅ 适合脚本化操作
- ✅ 低资源占用

### 方式2: 聊天界面 (推荐)
```bash
python run.py chat
```
- ✅ 自然语言交互
- ✅ 智能意图识别
- ✅ 实时数据可视化
- 🌐 访问: http://localhost:8501

### 方式3: 仪表板界面
```bash
python run.py dashboard
```
- ✅ 专业数据分析
- ✅ 多维度可视化
- ✅ 实时监控面板
- 🌐 访问: http://localhost:8502

### 方式4: 运行示例
```bash
python run.py examples
```
- ✅ 学习所有功能
- ✅ 测试连接状态
- ✅ 验证配置正确性

## 💬 使用示例

### 智能查询示例
在聊天界面中，您可以使用自然语言进行查询：

```
获取系统状态
显示最近24小时温度趋势图
检测温度异常
计算最近一周压力平均值
生成温度和湿度对比图
创建高温提醒规则
```

### 编程接口示例
```python
import asyncio
from mcp_client.client import MCPClient

async def example():
    async with MCPClient() as client:
        # 获取系统状态
        status = await client.get_system_status()
        print(status)
        
        # 统计分析
        result = await client.statistical_analysis(
            start_time="2024-01-01 00:00:00",
            end_time="2024-01-02 00:00:00", 
            columns=["temperature"],
            operation="average"
        )
        print(result)
        
        # 异常检测
        anomalies = await client.detect_anomalies(
            column="temperature",
            method="hybrid",
            time_window="24h"
        )
        print(anomalies)

asyncio.run(example())
```

## 🔧 常见问题

### Q: 连接失败怎么办？
A: 检查以下几点：
1. MCP服务器文件路径是否正确
2. 数据库连接参数是否正确
3. Python环境是否安装了所有依赖
4. 防火墙是否阻止了连接

### Q: 如何修改数据库配置？
A: 有两种方式：
1. 修改 `config.py` 中的 `env_vars`
2. 设置环境变量 (推荐)

### Q: Web界面无法访问？
A: 检查：
1. 端口是否被占用 (8501, 8502)
2. 防火墙设置
3. 浏览器是否支持

### Q: 如何添加新的数据列？
A: 修改以下文件：
1. `handlers/tool_handler.py` 中的 `column_mapping`
2. `utils/validator.py` 中的 `valid_columns`

## 📊 功能特性

### ✅ 核心功能
- [x] 完全本地化运行
- [x] 智能自然语言交互
- [x] 实时数据分析
- [x] 异常检测和预警
- [x] 数据可视化
- [x] 多种界面选择

### ✅ 数据分析
- [x] 统计分析 (平均值、最大值、最小值等)
- [x] 趋势分析和预测
- [x] 异常检测 (统计方法、孤立森林、混合方法)
- [x] 数据聚合和分组

### ✅ 可视化
- [x] 折线图、柱状图、散点图
- [x] 实时图表更新
- [x] 交互式图表
- [x] 数据导出功能

### ✅ 系统管理
- [x] 连接状态监控
- [x] 性能指标显示
- [x] 日志记录和调试
- [x] 配置管理

## 🎯 下一步

1. **熟悉界面**: 先使用聊天界面体验基本功能
2. **学习查询**: 尝试不同的自然语言查询
3. **探索分析**: 使用仪表板进行深度数据分析
4. **自定义配置**: 根据需要调整配置参数
5. **扩展功能**: 参考代码添加新的分析功能

## 📞 技术支持

- 📚 查看 `examples/` 目录中的示例代码
- 🐛 启用调试模式: `python run.py --debug cli`
- 📋 查看日志文件: `logs/mcp_client.log`
- 🔧 修改配置文件: `config.py`

---

🎉 **恭喜！您已经成功设置了企业级数据库分析MCP客户端！**

现在您可以开始使用强大的本地化数据分析功能了。
