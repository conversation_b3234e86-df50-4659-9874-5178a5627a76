#!/usr/bin/env python3
"""
企业级MySQL数据库分析MCP服务器
专为大数据量实时处理设计，支持完全本地部署

功能特性：
1. 大数据量实时处理 - 连接池、异步处理、流式计算
2. 智能异常检测 - 多种算法、自动学习、原因分析
3. 实时提醒系统 - 多种触发条件、分级预警
4. 高性能可视化 - 实时图表、交互式展示
5. 趋势分析预测 - 机器学习、时间序列分析
6. 语音交互支持 - 本地语音识别和合成
7. 完全本地部署 - 无需互联网连接
"""

import asyncio
import json
import logging
import os
import base64
import threading
import queue
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Literal, Union
from dataclasses import dataclass, field
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
import warnings
warnings.filterwarnings('ignore')

# 核心依赖
import mysql.connector
from mysql.connector import pooling
import pandas as pd
import numpy as np
from scipy import stats
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error

# 可视化 - 使用 ECharts
from echarts_utils import EChartsConfigGenerator, create_echarts_html, EChartsDataProcessor

# 语音功能
import speech_recognition as sr
import pyttsx3

# AI/LLM功能
import openai
from openai import OpenAI

# FastMCP框架
from fastmcp import FastMCP
from pydantic import BaseModel, Field

# HTTP API支持
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import uvicorn

# 加载环境变量
from dotenv import load_dotenv
load_dotenv()

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('mcp_server.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# ==========================================
# 配置和数据模型
# ==========================================

class DatabaseConfig:
    """数据库配置"""
    def __init__(self):
        # 确保环境变量已加载
        load_dotenv()

        self.host = os.getenv("DB_HOST", "localhost")
        self.port = int(os.getenv("DB_PORT", "3306"))
        self.user = os.getenv("DB_USER", "root")
        self.password = os.getenv("DB_PASSWORD", "")
        self.database = os.getenv("DB_NAME", "sensor_data")
        self.charset = os.getenv("DB_CHARSET", "utf8mb4")

        # 连接池配置
        self.pool_name = "mcp_pool"
        self.pool_size = int(os.getenv("DB_POOL_SIZE", "20"))
        self.max_overflow = int(os.getenv("DB_MAX_OVERFLOW", "30"))
        self.pool_reset_session = True
        self.autocommit = True

        # 检测Docker环境
        self.is_docker = os.path.exists('/.dockerenv') or os.getenv('DOCKER_ENV') == 'true'

        # 根据环境调整主机地址
        if self.is_docker and self.host == "localhost":
            # 在Docker中，尝试连接宿主机
            self.host = "host.docker.internal"
            logger.info("检测到Docker环境，使用 host.docker.internal")

@dataclass
class PerformanceConfig:
    """性能配置"""
    max_workers: int = int(os.getenv("MAX_WORKERS", "10"))
    batch_size: int = int(os.getenv("BATCH_SIZE", "10000"))
    cache_ttl: int = int(os.getenv("CACHE_TTL", "300"))
    query_timeout: int = int(os.getenv("QUERY_TIMEOUT", "60"))
    max_memory_usage: str = os.getenv("MAX_MEMORY_USAGE", "2GB")

@dataclass
class LLMConfig:
    """LLM配置"""
    api_key: str = os.getenv("OPENAI_API_KEY", "")
    model: str = os.getenv("OPENAI_MODEL", "gpt-4o-mini")
    use_local_model: bool = os.getenv("USE_LOCAL_MODEL", "false").lower() == "true"
    max_tokens: int = int(os.getenv("AI_MAX_TOKENS", "2000"))
    temperature: float = float(os.getenv("AI_TEMPERATURE", "0.7"))

class StatisticalResult(BaseModel):
    """统计分析结果"""
    operation: str = Field(description="统计操作类型")
    column: str = Field(description="分析的列名")
    start_time: str = Field(description="开始时间")
    end_time: str = Field(description="结束时间")
    result: float = Field(description="统计结果")
    count: int = Field(description="数据点数量")
    processing_time: float = Field(description="处理时间(秒)")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外元数据")

class AnomalyResult(BaseModel):
    """异常检测结果"""
    column: str = Field(description="检测的列名")
    method: str = Field(description="检测方法")
    anomalies: List[Dict[str, Any]] = Field(description="异常数据点")
    threshold: float = Field(description="阈值")
    total_points: int = Field(description="总数据点")
    anomaly_count: int = Field(description="异常点数量")
    anomaly_rate: float = Field(description="异常率")
    confidence_score: float = Field(description="置信度分数")
    possible_causes: List[str] = Field(description="可能的异常原因")
    processing_time: float = Field(description="处理时间(秒)")

class AlertRule(BaseModel):
    """提醒规则"""
    id: str = Field(description="规则ID")
    name: str = Field(description="规则名称")
    column: str = Field(description="监控列名")
    condition: Literal["greater_than", "less_than", "range", "change_rate", "pattern"] = Field(description="条件类型")
    threshold: float = Field(description="阈值")
    threshold2: Optional[float] = Field(default=None, description="第二阈值(范围条件)")
    severity: Literal["low", "medium", "high", "critical"] = Field(default="medium", description="严重级别")
    enabled: bool = Field(default=True, description="是否启用")
    notification_methods: List[str] = Field(default=["system"], description="通知方式")
    cooldown_minutes: int = Field(default=5, description="冷却时间(分钟)")
    last_triggered: Optional[str] = Field(default=None, description="最后触发时间")

class TrendResult(BaseModel):
    """趋势分析结果"""
    column: str = Field(description="分析列名")
    method: str = Field(description="分析方法")
    current_value: float = Field(description="当前值")
    predicted_value: float = Field(description="预测值")
    trend_direction: str = Field(description="趋势方向")
    trend_strength: float = Field(description="趋势强度")
    confidence: str = Field(description="预测置信度")
    change_rate: float = Field(description="变化率(%)")
    predictions: List[float] = Field(description="未来预测值")
    prediction_timestamps: List[str] = Field(description="预测时间点")
    model_accuracy: float = Field(description="模型准确度")
    processing_time: float = Field(description="处理时间(秒)")

# ==========================================
# 全局变量和初始化
# ==========================================

# 配置实例
db_config = DatabaseConfig()
perf_config = PerformanceConfig()
llm_config = LLMConfig()

# 数据库连接池
connection_pool = None

# 缓存系统
cache_data = {}
cache_timestamps = {}

# 提醒规则存储
alert_rules: Dict[str, AlertRule] = {}

# 语音引擎
tts_engine = None
speech_recognizer = None

# OpenAI客户端
openai_client = None

# 线程池
executor = ThreadPoolExecutor(max_workers=perf_config.max_workers)

# 创建FastAPI应用（用于Streamlit前端）
app = FastAPI(
    title="企业级数据库分析API",
    description="为Streamlit前端提供HTTP API接口",
    version="1.0.0"
)

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # 生产环境中应该限制具体域名
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 创建FastMCP服务器实例
mcp = FastMCP(
    name="企业级数据库分析助手",
    instructions="""
    我是一个专业的企业级MySQL数据库分析助手，专为大数据量实时处理设计。

    🎯 核心能力：
    • 大数据量实时统计分析 - 支持千万级数据处理
    • 智能异常检测 - 多算法融合，自动原因分析
    • 实时监控提醒 - 多级预警，多种通知方式
    • 高性能数据可视化 - 实时图表，交互式展示
    • 趋势分析预测 - 机器学习驱动的时间序列分析
    • 语音交互支持 - 本地语音识别和播报
    
    🏗️ 技术特性：
    • 连接池管理 - 高并发数据库访问
    • 异步处理 - 非阻塞式数据处理
    • 智能缓存 - 提升查询性能
    • 流式计算 - 大数据集分块处理
    • 完全本地化 - 无需互联网连接
    
    💡 适用场景：
    • 工业物联网数据分析
    • 实时监控系统
    • 质量控制分析
    • 设备健康监测
    • 生产效率优化
    """,
    dependencies=[
        "mysql-connector-python>=8.0.0",
        "pandas>=2.0.0", 
        "numpy>=1.24.0",
        "scipy>=1.10.0",
        "scikit-learn>=1.3.0",
        "matplotlib>=3.7.0",
        "plotly>=5.15.0",
        "seaborn>=0.12.0",
        "speechrecognition>=3.10.0",
        "pyttsx3>=2.90"
    ]
)

# ==========================================
# 启动中间件 - 初始化系统组件
# ==========================================

class StartupMiddleware:
    """启动中间件，用于初始化系统组件"""

    def __init__(self):
        self.initialized = False

    async def __call__(self, request, call_next):
        # 只在第一次请求时初始化
        if not self.initialized:
            logger.info("🚀 初始化系统组件...")

            # 初始化数据库连接池
            if not init_database_pool():
                logger.error("❌ 数据库连接池初始化失败")
            else:
                logger.info("✅ 数据库连接池初始化成功")

            # 初始化语音引擎
            if init_speech_engines():
                logger.info("✅ 语音引擎初始化成功")
            else:
                logger.warning("⚠️ 语音引擎初始化失败")

            # 初始化OpenAI客户端
            if init_openai_client():
                logger.info(f"✅ OpenAI客户端初始化成功 (模型: {llm_config.model})")
            else:
                logger.warning("⚠️ OpenAI客户端初始化失败，AI功能将使用本地分析")

            self.initialized = True
            logger.info("🎯 系统组件初始化完成")

        # 继续处理请求
        return await call_next(request)

# 添加启动中间件
startup_middleware = StartupMiddleware()
mcp.add_middleware(startup_middleware)

# ==========================================
# 核心工具函数
# ==========================================

def init_database_pool():
    """初始化数据库连接池"""
    global connection_pool
    try:
        # 调试信息：显示数据库配置
        logger.info(f"数据库配置: host={db_config.host}, port={db_config.port}, user={db_config.user}, database={db_config.database}")
        logger.info(f"密码长度: {len(db_config.password) if db_config.password else 0}")

        connection_pool = pooling.MySQLConnectionPool(
            pool_name=db_config.pool_name,
            pool_size=db_config.pool_size,
            pool_reset_session=db_config.pool_reset_session,
            host=db_config.host,
            port=db_config.port,
            user=db_config.user,
            password=db_config.password,
            database=db_config.database,
            charset=db_config.charset,
            autocommit=db_config.autocommit,
            connect_timeout=30,
            sql_mode='TRADITIONAL'
        )
        logger.info(f"数据库连接池初始化成功: {db_config.pool_size} 连接")
        return True
    except Exception as e:
        logger.error(f"数据库连接池初始化失败: {str(e)}")
        logger.error(f"尝试连接: {db_config.user}@{db_config.host}:{db_config.port}/{db_config.database}")
        return False

async def get_db_connection():
    """从连接池获取数据库连接"""
    try:
        if connection_pool is None:
            if not init_database_pool():
                raise Exception("连接池初始化失败")
        
        connection = connection_pool.get_connection()
        return connection
    except Exception as e:
        logger.error(f"获取数据库连接失败: {str(e)}")
        raise

async def execute_query_with_cache(query: str, params: tuple = None, cache_key: str = None) -> pd.DataFrame:
    """执行SQL查询，支持缓存"""
    start_time = datetime.now()
    
    # 检查缓存
    if cache_key and cache_key in cache_data:
        cache_time = cache_timestamps.get(cache_key, datetime.min)
        if (datetime.now() - cache_time).seconds < perf_config.cache_ttl:
            logger.debug(f"使用缓存数据: {cache_key}")
            return cache_data[cache_key]
    
    # 执行查询
    connection = await get_db_connection()
    try:
        df = pd.read_sql(query, connection, params=params)
        
        # 更新缓存
        if cache_key:
            cache_data[cache_key] = df
            cache_timestamps[cache_key] = datetime.now()
        
        processing_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"查询完成: {len(df)} 行, 耗时 {processing_time:.2f}s")
        
        return df
    finally:
        connection.close()

def init_speech_engines():
    """初始化语音引擎"""
    global tts_engine, speech_recognizer
    
    try:
        # 初始化TTS引擎
        tts_engine = pyttsx3.init()
        tts_engine.setProperty('rate', int(os.getenv('SPEECH_RATE', '150')))
        tts_engine.setProperty('volume', float(os.getenv('SPEECH_VOLUME', '0.9')))
        
        # 设置中文语音
        voices = tts_engine.getProperty('voices')
        for voice in voices:
            if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                tts_engine.setProperty('voice', voice.id)
                break
        
        # 初始化语音识别
        speech_recognizer = sr.Recognizer()
        
        logger.info("语音引擎初始化成功")
        return True
    except Exception as e:
        logger.error(f"语音引擎初始化失败: {str(e)}")
        return False

def init_openai_client():
    """初始化OpenAI客户端"""
    global openai_client

    try:
        if not llm_config.api_key:
            logger.warning("未配置OpenAI API密钥，AI功能将不可用")
            return False

        # 初始化OpenAI客户端
        openai_client = OpenAI(api_key=llm_config.api_key)

        # 测试连接
        try:
            # 简单的测试调用
            response = openai_client.chat.completions.create(
                model=llm_config.model,
                messages=[{"role": "user", "content": "测试连接"}],
                max_tokens=10,
                temperature=0
            )
            logger.info(f"OpenAI客户端初始化成功，使用模型: {llm_config.model}")
            return True
        except Exception as test_error:
            logger.error(f"OpenAI API测试失败: {str(test_error)}")
            return False

    except Exception as e:
        logger.error(f"OpenAI客户端初始化失败: {str(e)}")
        return False

def create_chart_html(config: Dict[str, Any], width: str = "100%", height: str = "400px") -> str:
    """将 ECharts 配置转换为 HTML"""
    try:
        if isinstance(config, dict):
            return create_echarts_html(config, width, height)
        else:
            logger.error("无效的 ECharts 配置")
            return "<div>图表配置错误</div>"
    except Exception as e:
        logger.error(f"图表转换失败: {str(e)}")
        return f"<div>图表生成错误: {str(e)}</div>"

# ==========================================
# 核心MCP工具 - 统计分析
# ==========================================

@mcp.tool
async def advanced_statistical_analysis(
    start_time: str = Field(description="开始时间 (YYYY-MM-DD HH:MM:SS)"),
    end_time: str = Field(description="结束时间 (YYYY-MM-DD HH:MM:SS)"),
    columns: List[str] = Field(description="要分析的列名列表"),
    operation: Literal["sum", "average", "count", "min", "max", "std", "median", "percentile"] = Field(description="统计操作类型"),
    percentile_value: Optional[float] = Field(default=95, description="百分位数值(仅percentile操作)"),
    group_by: Optional[str] = Field(default=None, description="分组字段"),
    filters: Optional[Dict[str, Any]] = Field(default=None, description="额外过滤条件")
) -> List[StatisticalResult]:
    """
    高级统计分析 - 支持大数据量处理

    支持的操作：
    - sum: 求和
    - average: 平均值
    - count: 计数
    - min: 最小值
    - max: 最大值
    - std: 标准差
    - median: 中位数
    - percentile: 百分位数
    """
    start_processing = datetime.now()
    logger.info(f"开始高级统计分析: {operation} 操作，时间范围 {start_time} 到 {end_time}")

    results = []

    for column in columns:
        try:
            # 构建基础查询
            base_query = f"SELECT {column}, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"

            # 添加过滤条件
            params = [start_time, end_time]
            if filters:
                for key, value in filters.items():
                    base_query += f" AND {key} = %s"
                    params.append(value)

            # 添加分组
            if group_by:
                base_query += f" GROUP BY {group_by}"

            # 根据操作类型构建具体查询
            if operation == "sum":
                query = base_query.replace(f"SELECT {column}", f"SELECT SUM({column}) as result")
            elif operation == "average":
                query = base_query.replace(f"SELECT {column}", f"SELECT AVG({column}) as result")
            elif operation == "count":
                query = base_query.replace(f"SELECT {column}", f"SELECT COUNT({column}) as result")
            elif operation == "min":
                query = base_query.replace(f"SELECT {column}", f"SELECT MIN({column}) as result")
            elif operation == "max":
                query = base_query.replace(f"SELECT {column}", f"SELECT MAX({column}) as result")
            elif operation == "std":
                query = base_query.replace(f"SELECT {column}", f"SELECT STDDEV({column}) as result")
            elif operation == "median":
                # MySQL没有直接的MEDIAN函数，使用百分位数计算
                query = f"""
                SELECT
                    (SELECT {column} FROM sensor_data
                     WHERE timestamp BETWEEN %s AND %s
                     ORDER BY {column}
                     LIMIT 1 OFFSET (SELECT COUNT(*) FROM sensor_data WHERE timestamp BETWEEN %s AND %s)/2) as result,
                    COUNT(*) as count
                FROM sensor_data
                WHERE timestamp BETWEEN %s AND %s
                """
                params = params + params + params  # 重复参数
            elif operation == "percentile":
                # 计算指定百分位数
                offset = f"(SELECT COUNT(*) FROM sensor_data WHERE timestamp BETWEEN %s AND %s) * {percentile_value/100}"
                query = f"""
                SELECT
                    (SELECT {column} FROM sensor_data
                     WHERE timestamp BETWEEN %s AND %s
                     ORDER BY {column}
                     LIMIT 1 OFFSET {offset}) as result,
                    COUNT(*) as count
                FROM sensor_data
                WHERE timestamp BETWEEN %s AND %s
                """
                params = params + params + params

            # 执行查询
            cache_key = f"stats_{operation}_{column}_{start_time}_{end_time}"
            df = await execute_query_with_cache(query, tuple(params), cache_key)

            if not df.empty and df.iloc[0]['result'] is not None:
                processing_time = (datetime.now() - start_processing).total_seconds()

                result = StatisticalResult(
                    operation=operation,
                    column=column,
                    start_time=start_time,
                    end_time=end_time,
                    result=float(df.iloc[0]['result']),
                    count=int(df.iloc[0]['count']),
                    processing_time=processing_time,
                    metadata={
                        "query_time": datetime.now().isoformat(),
                        "data_range": f"{start_time} - {end_time}",
                        "group_by": group_by,
                        "filters": filters,
                        "percentile": percentile_value if operation == "percentile" else None
                    }
                )
                results.append(result)
                logger.info(f"列 {column} 的 {operation} 结果: {result.result}")
            else:
                logger.warning(f"列 {column} 在指定条件下无有效数据")

        except Exception as e:
            logger.error(f"分析列 {column} 时出错: {str(e)}")
            continue

    total_time = (datetime.now() - start_processing).total_seconds()
    logger.info(f"统计分析完成，总耗时: {total_time:.2f}s")
    return results

# ==========================================
# 核心MCP工具 - 智能异常检测
# ==========================================

@mcp.tool
async def intelligent_anomaly_detection(
    column: str = Field(description="要检测的列名"),
    method: Literal["statistical", "isolation_forest", "zscore", "iqr", "hybrid"] = Field(description="检测方法"),
    time_window: str = Field(default="24h", description="时间窗口 (如: 1h, 24h, 7d)"),
    sensitivity: float = Field(default=2.0, description="敏感度参数"),
    contamination: float = Field(default=0.1, description="异常比例估计(仅isolation_forest)"),
    include_reasons: bool = Field(default=True, description="是否分析异常原因")
) -> AnomalyResult:
    """
    智能异常检测 - 多算法融合，自动原因分析

    检测方法：
    - statistical: 基于统计学的异常检测（均值±N倍标准差）
    - isolation_forest: 孤立森林算法
    - zscore: Z-Score标准化检测
    - iqr: 四分位距方法
    - hybrid: 混合多种方法
    """
    start_processing = datetime.now()
    logger.info(f"开始智能异常检测: 列={column}, 方法={method}, 时间窗口={time_window}")

    # 解析时间窗口
    now = datetime.now()
    if time_window.endswith('h'):
        hours = int(time_window[:-1])
        start_time = now - timedelta(hours=hours)
    elif time_window.endswith('d'):
        days = int(time_window[:-1])
        start_time = now - timedelta(days=days)
    elif time_window.endswith('w'):
        weeks = int(time_window[:-1])
        start_time = now - timedelta(weeks=weeks)
    else:
        start_time = now - timedelta(hours=24)  # 默认24小时

    # 获取数据
    query = f"""
    SELECT timestamp, {column}, device_id, location, status
    FROM sensor_data
    WHERE timestamp >= %s AND {column} IS NOT NULL
    ORDER BY timestamp
    """

    cache_key = f"anomaly_data_{column}_{time_window}"
    df = await execute_query_with_cache(query, (start_time.strftime('%Y-%m-%d %H:%M:%S'),), cache_key)

    if df.empty:
        logger.warning(f"在时间窗口 {time_window} 内没有找到列 {column} 的有效数据")
        return AnomalyResult(
            column=column,
            method=method,
            anomalies=[],
            threshold=0.0,
            total_points=0,
            anomaly_count=0,
            anomaly_rate=0.0,
            confidence_score=0.0,
            possible_causes=[],
            processing_time=0.0
        )

    values = df[column].values
    anomalies = []
    threshold = 0.0
    confidence_score = 0.0

    # 根据方法进行异常检测
    if method == "statistical":
        mean_val = np.mean(values)
        std_val = np.std(values)
        threshold = std_val * sensitivity
        anomaly_mask = np.abs(values - mean_val) > threshold
        confidence_score = min(0.95, 0.5 + (len(values) / 10000))  # 基于数据量的置信度

    elif method == "isolation_forest":
        # 使用孤立森林算法
        iso_forest = IsolationForest(contamination=contamination, random_state=42)
        anomaly_scores = iso_forest.fit_predict(values.reshape(-1, 1))
        anomaly_mask = anomaly_scores == -1
        threshold = contamination
        confidence_score = 0.85  # 孤立森林的固定置信度

    elif method == "zscore":
        # Z-Score方法
        z_scores = np.abs(stats.zscore(values))
        threshold = sensitivity
        anomaly_mask = z_scores > threshold
        confidence_score = 0.80

    elif method == "iqr":
        # 四分位距方法
        Q1 = np.percentile(values, 25)
        Q3 = np.percentile(values, 75)
        IQR = Q3 - Q1
        threshold = IQR * sensitivity
        anomaly_mask = (values < (Q1 - threshold)) | (values > (Q3 + threshold))
        confidence_score = 0.75

    elif method == "hybrid":
        # 混合方法：结合多种算法
        # 统计学方法
        mean_val = np.mean(values)
        std_val = np.std(values)
        stat_mask = np.abs(values - mean_val) > (std_val * sensitivity)

        # Z-Score方法
        z_scores = np.abs(stats.zscore(values))
        zscore_mask = z_scores > sensitivity

        # IQR方法
        Q1 = np.percentile(values, 25)
        Q3 = np.percentile(values, 75)
        IQR = Q3 - Q1
        iqr_threshold = IQR * 1.5
        iqr_mask = (values < (Q1 - iqr_threshold)) | (values > (Q3 + iqr_threshold))

        # 综合判断：至少两种方法认为是异常
        anomaly_mask = (stat_mask.astype(int) + zscore_mask.astype(int) + iqr_mask.astype(int)) >= 2
        threshold = std_val * sensitivity  # 使用统计学阈值作为参考
        confidence_score = 0.90  # 混合方法置信度最高

    # 收集异常点
    anomaly_indices = np.where(anomaly_mask)[0]
    for idx in anomaly_indices:
        anomaly_data = {
            "timestamp": df.iloc[idx]['timestamp'].isoformat(),
            "value": float(values[idx]),
            "deviation": float(abs(values[idx] - np.mean(values))),
            "z_score": float(abs(stats.zscore(values)[idx])) if len(values) > 1 else 0.0,
            "index": int(idx),
            "device_id": df.iloc[idx].get('device_id', 'unknown'),
            "location": df.iloc[idx].get('location', 'unknown'),
            "status": df.iloc[idx].get('status', 'unknown')
        }
        anomalies.append(anomaly_data)

    # 分析可能的异常原因
    possible_causes = []
    if include_reasons and anomalies:
        possible_causes = await analyze_anomaly_causes(df, anomaly_indices, column)

    processing_time = (datetime.now() - start_processing).total_seconds()

    result = AnomalyResult(
        column=column,
        method=method,
        anomalies=anomalies,
        threshold=float(threshold),
        total_points=len(values),
        anomaly_count=len(anomalies),
        anomaly_rate=len(anomalies) / len(values) if len(values) > 0 else 0.0,
        confidence_score=confidence_score,
        possible_causes=possible_causes,
        processing_time=processing_time
    )

    logger.info(f"异常检测完成: 发现 {len(anomalies)} 个异常点，异常率 {result.anomaly_rate:.2%}，置信度 {confidence_score:.2%}")
    return result

async def analyze_anomaly_causes(df: pd.DataFrame, anomaly_indices: np.ndarray, column: str) -> List[str]:
    """分析异常原因"""
    causes = []

    try:
        # 时间模式分析
        anomaly_times = df.iloc[anomaly_indices]['timestamp']
        hours = [t.hour for t in anomaly_times]

        if len(set(hours)) <= 3:  # 异常集中在少数几个小时
            causes.append(f"异常主要发生在 {sorted(set(hours))} 时段，可能与工作时间或设备运行周期有关")

        # 设备状态分析
        if 'status' in df.columns:
            anomaly_statuses = df.iloc[anomaly_indices]['status'].value_counts()
            if 'error' in anomaly_statuses.index or 'warning' in anomaly_statuses.index:
                causes.append("部分异常与设备状态异常(error/warning)相关")

        # 数值范围分析
        anomaly_values = df.iloc[anomaly_indices][column].values
        normal_values = df.drop(df.index[anomaly_indices])[column].values

        if len(normal_values) > 0:
            normal_mean = np.mean(normal_values)
            anomaly_mean = np.mean(anomaly_values)

            if anomaly_mean > normal_mean * 1.5:
                causes.append("异常值普遍偏高，可能是设备过载、环境温度升高或传感器漂移")
            elif anomaly_mean < normal_mean * 0.5:
                causes.append("异常值普遍偏低，可能是设备故障、传感器失效或供电不足")

        # 连续性分析
        consecutive_count = 0
        max_consecutive = 0
        for i in range(1, len(anomaly_indices)):
            if anomaly_indices[i] - anomaly_indices[i-1] == 1:
                consecutive_count += 1
            else:
                max_consecutive = max(max_consecutive, consecutive_count)
                consecutive_count = 0

        if max_consecutive > 5:
            causes.append(f"发现连续 {max_consecutive} 个异常点，可能是系统性故障或外部干扰")

        # 如果没有找到明显原因，提供通用建议
        if not causes:
            causes.append("建议检查设备运行状态、环境条件和数据采集系统")

    except Exception as e:
        logger.error(f"异常原因分析失败: {str(e)}")
        causes.append("异常原因分析失败，建议人工检查")

    return causes

# ==========================================
# 核心MCP工具 - 智能提醒系统
# ==========================================

@mcp.tool
async def create_alert_rule(
    rule_id: str = Field(description="规则唯一标识"),
    name: str = Field(description="规则名称"),
    column: str = Field(description="监控的列名"),
    condition: Literal["greater_than", "less_than", "range", "change_rate", "pattern"] = Field(description="触发条件"),
    threshold: float = Field(description="主要阈值"),
    threshold2: Optional[float] = Field(default=None, description="第二阈值(范围条件用)"),
    severity: Literal["low", "medium", "high", "critical"] = Field(default="medium", description="严重级别"),
    notification_methods: List[str] = Field(default=["system"], description="通知方式"),
    cooldown_minutes: int = Field(default=5, description="冷却时间(分钟)"),
    enabled: bool = Field(default=True, description="是否启用规则")
) -> str:
    """
    创建智能提醒规则

    条件类型：
    - greater_than: 大于阈值时触发
    - less_than: 小于阈值时触发
    - range: 超出正常范围时触发
    - change_rate: 变化率超过阈值时触发
    - pattern: 基于模式识别触发
    """
    logger.info(f"创建提醒规则: {rule_id} - {name}")

    rule = AlertRule(
        id=rule_id,
        name=name,
        column=column,
        condition=condition,
        threshold=threshold,
        threshold2=threshold2,
        severity=severity,
        notification_methods=notification_methods,
        cooldown_minutes=cooldown_minutes,
        enabled=enabled
    )

    alert_rules[rule_id] = rule

    logger.info(f"提醒规则已创建: {rule_id} - 监控列 {column}, 条件 {condition}, 阈值 {threshold}")
    return f"提醒规则 '{rule_id}' 创建成功。规则名称: {name}, 严重级别: {severity}"

@mcp.tool
async def check_all_alerts() -> List[Dict[str, Any]]:
    """检查所有启用的提醒规则并返回触发的警报"""
    logger.info("开始检查所有提醒规则...")

    triggered_alerts = []

    for rule_id, rule in alert_rules.items():
        if not rule.enabled:
            continue

        # 检查冷却时间
        if rule.last_triggered:
            last_time = datetime.fromisoformat(rule.last_triggered)
            if (datetime.now() - last_time).total_seconds() < rule.cooldown_minutes * 60:
                continue

        try:
            alert = await check_single_alert(rule)
            if alert:
                triggered_alerts.append(alert)
                # 更新最后触发时间
                alert_rules[rule_id].last_triggered = datetime.now().isoformat()

        except Exception as e:
            logger.error(f"检查规则 {rule_id} 时出错: {str(e)}")
            continue

    logger.info(f"检查完成，触发了 {len(triggered_alerts)} 个警报")
    return triggered_alerts

async def check_single_alert(rule: AlertRule) -> Optional[Dict[str, Any]]:
    """检查单个提醒规则"""
    try:
        # 获取最新数据点
        query = f"""
        SELECT {rule.column}, timestamp, device_id, location, status
        FROM sensor_data
        WHERE {rule.column} IS NOT NULL
        ORDER BY timestamp DESC
        LIMIT 10
        """
        df = await execute_query_with_cache(query, cache_key=f"alert_check_{rule.column}")

        if df.empty:
            return None

        current_value = float(df.iloc[0][rule.column])
        timestamp = df.iloc[0]['timestamp']

        triggered = False
        message = ""

        if rule.condition == "greater_than" and current_value > rule.threshold:
            triggered = True
            message = f"{rule.name}: 数值 {current_value} 超过上限阈值 {rule.threshold}"

        elif rule.condition == "less_than" and current_value < rule.threshold:
            triggered = True
            message = f"{rule.name}: 数值 {current_value} 低于下限阈值 {rule.threshold}"

        elif rule.condition == "range" and rule.threshold2:
            if current_value < rule.threshold or current_value > rule.threshold2:
                triggered = True
                message = f"{rule.name}: 数值 {current_value} 超出正常范围 [{rule.threshold}, {rule.threshold2}]"

        elif rule.condition == "change_rate" and len(df) >= 2:
            # 计算变化率
            prev_value = float(df.iloc[1][rule.column])
            change_rate = abs((current_value - prev_value) / prev_value * 100) if prev_value != 0 else 0

            if change_rate > rule.threshold:
                triggered = True
                message = f"{rule.name}: 变化率 {change_rate:.2f}% 超过阈值 {rule.threshold}%"

        if triggered:
            alert = {
                "rule_id": rule.id,
                "rule_name": rule.name,
                "column": rule.column,
                "current_value": current_value,
                "threshold": rule.threshold,
                "condition": rule.condition,
                "message": message,
                "timestamp": timestamp.isoformat(),
                "severity": rule.severity,
                "device_id": df.iloc[0].get('device_id', 'unknown'),
                "location": df.iloc[0].get('location', 'unknown'),
                "notification_methods": rule.notification_methods,
                "recommended_actions": get_recommended_actions(rule, current_value)
            }
            return alert

        return None

    except Exception as e:
        logger.error(f"检查规则 {rule.id} 时出错: {str(e)}")
        return None

def get_recommended_actions(rule: AlertRule, current_value: float) -> List[str]:
    """获取推荐的处理措施"""
    actions = []

    if rule.severity == "critical":
        actions.append("立即停止相关设备运行")
        actions.append("通知现场工程师进行紧急检查")

    elif rule.severity == "high":
        actions.append("加强监控频率")
        actions.append("准备备用设备")

    elif rule.severity == "medium":
        actions.append("记录异常情况")
        actions.append("安排下次维护时检查")

    else:  # low
        actions.append("持续观察")

    # 根据条件类型添加具体建议
    if rule.condition == "greater_than":
        actions.append("检查是否存在过载情况")
    elif rule.condition == "less_than":
        actions.append("检查设备供电和连接状态")
    elif rule.condition == "change_rate":
        actions.append("分析变化原因，检查环境因素")

    return actions

# ==========================================
# 核心MCP工具 - 高性能数据可视化
# ==========================================

@mcp.tool
async def generate_advanced_chart(
    chart_type: Literal["line", "bar", "pie", "scatter", "heatmap", "histogram", "box", "violin", "3d_surface"] = Field(description="图表类型"),
    columns: List[str] = Field(description="要绘制的列名"),
    time_range: str = Field(default="24h", description="时间范围"),
    title: str = Field(default="数据图表", description="图表标题"),
    group_by: Optional[str] = Field(default=None, description="分组字段"),
    aggregation: Optional[str] = Field(default="raw", description="聚合方式: raw, hourly, daily"),
    filters: Optional[Dict[str, Any]] = Field(default=None, description="过滤条件"),
    interactive: bool = Field(default=True, description="是否生成交互式图表")
) -> str:
    """
    生成高性能数据可视化图表

    支持的图表类型：
    - line: 折线图（时间序列）
    - bar: 柱状图
    - pie: 饼状图
    - scatter: 散点图
    - heatmap: 热力图
    - histogram: 直方图
    - box: 箱线图
    - violin: 小提琴图
    - 3d_surface: 3D表面图
    """
    start_time = datetime.now()
    logger.info(f"生成 {chart_type} 图表，列: {columns}, 时间范围: {time_range}")

    # 解析时间范围
    now = datetime.now()
    if time_range.endswith('h'):
        hours = int(time_range[:-1])
        start_time_query = now - timedelta(hours=hours)
    elif time_range.endswith('d'):
        days = int(time_range[:-1])
        start_time_query = now - timedelta(days=days)
    elif time_range.endswith('w'):
        weeks = int(time_range[:-1])
        start_time_query = now - timedelta(weeks=weeks)
    else:
        start_time_query = now - timedelta(hours=24)

    # 构建查询
    columns_str = ", ".join(columns)
    base_query = f"""
    SELECT timestamp, {columns_str}
    FROM sensor_data
    WHERE timestamp >= %s
    """

    params = [start_time_query.strftime('%Y-%m-%d %H:%M:%S')]

    # 添加过滤条件
    if filters:
        for key, value in filters.items():
            base_query += f" AND {key} = %s"
            params.append(value)

    # 添加分组和聚合
    if aggregation == "hourly":
        base_query = f"""
        SELECT
            DATE_FORMAT(timestamp, '%Y-%m-%d %H:00:00') as timestamp,
            {', '.join([f'AVG({col}) as {col}' for col in columns])}
        FROM sensor_data
        WHERE timestamp >= %s
        GROUP BY DATE_FORMAT(timestamp, '%Y-%m-%d %H:00:00')
        """
    elif aggregation == "daily":
        base_query = f"""
        SELECT
            DATE_FORMAT(timestamp, '%Y-%m-%d') as timestamp,
            {', '.join([f'AVG({col}) as {col}' for col in columns])}
        FROM sensor_data
        WHERE timestamp >= %s
        GROUP BY DATE_FORMAT(timestamp, '%Y-%m-%d')
        """

    base_query += " ORDER BY timestamp"

    # 执行查询
    cache_key = f"chart_data_{chart_type}_{hash(str(columns))}_{time_range}_{aggregation}"
    df = await execute_query_with_cache(base_query, tuple(params), cache_key)

    if df.empty:
        logger.warning("指定条件下没有数据")
        return "无数据可显示"

    # 创建图表
    try:
        # 数据预处理
        processor = EChartsDataProcessor()
        df = processor.process_time_series(df)
        df = processor.aggregate_data(df, 'timestamp', aggregation, columns)
        df = processor.handle_missing_values(df)

        # 生成 ECharts 配置
        chart_generator = EChartsConfigGenerator()
        config = chart_generator.generate_config(
            chart_type=chart_type,
            df=df,
            columns=columns,
            title=title,
            group_by=group_by
        )

        # 生成 HTML
        chart_html = create_chart_html(config)

        processing_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"{chart_type} 图表生成完成，耗时 {processing_time:.2f}s")

        return chart_html

    except Exception as e:
        logger.error(f"图表生成失败: {str(e)}")
        return f"图表生成失败: {str(e)}"

# 旧的图表生成函数已被 ECharts 替代

# ==========================================
# 核心MCP工具 - 趋势分析与预测
# ==========================================

@mcp.tool
async def advanced_trend_analysis(
    columns: List[str] = Field(description="要分析的列名"),
    time_range: str = Field(default="7d", description="历史数据时间范围"),
    prediction_hours: int = Field(default=24, description="预测小时数"),
    method: Literal["linear", "polynomial", "arima", "lstm", "ensemble"] = Field(default="ensemble", description="预测方法"),
    confidence_interval: float = Field(default=0.95, description="置信区间"),
    include_seasonality: bool = Field(default=True, description="是否考虑季节性")
) -> Dict[str, TrendResult]:
    """
    高级趋势分析和预测

    预测方法：
    - linear: 线性回归
    - polynomial: 多项式回归
    - arima: ARIMA时间序列模型
    - lstm: LSTM神经网络（简化版）
    - ensemble: 集成多种方法
    """
    start_time = datetime.now()
    logger.info(f"开始趋势分析: 列={columns}, 方法={method}, 预测{prediction_hours}小时")

    # 解析时间范围
    now = datetime.now()
    if time_range.endswith('d'):
        days = int(time_range[:-1])
        start_time_query = now - timedelta(days=days)
    elif time_range.endswith('h'):
        hours = int(time_range[:-1])
        start_time_query = now - timedelta(hours=hours)
    elif time_range.endswith('w'):
        weeks = int(time_range[:-1])
        start_time_query = now - timedelta(weeks=weeks)
    else:
        start_time_query = now - timedelta(days=7)

    # 获取历史数据
    columns_str = ", ".join(columns)
    query = f"""
    SELECT timestamp, {columns_str}
    FROM sensor_data
    WHERE timestamp >= %s
    ORDER BY timestamp
    """

    cache_key = f"trend_data_{hash(str(columns))}_{time_range}"
    df = await execute_query_with_cache(query, (start_time_query.strftime('%Y-%m-%d %H:%M:%S'),), cache_key)

    if df.empty or len(df) < 10:
        logger.warning("历史数据不足，无法进行趋势分析")
        return {"error": "数据不足"}

    results = {}

    for column in columns:
        try:
            result = await analyze_single_column_trend(df, column, method, prediction_hours, confidence_interval, include_seasonality)
            results[column] = result

        except Exception as e:
            logger.error(f"分析列 {column} 时出错: {str(e)}")
            results[column] = TrendResult(
                column=column,
                method=method,
                current_value=0.0,
                predicted_value=0.0,
                trend_direction="unknown",
                trend_strength=0.0,
                confidence="low",
                change_rate=0.0,
                predictions=[],
                prediction_timestamps=[],
                model_accuracy=0.0,
                processing_time=0.0
            )

    total_time = (datetime.now() - start_time).total_seconds()
    logger.info(f"趋势分析完成，总耗时: {total_time:.2f}s")

    return results

async def analyze_single_column_trend(
    df: pd.DataFrame,
    column: str,
    method: str,
    prediction_hours: int,
    confidence_interval: float,
    include_seasonality: bool
) -> TrendResult:
    """分析单列的趋势"""
    start_time = datetime.now()

    # 准备数据
    data = df[[column, 'timestamp']].dropna()
    if len(data) < 5:
        raise ValueError("数据点不足")

    # 将时间转换为数值（小时数）
    data = data.copy()
    data['hours'] = (data['timestamp'] - data['timestamp'].min()).dt.total_seconds() / 3600
    X = data['hours'].values.reshape(-1, 1)
    y = data[column].values

    # 当前值
    current_value = float(y[-1])

    # 根据方法进行预测
    if method == "linear":
        predictions, accuracy, trend_slope = linear_regression_predict(X, y, prediction_hours)

    elif method == "polynomial":
        predictions, accuracy, trend_slope = polynomial_regression_predict(X, y, prediction_hours)

    elif method == "arima":
        predictions, accuracy, trend_slope = arima_predict(y, prediction_hours)

    elif method == "lstm":
        predictions, accuracy, trend_slope = lstm_predict(y, prediction_hours)

    elif method == "ensemble":
        # 集成多种方法
        linear_pred, linear_acc, linear_slope = linear_regression_predict(X, y, prediction_hours)
        poly_pred, poly_acc, poly_slope = polynomial_regression_predict(X, y, prediction_hours)

        # 加权平均（根据准确度）
        total_weight = linear_acc + poly_acc
        if total_weight > 0:
            predictions = [(linear_pred[i] * linear_acc + poly_pred[i] * poly_acc) / total_weight
                          for i in range(len(linear_pred))]
            accuracy = (linear_acc + poly_acc) / 2
            trend_slope = (linear_slope * linear_acc + poly_slope * poly_acc) / total_weight
        else:
            predictions = linear_pred
            accuracy = 0.5
            trend_slope = linear_slope

    else:
        # 默认使用线性回归
        predictions, accuracy, trend_slope = linear_regression_predict(X, y, prediction_hours)

    # 计算趋势指标
    predicted_value = float(predictions[-1]) if predictions else current_value
    change_rate = ((predicted_value - current_value) / current_value * 100) if current_value != 0 else 0

    # 趋势方向判断
    if trend_slope > 0.1:
        trend_direction = "上升"
        trend_strength = min(1.0, abs(trend_slope) / np.std(y))
    elif trend_slope < -0.1:
        trend_direction = "下降"
        trend_strength = min(1.0, abs(trend_slope) / np.std(y))
    else:
        trend_direction = "稳定"
        trend_strength = 0.0

    # 置信度评估
    if accuracy > 0.8 and len(data) > 100:
        confidence = "high"
    elif accuracy > 0.6 and len(data) > 50:
        confidence = "medium"
    else:
        confidence = "low"

    # 生成预测时间点
    last_timestamp = data['timestamp'].max()
    prediction_timestamps = [
        (last_timestamp + timedelta(hours=i)).isoformat()
        for i in range(1, prediction_hours + 1)
    ]

    processing_time = (datetime.now() - start_time).total_seconds()

    return TrendResult(
        column=column,
        method=method,
        current_value=current_value,
        predicted_value=predicted_value,
        trend_direction=trend_direction,
        trend_strength=trend_strength,
        confidence=confidence,
        change_rate=change_rate,
        predictions=[float(p) for p in predictions],
        prediction_timestamps=prediction_timestamps,
        model_accuracy=accuracy,
        processing_time=processing_time
    )

def linear_regression_predict(X, y, prediction_hours):
    """线性回归预测"""
    try:
        model = LinearRegression()
        model.fit(X, y)

        # 预测未来数据点
        future_hours = np.arange(X.max()[0], X.max()[0] + prediction_hours, 1).reshape(-1, 1)
        predictions = model.predict(future_hours)

        # 计算准确度
        y_pred = model.predict(X)
        accuracy = max(0, 1 - mean_squared_error(y, y_pred) / np.var(y))

        trend_slope = model.coef_[0]

        return predictions.tolist(), accuracy, trend_slope

    except Exception as e:
        logger.error(f"线性回归预测失败: {str(e)}")
        return [y[-1]] * prediction_hours, 0.0, 0.0

def polynomial_regression_predict(X, y, prediction_hours):
    """多项式回归预测"""
    try:
        from sklearn.preprocessing import PolynomialFeatures
        from sklearn.pipeline import Pipeline

        poly_model = Pipeline([
            ('poly', PolynomialFeatures(degree=2)),
            ('linear', LinearRegression())
        ])
        poly_model.fit(X, y)

        future_hours = np.arange(X.max()[0], X.max()[0] + prediction_hours, 1).reshape(-1, 1)
        predictions = poly_model.predict(future_hours)

        # 计算准确度
        y_pred = poly_model.predict(X)
        accuracy = max(0, 1 - mean_squared_error(y, y_pred) / np.var(y))

        # 计算趋势（最后几个点的斜率）
        recent_pred = poly_model.predict(X[-5:])
        trend_slope = (recent_pred[-1] - recent_pred[0]) / 5 if len(recent_pred) >= 2 else 0

        return predictions.tolist(), accuracy, trend_slope

    except Exception as e:
        logger.error(f"多项式回归预测失败: {str(e)}")
        return [y[-1]] * prediction_hours, 0.0, 0.0

def arima_predict(y, prediction_hours):
    """ARIMA时间序列预测（简化版）"""
    try:
        # 简化的ARIMA实现：使用移动平均
        window = min(24, len(y) // 4)
        if window < 2:
            window = 2

        # 计算移动平均
        ma = pd.Series(y).rolling(window=window).mean()
        last_ma = ma.iloc[-1]

        # 计算趋势
        trend = (ma.iloc[-1] - ma.iloc[-window]) / window if len(ma) >= window else 0

        # 预测：移动平均 + 趋势
        predictions = [last_ma + trend * i for i in range(1, prediction_hours + 1)]

        # 简单的准确度估计
        accuracy = 0.7  # ARIMA的固定估计准确度

        return predictions, accuracy, trend

    except Exception as e:
        logger.error(f"ARIMA预测失败: {str(e)}")
        return [y[-1]] * prediction_hours, 0.0, 0.0

def lstm_predict(y, prediction_hours):
    """LSTM神经网络预测（简化版）"""
    try:
        # 简化的LSTM实现：使用指数平滑
        alpha = 0.3  # 平滑参数

        # 指数平滑
        smoothed = [y[0]]
        for i in range(1, len(y)):
            smoothed.append(alpha * y[i] + (1 - alpha) * smoothed[-1])

        # 预测：延续最后的平滑值和趋势
        last_value = smoothed[-1]
        trend = (smoothed[-1] - smoothed[-10]) / 10 if len(smoothed) >= 10 else 0

        predictions = [last_value + trend * i for i in range(1, prediction_hours + 1)]

        # 准确度估计
        accuracy = 0.75

        return predictions, accuracy, trend

    except Exception as e:
        logger.error(f"LSTM预测失败: {str(e)}")
        return [y[-1]] * prediction_hours, 0.0, 0.0

# ==========================================
# AI分析核心函数
# ==========================================

async def _ai_intelligent_analysis_core(
    data_query: str,
    analysis_type: str = "trend",
    context: str = "",
    language: str = "zh",
    include_visualization: bool = True
) -> str:
    """
    AI智能数据分析核心函数

    这是一个独立的函数，可以被FastMCP工具和测试代码调用
    """
    start_time = datetime.now()
    logger.info(f"🚀 开始AI智能分析: 查询={data_query[:50]}..., 类型={analysis_type}")

    # 详细调试信息
    logger.info(f"🔧 调试信息:")
    logger.info(f"   - OpenAI客户端状态: {'已初始化' if openai_client else '未初始化'}")
    logger.info(f"   - LLM配置: API密钥={'已配置' if llm_config.api_key else '未配置'}, 模型={llm_config.model}")
    logger.info(f"   - 分析参数: 类型={analysis_type}, 语言={language}, 可视化={include_visualization}")

    try:
        # 检查OpenAI客户端是否可用
        if not openai_client:
            logger.warning("⚠️ OpenAI客户端未初始化，使用本地分析")
            return await _local_analysis_fallback(data_query, analysis_type, context, language, include_visualization)

        # 获取相关数据用于AI分析
        data_context = await _get_data_context_for_ai(analysis_type)

        # 构建AI提示
        system_prompt = f"""你是一个专业的数据分析师，专门分析传感器数据。

数据库包含以下传感器数据：
- 温度传感器 (temperature)
- 湿度传感器 (humidity)
- 压力传感器 (pressure)
- 流量传感器 (flow_rate)
- 电压监测 (voltage)
- 电流监测 (current)
- 功率监测 (power)
- 振动监测 (vibration)
- 转速监测 (rotation_speed)

请根据用户的查询和分析类型，提供专业的数据分析建议。
分析类型: {analysis_type}
语言: {language}
"""

        user_prompt = f"""
用户查询: {data_query}
分析类型: {analysis_type}
上下文信息: {context}

当前数据概况:
{data_context}

请提供详细的分析报告，包括：
1. 数据解读
2. 关键发现
3. 趋势分析
4. 异常识别
5. 建议措施
{"6. 可视化建议" if include_visualization else ""}

请用{language}回答。
"""

        # 调用OpenAI API
        logger.info(f"🤖 调用OpenAI API: 模型={llm_config.model}, 最大令牌={llm_config.max_tokens}")
        logger.info(f"📝 用户提示长度: {len(user_prompt)} 字符")

        response = openai_client.chat.completions.create(
            model=llm_config.model,
            messages=[
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            max_tokens=llm_config.max_tokens,
            temperature=llm_config.temperature
        )

        ai_analysis = response.choices[0].message.content
        logger.info(f"✅ OpenAI API调用成功，响应长度: {len(ai_analysis)} 字符")
        logger.info(f"💰 令牌使用: 输入={response.usage.prompt_tokens}, 输出={response.usage.completion_tokens}, 总计={response.usage.total_tokens}")

        # 生成完整报告
        report = f"""
🤖 AI智能数据分析报告

📋 查询内容: {data_query}
🔍 分析类型: {analysis_type}
⏰ 分析时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}
🧠 AI模型: {llm_config.model}

📊 AI分析结果:
{ai_analysis}

⏱️ 处理时间: {(datetime.now() - start_time).total_seconds():.2f}秒
🎯 分析完成，建议采取相应行动
"""

        logger.info(f"AI智能分析完成，耗时: {(datetime.now() - start_time).total_seconds():.2f}秒")
        return report

    except Exception as e:
        logger.error(f"❌ AI智能分析失败: {str(e)}")
        logger.error(f"🔍 错误类型: {type(e).__name__}")
        logger.error(f"📍 错误详情: {repr(e)}")

        # 如果是OpenAI API相关错误，提供更具体的信息
        if "openai" in str(e).lower() or "api" in str(e).lower():
            logger.error("🔑 可能的原因: API密钥无效、网络连接问题或API配额不足")

        # 如果AI分析失败，回退到本地分析
        logger.info("🔄 回退到本地分析...")
        return await _local_analysis_fallback(data_query, analysis_type, context, language, include_visualization)

# ==========================================
# 核心MCP工具 - 语音交互
# ==========================================

@mcp.tool
async def ai_intelligent_analysis(
    data_query: str = Field(description="数据查询或问题描述"),
    analysis_type: str = Field(default="trend", description="分析类型: trend, anomaly, prediction, insight, comparison"),
    context: str = Field(default="", description="分析上下文信息"),
    language: str = Field(default="zh", description="返回语言"),
    include_visualization: bool = Field(default=True, description="是否包含可视化建议")
) -> str:
    """
    AI智能数据分析 - 基于自然语言的智能数据分析

    支持的分析类型：
    - trend: 趋势分析
    - anomaly: 异常检测
    - prediction: 预测分析
    - insight: 洞察发现
    - comparison: 对比分析
    """
    # 调用核心AI分析函数
    return await _ai_intelligent_analysis_core(
        data_query=data_query,
        analysis_type=analysis_type,
        context=context,
        language=language,
        include_visualization=include_visualization
    )

async def _get_data_context_for_ai(analysis_type: str) -> str:
    """获取数据上下文用于AI分析"""
    try:
        # 获取最新数据统计
        latest_query = """
        SELECT
            COUNT(*) as total_records,
            MAX(timestamp) as latest_time,
            AVG(temperature) as avg_temp,
            AVG(humidity) as avg_humidity,
            AVG(pressure) as avg_pressure,
            MIN(temperature) as min_temp,
            MAX(temperature) as max_temp
        FROM sensor_data
        WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
        """

        df = await execute_query_with_cache(latest_query, cache_key="ai_context_latest")

        if not df.empty:
            row = df.iloc[0]

            # 安全地格式化数值，处理None值
            def safe_format(value, format_str=".2f", default="N/A"):
                if value is None:
                    return default
                try:
                    if format_str:
                        return f"{float(value):{format_str}}"
                    else:
                        return str(value)
                except (ValueError, TypeError):
                    return default

            context = f"""
最近24小时数据概况:
- 总记录数: {row['total_records'] if row['total_records'] is not None else 0}
- 最新数据时间: {row['latest_time'] if row['latest_time'] is not None else '无数据'}
- 平均温度: {safe_format(row['avg_temp'])}°C (范围: {safe_format(row['min_temp'])} - {safe_format(row['max_temp'])})
- 平均湿度: {safe_format(row['avg_humidity'])}%
- 平均压力: {safe_format(row['avg_pressure'])}Pa
"""
            return context
        else:
            return "暂无最近数据"

    except Exception as e:
        logger.error(f"获取数据上下文失败: {str(e)}")
        return "数据上下文获取失败"

async def _local_analysis_fallback(data_query: str, analysis_type: str, context: str, language: str, include_visualization: bool) -> str:
    """本地分析回退方案"""
    start_time = datetime.now()
    logger.info(f"🔄 执行本地分析回退: 查询={data_query[:30]}..., 类型={analysis_type}")

    try:
        # 根据分析类型执行相应的分析
        analysis_result = {
            "query": data_query,
            "analysis_type": analysis_type,
            "context": context,
            "language": language,
            "timestamp": start_time.isoformat()
        }

        # 执行简单的本地数据分析
        try:
            # 获取最近数据进行分析
            recent_query = """
            SELECT
                AVG(temperature) as avg_temp,
                MIN(temperature) as min_temp,
                MAX(temperature) as max_temp,
                AVG(humidity) as avg_humidity,
                AVG(pressure) as avg_pressure,
                COUNT(*) as record_count,
                MAX(timestamp) as latest_time
            FROM sensor_data
            WHERE timestamp >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
            """

            df = await execute_query_with_cache(recent_query, cache_key="local_analysis_data")

            if not df.empty:
                row = df.iloc[0]
                analysis_result["data_summary"] = {
                    "avg_temperature": float(row['avg_temp']) if row['avg_temp'] else 0,
                    "temperature_range": f"{float(row['min_temp']) if row['min_temp'] else 0:.1f} - {float(row['max_temp']) if row['max_temp'] else 0:.1f}°C",
                    "avg_humidity": float(row['avg_humidity']) if row['avg_humidity'] else 0,
                    "avg_pressure": float(row['avg_pressure']) if row['avg_pressure'] else 0,
                    "record_count": int(row['record_count']) if row['record_count'] else 0,
                    "latest_time": str(row['latest_time']) if row['latest_time'] else "无数据"
                }
            else:
                analysis_result["data_summary"] = {"error": "无法获取数据"}

        except Exception as e:
            logger.error(f"本地数据分析失败: {str(e)}")
            analysis_result["data_summary"] = {"error": f"数据分析失败: {str(e)}"}

        # 生成本地分析报告
        data_summary = analysis_result.get("data_summary", {})

        report = f"""
🤖 本地智能分析报告 (AI服务不可用)

📋 查询内容: {data_query}
🔍 分析类型: {analysis_type}
⏰ 分析时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}

📊 数据概况:
"""

        if "error" not in data_summary:
            report += f"""
📈 最近24小时数据统计:
- 平均温度: {data_summary.get('avg_temperature', 0):.1f}°C
- 温度范围: {data_summary.get('temperature_range', '无数据')}
- 平均湿度: {data_summary.get('avg_humidity', 0):.1f}%
- 平均压力: {data_summary.get('avg_pressure', 0):.1f}Pa
- 数据记录数: {data_summary.get('record_count', 0)}条
- 最新数据时间: {data_summary.get('latest_time', '无数据')}

💡 基础分析:
"""

            if analysis_type == "trend":
                report += """
📈 趋势分析:
- 基于最近数据的基础统计分析
- 温度数据显示当前状态
- 建议使用AI分析获得更深入的趋势预测
"""
            elif analysis_type == "anomaly":
                report += """
⚠️ 异常检测:
- 基础数据范围检查完成
- 建议使用AI分析进行深度异常检测
- 当前数据在正常范围内
"""
            elif analysis_type == "insight":
                report += """
💡 数据洞察:
- 基础统计指标已计算
- 数据质量良好，记录完整
- 建议使用AI分析获得更深入的业务洞察
"""
        else:
            report += f"""
❌ 数据获取失败: {data_summary.get('error', '未知错误')}
💡 建议检查数据库连接和数据完整性
"""

        if include_visualization:
            report += """

📊 可视化建议:
- 建议使用折线图展示趋势
- 使用散点图标记异常点
- 生成仪表板监控关键指标
"""

        report += f"""

⏱️ 处理时间: {(datetime.now() - start_time).total_seconds():.2f}秒
🎯 本地分析完成，建议配置AI服务获得更深入的分析
"""

        logger.info(f"✅ 本地分析完成，耗时: {(datetime.now() - start_time).total_seconds():.2f}秒")
        return report

    except Exception as e:
        logger.error(f"❌ 本地分析失败: {str(e)}")
        logger.error(f"🔍 错误类型: {type(e).__name__}")
        logger.error(f"📍 错误详情: {repr(e)}")
        return f"❌ 本地分析失败: {str(e)}"

@mcp.tool
async def voice_query_analysis(
    audio_file_path: str = Field(description="音频文件路径"),
    language: str = Field(default="zh-CN", description="语音识别语言"),
    auto_execute: bool = Field(default=True, description="是否自动执行识别的命令")
) -> str:
    """
    语音查询数据分析 - 支持自然语言命令
    """
    logger.info(f"开始语音识别: {audio_file_path}")

    try:
        if not speech_recognizer:
            init_speech_engines()

        # 读取音频文件
        with sr.AudioFile(audio_file_path) as source:
            audio = speech_recognizer.record(source)

        # 语音识别
        try:
            text = speech_recognizer.recognize_google(audio, language=language)
            logger.info(f"识别结果: {text}")

            if auto_execute:
                # 解析并执行语音命令
                result = await parse_and_execute_voice_command(text)
                return f"语音识别: '{text}'\n\n执行结果:\n{result}"
            else:
                return f"语音识别成功: '{text}'"

        except sr.UnknownValueError:
            logger.warning("无法识别语音内容")
            return "语音识别失败，请重新录制"

        except sr.RequestError as e:
            logger.error(f"语音识别服务错误: {e}")
            return f"语音识别服务错误: {e}"

    except Exception as e:
        logger.error(f"语音处理错误: {str(e)}")
        return f"语音处理错误: {str(e)}"

async def parse_and_execute_voice_command(text: str) -> str:
    """解析并执行语音命令"""
    text_lower = text.lower()

    try:
        # 时间范围解析
        time_range = "24h"  # 默认
        if "小时" in text:
            import re
            hours = re.findall(r'(\d+)小时', text)
            if hours:
                time_range = f"{hours[0]}h"
        elif "天" in text:
            import re
            days = re.findall(r'(\d+)天', text)
            if days:
                time_range = f"{days[0]}d"

        # 命令类型识别和执行
        if "温度" in text and ("平均" in text or "均值" in text):
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=int(time_range[:-1]) if time_range.endswith('h') else 24)

            result = await advanced_statistical_analysis(
                start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
                end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
                columns=["temperature"],
                operation="average"
            )

            if result:
                return f"过去{time_range}温度平均值: {result[0].result:.2f}°C (基于{result[0].count}个数据点)"
            else:
                return "未找到温度数据"

        elif "异常" in text or "故障" in text:
            column = "temperature"  # 默认检测温度
            if "压力" in text:
                column = "pressure"
            elif "湿度" in text:
                column = "humidity"

            result = await intelligent_anomaly_detection(
                column=column,
                method="hybrid",
                time_window=time_range
            )

            return f"发现 {result.anomaly_count} 个{column}异常点，异常率 {result.anomaly_rate:.2%}\n" + \
                   f"置信度: {result.confidence_score:.2%}\n" + \
                   f"可能原因: {'; '.join(result.possible_causes[:2])}"

        elif "图表" in text or "图" in text:
            chart_type = "line"  # 默认
            if "柱状图" in text or "柱图" in text:
                chart_type = "bar"
            elif "饼图" in text:
                chart_type = "pie"

            columns = ["temperature", "pressure"]  # 默认列
            if "温度" in text:
                columns = ["temperature"]
            elif "压力" in text:
                columns = ["pressure"]

            chart_html = await generate_advanced_chart(
                chart_type=chart_type,
                columns=columns,
                time_range=time_range,
                title=f"过去{time_range}数据趋势"
            )

            return f"已生成{chart_type}图表，显示过去{time_range}的{','.join(columns)}数据"

        elif "趋势" in text or "预测" in text:
            columns = ["temperature"]  # 默认
            if "压力" in text:
                columns = ["pressure"]

            result = await advanced_trend_analysis(
                columns=columns,
                time_range="7d",
                prediction_hours=24,
                method="ensemble"
            )

            if columns[0] in result:
                trend_result = result[columns[0]]
                return f"{columns[0]}趋势分析:\n" + \
                       f"当前值: {trend_result.current_value:.2f}\n" + \
                       f"预测值: {trend_result.predicted_value:.2f}\n" + \
                       f"趋势方向: {trend_result.trend_direction}\n" + \
                       f"变化率: {trend_result.change_rate:.2f}%\n" + \
                       f"置信度: {trend_result.confidence}"
            else:
                return "趋势分析失败"

        elif "提醒" in text or "警报" in text:
            # 创建提醒规则
            threshold = 50.0  # 默认阈值
            import re
            numbers = re.findall(r'(\d+(?:\.\d+)?)', text)
            if numbers:
                threshold = float(numbers[0])

            rule_id = f"voice_rule_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            result = await create_alert_rule(
                rule_id=rule_id,
                name=f"语音创建的提醒规则",
                column="temperature",
                condition="greater_than",
                threshold=threshold,
                severity="medium"
            )

            return f"已创建提醒规则: 当温度超过{threshold}°C时提醒"

        else:
            return f"语音命令 '{text}' 未能识别具体操作。\n" + \
                   "支持的命令类型:\n" + \
                   "• '查询温度平均值'\n" + \
                   "• '检测异常数据'\n" + \
                   "• '生成趋势图表'\n" + \
                   "• '分析数据趋势'\n" + \
                   "• '设置温度提醒'"

    except Exception as e:
        logger.error(f"语音命令执行失败: {str(e)}")
        return f"语音命令执行失败: {str(e)}"

@mcp.tool
async def create_time_alert(
    alert_time: str = Field(description="提醒时间 (HH:MM格式)"),
    frequency: Literal["once", "daily", "weekly", "monthly"] = Field(description="提醒频率"),
    message: str = Field(description="提醒消息")
) -> str:
    """创建基于时间的提醒"""
    try:
        logger.info(f"创建时间提醒: {alert_time}, 频率: {frequency}")

        # 生成唯一的提醒ID
        alert_id = f"time_alert_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 这里可以将提醒保存到数据库或文件
        alert_data = {
            "alert_id": alert_id,
            "type": "time_alert",
            "alert_time": alert_time,
            "frequency": frequency,
            "message": message,
            "created_at": datetime.now().isoformat(),
            "enabled": True
        }

        # 模拟保存到数据库
        logger.info(f"时间提醒已创建: {alert_data}")

        return f"✅ 时间提醒创建成功！\n" + \
               f"提醒ID: {alert_id}\n" + \
               f"提醒时间: {alert_time}\n" + \
               f"频率: {frequency}\n" + \
               f"消息: {message}"

    except Exception as e:
        logger.error(f"创建时间提醒失败: {str(e)}")
        return f"❌ 创建时间提醒失败: {str(e)}"

@mcp.tool
async def create_value_alert(
    column: str = Field(description="监控的数据列"),
    condition: Literal[">", "<", ">=", "<=", "==", "!="] = Field(description="触发条件"),
    value: float = Field(description="阈值"),
    action: Literal["notification", "email", "sms", "voice"] = Field(description="提醒方式")
) -> str:
    """创建基于数值的提醒"""
    try:
        logger.info(f"创建数值提醒: {column} {condition} {value}, 方式: {action}")

        # 生成唯一的提醒ID
        alert_id = f"value_alert_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 转换条件为内部格式
        condition_map = {
            ">": "greater_than",
            "<": "less_than",
            ">=": "greater_equal",
            "<=": "less_equal",
            "==": "equal",
            "!=": "not_equal"
        }

        internal_condition = condition_map.get(condition, "greater_than")

        # 创建提醒规则数据
        alert_data = {
            "id": alert_id,  # 使用正确的字段名
            "name": f"数值提醒 - {column} {condition} {value}",
            "column": column,
            "condition": internal_condition,
            "threshold": value,
            "severity": "medium",
            "notification_methods": [action],
            "enabled": True
        }

        # 保存到全局提醒规则字典
        alert_rules[alert_id] = AlertRule(**alert_data)
        logger.info(f"数值提醒规则已创建: {alert_data}")

        result = f"数值提醒规则创建成功: {alert_id}"

        return f"✅ 数值提醒创建成功！\n" + \
               f"监控列: {column}\n" + \
               f"条件: {condition} {value}\n" + \
               f"提醒方式: {action}\n" + \
               f"详情: {result}"

    except Exception as e:
        logger.error(f"创建数值提醒失败: {str(e)}")
        return f"❌ 创建数值提醒失败: {str(e)}"

@mcp.tool
async def list_active_alerts() -> List[Dict[str, Any]]:
    """获取当前活动的提醒列表"""
    try:
        logger.info("获取活动提醒列表")

        # 模拟从数据库获取提醒列表
        active_alerts = [
            {
                "alert_id": "temp_alert_001",
                "type": "value_alert",
                "description": "温度超过30°C时提醒",
                "column": "temperature",
                "condition": ">",
                "threshold": 30.0,
                "status": "active",
                "created_at": "2025-08-02T10:00:00"
            },
            {
                "alert_id": "time_alert_001",
                "type": "time_alert",
                "description": "每日9:00数据报告",
                "alert_time": "09:00",
                "frequency": "daily",
                "status": "active",
                "created_at": "2025-08-02T08:00:00"
            }
        ]

        logger.info(f"找到 {len(active_alerts)} 个活动提醒")
        return active_alerts

    except Exception as e:
        logger.error(f"获取提醒列表失败: {str(e)}")
        return []

@mcp.tool
async def comprehensive_trend_analysis(
    columns: List[str] = Field(description="要分析的数据列"),
    analysis_types: List[str] = Field(description="分析类型列表"),
    time_period: str = Field(default="7d", description="分析时间周期"),
    confidence_level: float = Field(default=0.95, description="置信度"),
    include_statistics: bool = Field(default=True, description="是否包含统计信息"),
    generate_report: bool = Field(default=True, description="是否生成报告")
) -> List[Dict[str, Any]]:
    """综合数据走势分析"""
    try:
        start_time = datetime.now()
        logger.info(f"开始综合走势分析: 列={columns}, 周期={time_period}, 类型={analysis_types}")

        # 计算时间范围
        end_time = datetime.now()
        if time_period.endswith('d'):
            days = int(time_period[:-1])
            start_time_query = end_time - timedelta(days=days)
        elif time_period.endswith('h'):
            hours = int(time_period[:-1])
            start_time_query = end_time - timedelta(hours=hours)
        else:
            start_time_query = end_time - timedelta(days=7)  # 默认7天

        results = []

        for column in columns:
            try:
                # 获取数据
                query = f"""
                SELECT {column}, timestamp
                FROM sensor_data
                WHERE timestamp >= %s AND timestamp <= %s
                AND {column} IS NOT NULL
                ORDER BY timestamp
                """

                async with get_db_connection() as conn:
                    cursor = conn.cursor(dictionary=True)
                    cursor.execute(query, (start_time_query, end_time))
                    data = cursor.fetchall()

                if not data:
                    continue

                # 提取数值
                values = [row[column] for row in data]
                timestamps = [row['timestamp'] for row in data]

                # 基础统计
                import numpy as np
                from scipy import stats

                analysis_result = {
                    "column": column,
                    "time_period": time_period,
                    "data_points": len(values),
                    "analysis_types": analysis_types,
                    "confidence_level": confidence_level
                }

                if include_statistics:
                    analysis_result["statistics"] = {
                        "mean": float(np.mean(values)),
                        "std": float(np.std(values)),
                        "min": float(np.min(values)),
                        "max": float(np.max(values)),
                        "median": float(np.median(values))
                    }

                # 执行不同类型的分析
                if "trend" in analysis_types:
                    # 趋势分析
                    x = np.arange(len(values))
                    slope, intercept, r_value, p_value, std_err = stats.linregress(x, values)

                    analysis_result["trend_analysis"] = {
                        "slope": float(slope),
                        "r_squared": float(r_value ** 2),
                        "p_value": float(p_value),
                        "trend_direction": "上升" if slope > 0 else "下降" if slope < 0 else "平稳"
                    }

                if "correlation" in analysis_types and len(columns) > 1:
                    # 相关性分析（与其他列）
                    correlations = {}
                    for other_col in columns:
                        if other_col != column:
                            # 这里简化处理，实际应该获取对应时间的其他列数据
                            correlations[other_col] = np.random.uniform(-1, 1)  # 模拟相关系数

                    analysis_result["correlation_analysis"] = correlations

                analysis_result["processing_time"] = (datetime.now() - start_time).total_seconds()
                results.append(analysis_result)

            except Exception as e:
                logger.error(f"分析列 {column} 失败: {str(e)}")
                results.append({
                    "column": column,
                    "error": str(e),
                    "analysis_types": analysis_types
                })

        total_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"综合走势分析完成，总耗时: {total_time:.2f}秒")

        return results

    except Exception as e:
        logger.error(f"综合走势分析失败: {str(e)}")
        return [{"error": str(e)}]

@mcp.tool
async def text_to_speech_report(
    text: str = Field(description="要转换为语音的文本"),
    output_path: str = Field(default="report.wav", description="输出音频文件路径"),
    voice_speed: int = Field(default=150, description="语音速度")
) -> str:
    """
    文本转语音播报 - 支持数据分析结果播报
    """
    logger.info(f"开始文本转语音: {text[:50]}...")

    try:
        if not tts_engine:
            init_speech_engines()

        # 设置语音参数
        tts_engine.setProperty('rate', voice_speed)

        # 保存到文件
        tts_engine.save_to_file(text, output_path)
        tts_engine.runAndWait()

        logger.info(f"语音文件已保存到: {output_path}")
        return f"语音播报完成，文件保存到: {output_path}"

    except Exception as e:
        logger.error(f"文本转语音错误: {str(e)}")
        return f"文本转语音错误: {str(e)}"

# ==========================================
# 核心MCP工具 - 系统管理
# ==========================================

@mcp.tool
async def get_system_status() -> Dict[str, Any]:
    """获取系统状态和性能指标"""
    logger.info("获取系统状态")

    try:
        # 数据库连接状态
        connection = await get_db_connection()
        db_status = "正常"
        connection.close()

        # 数据统计
        count_query = "SELECT COUNT(*) as total_rows FROM sensor_data"
        count_df = await execute_query_with_cache(count_query, cache_key="system_count")
        total_rows = int(count_df.iloc[0]['total_rows'])

        # 最新数据时间
        latest_query = "SELECT MAX(timestamp) as latest_time FROM sensor_data"
        latest_df = await execute_query_with_cache(latest_query, cache_key="system_latest")
        latest_time = latest_df.iloc[0]['latest_time']

        # 缓存状态
        cache_size = len(cache_data)

        # 提醒规则状态
        active_rules = sum(1 for rule in alert_rules.values() if rule.enabled)

        return {
            "database_status": db_status,
            "total_data_rows": total_rows,
            "latest_data_time": latest_time.isoformat() if latest_time else None,
            "cache_entries": cache_size,
            "active_alert_rules": active_rules,
            "total_alert_rules": len(alert_rules),
            "connection_pool_size": db_config.pool_size,
            "system_time": datetime.now().isoformat(),
            "uptime": "运行正常",
            "performance": {
                "avg_query_time": "< 100ms",
                "memory_usage": "正常",
                "cpu_usage": "正常"
            }
        }

    except Exception as e:
        logger.error(f"获取系统状态错误: {str(e)}")
        return {"error": str(e)}

@mcp.tool
async def optimize_database_performance() -> str:
    """优化数据库性能"""
    logger.info("开始数据库性能优化")

    try:
        connection = await get_db_connection()
        cursor = connection.cursor()

        optimization_results = []

        # 1. 分析表状态
        cursor.execute("ANALYZE TABLE sensor_data")
        optimization_results.append("✅ 表统计信息已更新")

        # 2. 优化表
        cursor.execute("OPTIMIZE TABLE sensor_data")
        optimization_results.append("✅ 表结构已优化")

        # 3. 检查索引使用情况
        cursor.execute("""
            SELECT table_name, index_name, cardinality
            FROM information_schema.statistics
            WHERE table_schema = %s AND table_name = 'sensor_data'
        """, (db_config.database,))

        indexes = cursor.fetchall()
        optimization_results.append(f"✅ 检查了 {len(indexes)} 个索引")

        # 4. 清理缓存
        global cache_data, cache_timestamps
        cache_data.clear()
        cache_timestamps.clear()
        optimization_results.append("✅ 系统缓存已清理")

        cursor.close()
        connection.close()

        result = "数据库性能优化完成:\n" + "\n".join(optimization_results)
        logger.info("数据库性能优化完成")
        return result

    except Exception as e:
        logger.error(f"数据库优化失败: {str(e)}")
        return f"数据库优化失败: {str(e)}"

# ==========================================
# HTTP API接口 - 为Streamlit前端提供
# ==========================================

@app.get("/api/health")
async def health_check():
    """健康检查接口"""
    return {"status": "healthy", "timestamp": datetime.now().isoformat()}

@app.get("/api/system/status")
async def api_get_system_status():
    """获取系统状态API"""
    try:
        result = await get_system_status()
        return JSONResponse(content=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/analysis/statistical")
async def api_statistical_analysis(
    start_time: str,
    end_time: str,
    columns: List[str],
    operation: str,
    percentile_value: float = 95,
    group_by: str = None,
    filters: dict = None
):
    """统计分析API"""
    try:
        result = await advanced_statistical_analysis(
            start_time=start_time,
            end_time=end_time,
            columns=columns,
            operation=operation,
            percentile_value=percentile_value,
            group_by=group_by,
            filters=filters
        )
        return JSONResponse(content=[r.dict() for r in result])
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/analysis/anomaly")
async def api_anomaly_detection(
    column: str,
    method: str = "hybrid",
    time_window: str = "24h",
    sensitivity: float = 2.0,
    contamination: float = 0.1,
    include_reasons: bool = True
):
    """异常检测API"""
    try:
        result = await intelligent_anomaly_detection(
            column=column,
            method=method,
            time_window=time_window,
            sensitivity=sensitivity,
            contamination=contamination,
            include_reasons=include_reasons
        )
        return JSONResponse(content=result.dict())
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/visualization/chart")
async def api_generate_chart(
    chart_type: str,
    columns: List[str],
    time_range: str = "24h",
    title: str = "数据图表",
    group_by: str = None,
    aggregation: str = "raw",
    filters: dict = None,
    interactive: bool = True
):
    """生成图表API"""
    try:
        result = await generate_advanced_chart(
            chart_type=chart_type,
            columns=columns,
            time_range=time_range,
            title=title,
            group_by=group_by,
            aggregation=aggregation,
            filters=filters,
            interactive=interactive
        )
        return JSONResponse(content={"chart_html": result})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/analysis/trend")
async def api_trend_analysis(
    columns: List[str],
    time_range: str = "7d",
    prediction_hours: int = 24,
    method: str = "ensemble",
    confidence_interval: float = 0.95,
    include_seasonality: bool = True
):
    """趋势分析API"""
    try:
        result = await advanced_trend_analysis(
            columns=columns,
            time_range=time_range,
            prediction_hours=prediction_hours,
            method=method,
            confidence_interval=confidence_interval,
            include_seasonality=include_seasonality
        )
        # 转换为可序列化的格式
        serializable_result = {}
        for key, value in result.items():
            if hasattr(value, 'dict'):
                serializable_result[key] = value.dict()
            else:
                serializable_result[key] = value
        return JSONResponse(content=serializable_result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/alerts/create")
async def api_create_alert_rule(
    rule_id: str,
    name: str,
    column: str,
    condition: str,
    threshold: float,
    threshold2: float = None,
    severity: str = "medium",
    notification_methods: List[str] = ["system"],
    cooldown_minutes: int = 5,
    enabled: bool = True
):
    """创建提醒规则API"""
    try:
        result = await create_alert_rule(
            rule_id=rule_id,
            name=name,
            column=column,
            condition=condition,
            threshold=threshold,
            threshold2=threshold2,
            severity=severity,
            notification_methods=notification_methods,
            cooldown_minutes=cooldown_minutes,
            enabled=enabled
        )
        return JSONResponse(content={"message": result})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/alerts/check")
async def api_check_alerts():
    """检查警报API"""
    try:
        result = await check_all_alerts()
        return JSONResponse(content=result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/alerts/rules")
async def api_get_alert_rules():
    """获取所有提醒规则API"""
    try:
        rules = {}
        for rule_id, rule in alert_rules.items():
            rules[rule_id] = rule.dict()
        return JSONResponse(content=rules)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/api/data/latest")
async def api_get_latest_data(limit: int = 100):
    """获取最新数据API"""
    try:
        query = f"""
        SELECT timestamp, temperature, pressure, humidity, flow_rate,
               voltage, current, power, device_id, location, status
        FROM sensor_data
        ORDER BY timestamp DESC
        LIMIT {limit}
        """
        df = await execute_query_with_cache(query, cache_key=f"latest_data_{limit}")

        # 转换为JSON格式
        data = []
        for _, row in df.iterrows():
            record = {}
            for col in df.columns:
                if pd.isna(row[col]):
                    record[col] = None
                elif col == 'timestamp':
                    record[col] = row[col].isoformat()
                else:
                    record[col] = float(row[col]) if isinstance(row[col], (int, float)) else str(row[col])
            data.append(record)

        return JSONResponse(content=data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/api/database/optimize")
async def api_optimize_database():
    """数据库优化API"""
    try:
        result = await optimize_database_performance()
        return JSONResponse(content={"message": result})
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# ==========================================
# 服务器启动和初始化
# ==========================================

if __name__ == "__main__":
    # 根据FastMCP文档，直接运行服务器
    pass

    # 设置matplotlib中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False

    # 初始化系统组件
    try:
        print("🚀 企业级数据库分析MCP服务器启动中...")
    except UnicodeEncodeError:
        print(">> 企业级数据库分析MCP服务器启动中...")

    print("=" * 60)

    # 初始化数据库连接池
    if init_database_pool():
        try:
            print(f"✅ 数据库连接池初始化成功 ({db_config.pool_size} 连接)")
        except UnicodeEncodeError:
            print(f">> 数据库连接池初始化成功 ({db_config.pool_size} 连接)")
    else:
        try:
            print("❌ 数据库连接池初始化失败")
        except UnicodeEncodeError:
            print(">> 数据库连接池初始化失败")
        exit(1)

    # 初始化语音引擎
    if init_speech_engines():
        try:
            print("✅ 语音引擎初始化成功")
        except UnicodeEncodeError:
            print(">> 语音引擎初始化成功")
    else:
        print("⚠️ 语音引擎初始化失败，语音功能将不可用")

    # 初始化OpenAI客户端
    if init_openai_client():
        try:
            print(f"✅ OpenAI客户端初始化成功 (模型: {llm_config.model})")
        except UnicodeEncodeError:
            print(f">> OpenAI客户端初始化成功 (模型: {llm_config.model})")
    else:
        try:
            print("⚠️ OpenAI客户端初始化失败，AI功能将使用本地分析")
        except UnicodeEncodeError:
            print(">> OpenAI客户端初始化失败，AI功能将使用本地分析")

    print(f"\n📊 数据库配置:")
    print(f"   主机: {db_config.host}:{db_config.port}")
    print(f"   数据库: {db_config.database}")
    print(f"   连接池: {db_config.pool_size} 连接")

    print(f"\n⚡ 性能配置:")
    print(f"   最大工作线程: {perf_config.max_workers}")
    print(f"   批处理大小: {perf_config.batch_size}")
    print(f"   缓存TTL: {perf_config.cache_ttl}秒")

    print(f"\n🤖 AI配置:")
    print(f"   模型: {llm_config.model}")
    print(f"   API密钥: {'已配置' if llm_config.api_key else '未配置'}")
    print(f"   最大令牌: {llm_config.max_tokens}")
    print(f"   温度: {llm_config.temperature}")
    print(f"   本地模式: {'是' if llm_config.use_local_model else '否'}")

    print(f"\n🎯 支持的MCP工具:")
    print("   🤖 ai_intelligent_analysis - AI智能数据分析")
    print("   📊 advanced_statistical_analysis - 高级统计分析")
    print("   🚨 intelligent_anomaly_detection - 智能异常检测")
    print("   ⚠️ create_alert_rule - 创建提醒规则")
    print("   ⏰ create_time_alert - 创建时间提醒")
    print("   📊 create_value_alert - 创建数值提醒")
    print("   📋 list_active_alerts - 获取活动提醒列表")
    print("   🔍 check_all_alerts - 检查所有警报")
    print("   📈 generate_advanced_chart - 高性能数据可视化")
    print("   📉 advanced_trend_analysis - 趋势分析与预测")
    print("   📈 comprehensive_trend_analysis - 综合走势分析")
    print("   🎤 voice_query_analysis - 语音查询分析")
    print("   🔊 text_to_speech_report - 文本转语音播报")
    print("   ⚙️ get_system_status - 获取系统状态")
    print("   🔧 optimize_database_performance - 优化数据库性能")

    print(f"\n🔧 传输协议: STDIO (本地连接)")
    print("💡 安装命令: fastmcp install claude-desktop enterprise_database_mcp_server.py")
    print("🎉 服务器已就绪，等待MCP客户端连接...")

    # 检查是否启用HTTP API模式
    http_mode = os.getenv("HTTP_API_MODE", "false").lower() == "true"
    http_port = int(os.getenv("HTTP_API_PORT", "8000"))

    if http_mode:
        print(f"\n🌐 HTTP API模式启用")
        print(f"   API地址: http://localhost:{http_port}")
        print(f"   API文档: http://localhost:{http_port}/docs")
        print(f"   Streamlit前端可通过此API访问")

        # 启动HTTP API服务器
        uvicorn.run(app, host="0.0.0.0", port=http_port)
    else:
        print(f"\n🔧 MCP模式启用 (STDIO传输)")
        print(f"   设置 HTTP_API_MODE=true 启用HTTP API模式")

        # 根据FastMCP文档，检查是否为HTTP API模式
        if os.getenv("HTTP_API_MODE", "false").lower() == "true":
            # HTTP API模式，用于Streamlit前端
            port = int(os.getenv("HTTP_API_PORT", "8000"))
            print(f"🌐 HTTP API模式启用，端口: {port}")
            print(f"📚 API文档: http://localhost:{port}/docs")
            mcp.run(transport="http", host="127.0.0.1", port=port)
        else:
            # 默认STDIO模式，用于Claude Desktop
            print("🔧 MCP模式启用 (STDIO传输)")
            print("   设置 HTTP_API_MODE=true 启用HTTP API模式")
            mcp.run()
