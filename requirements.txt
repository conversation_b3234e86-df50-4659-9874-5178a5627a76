# 数据库分析MCP服务器依赖包
# =====================================

# 核心MCP框架
fastmcp>=2.0.0

# Streamlit前端
streamlit>=1.28.0

# 数据库连接
mysql-connector-python>=8.0.0

# 数据处理和分析
pandas>=2.0.0
numpy>=1.24.0

# 数据可视化
matplotlib>=3.7.0
plotly>=5.15.0

# 机器学习和统计
scikit-learn>=1.3.0
scipy>=1.10.0

# 语音功能
speechrecognition>=3.10.0
pyttsx3>=2.90

# 数据验证
pydantic>=2.0.0

# 环境配置
python-dotenv>=1.0.0

# HTTP客户端（Streamlit前端用）
requests>=2.31.0

# FastAPI（HTTP API支持）
fastapi>=0.104.0
uvicorn>=0.24.0

# 日期时间处理
python-dateutil>=2.8.0

# 异步支持
asyncio-mqtt>=0.13.0  # 可选：MQTT支持
aiofiles>=23.0.0      # 可选：异步文件操作

# 开发和测试工具（可选）
pytest>=7.0.0
pytest-asyncio>=0.21.0
black>=23.0.0
flake8>=6.0.0

# 性能优化（可选）
numba>=0.57.0         # JIT编译加速
cython>=3.0.0         # C扩展支持

# 额外的数据库支持（可选）
# postgresql适配器
# psycopg2-binary>=2.9.0

# SQLite支持（Python内置，无需安装）

# Redis缓存支持（可选）
# redis>=4.5.0

# 消息队列支持（可选）
# celery>=5.3.0

# 监控和日志（可选）
# prometheus-client>=0.17.0
# structlog>=23.0.0

# Web界面支持（可选）
# fastapi>=0.100.0
# uvicorn>=0.23.0

# 安全增强（可选）
# cryptography>=41.0.0
# bcrypt>=4.0.0
