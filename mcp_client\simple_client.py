#!/usr/bin/env python3
"""
简单MCP客户端 - 基于FastMCP文档的标准实现
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from fastmcp import Client

class SimpleMCPClient:
    """简单MCP客户端"""
    
    def __init__(self, server_path: str = None):
        """
        初始化客户端
        
        Args:
            server_path: MCP服务器文件路径
        """
        if server_path is None:
            # 默认服务器路径
            server_path = str(Path(__file__).parent.parent / "enterprise_database_mcp_server.py")
        
        self.server_path = server_path
        self.client = None
        
        print(f"🤖 初始化MCP客户端")
        print(f"📁 服务器路径: {server_path}")
    
    async def connect(self):
        """连接到MCP服务器"""
        try:
            print("🔗 正在连接到MCP服务器...")
            
            # 根据FastMCP文档，直接传入服务器文件路径
            self.client = Client(self.server_path)
            
            # 测试连接
            async with self.client:
                await self.client.ping()
                print("✅ 连接成功!")
                return True
                
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    async def get_tools(self):
        """获取可用工具"""
        try:
            async with self.client:
                tools = await self.client.list_tools()
                return tools
        except Exception as e:
            print(f"❌ 获取工具失败: {e}")
            return []
    
    async def call_tool(self, tool_name: str, arguments: dict = None):
        """调用工具"""
        try:
            async with self.client:
                result = await self.client.call_tool(tool_name, arguments or {})
                return result
        except Exception as e:
            print(f"❌ 调用工具失败: {e}")
            return None
    
    async def get_system_status(self):
        """获取系统状态"""
        return await self.call_tool("get_system_status")
    
    async def statistical_analysis(self, start_time: str, end_time: str, columns: list, operation: str = "average"):
        """统计分析"""
        return await self.call_tool("advanced_statistical_analysis", {
            "start_time": start_time,
            "end_time": end_time,
            "columns": columns,
            "operation": operation
        })
    
    async def detect_anomalies(self, column: str, method: str = "hybrid", time_window: str = "24h"):
        """异常检测"""
        return await self.call_tool("intelligent_anomaly_detection", {
            "column": column,
            "method": method,
            "time_window": time_window
        })
    
    async def generate_chart(self, chart_type: str, columns: list, time_range: str = "24h", title: str = "数据图表"):
        """生成图表"""
        return await self.call_tool("generate_advanced_chart", {
            "chart_type": chart_type,
            "columns": columns,
            "time_range": time_range,
            "title": title
        })

async def test_basic_functionality():
    """测试基本功能"""
    print("🚀 测试基本功能")
    print("=" * 50)
    
    # 创建客户端
    client = SimpleMCPClient()
    
    # 连接测试
    if not await client.connect():
        print("❌ 无法连接到服务器")
        return
    
    # 获取工具列表
    print("\n📋 获取可用工具:")
    tools = await client.get_tools()
    if tools:
        for tool in tools:
            print(f"  • {tool.name}: {tool.description}")
    else:
        print("  ❌ 无法获取工具列表")
        return
    
    # 测试系统状态
    print("\n📊 测试系统状态:")
    status = await client.get_system_status()
    if status:
        if hasattr(status, 'data') and result.data:
            data = status.data
            print(f"  • 数据库状态: {data.get('database_status', 'unknown')}")
            print(f"  • 数据总量: {data.get('total_data_rows', 0):,}")
        else:
            print(f"  • 状态: {status}")
    else:
        print("  ❌ 无法获取系统状态")
    
    # 测试统计分析
    print("\n📈 测试统计分析:")
    try:
        result = await client.statistical_analysis(
            start_time="2024-01-01 00:00:00",
            end_time="2024-01-02 00:00:00",
            columns=["temperature"],
            operation="average"
        )
        if result:
            print(f"  ✅ 统计分析成功")
            if hasattr(result, 'data') and result.data:
                print(f"  • 结果: {result.data}")
        else:
            print("  ❌ 统计分析失败")
    except Exception as e:
        print(f"  ❌ 统计分析异常: {e}")
    
    # 测试异常检测
    print("\n🚨 测试异常检测:")
    try:
        result = await client.detect_anomalies(
            column="temperature",
            method="hybrid",
            time_window="24h"
        )
        if result:
            print(f"  ✅ 异常检测成功")
            if hasattr(result, 'data') and result.data:
                print(f"  • 结果: {result.data}")
        else:
            print("  ❌ 异常检测失败")
    except Exception as e:
        print(f"  ❌ 异常检测异常: {e}")
    
    print("\n🎉 测试完成!")

async def interactive_mode():
    """交互模式"""
    print("💬 进入交互模式")
    print("可用命令: status, tools, stats, anomaly, chart, quit")
    print("-" * 50)
    
    client = SimpleMCPClient()
    
    if not await client.connect():
        print("❌ 无法连接到服务器")
        return
    
    while True:
        try:
            command = input("\n> ").strip().lower()
            
            if command in ['quit', 'exit', 'q']:
                break
            
            elif command == 'status':
                result = await client.get_system_status()
                print(f"📊 系统状态: {result}")
            
            elif command == 'tools':
                tools = await client.get_tools()
                print("🛠️ 可用工具:")
                for tool in tools:
                    print(f"  • {tool.name}: {tool.description}")
            
            elif command == 'stats':
                result = await client.statistical_analysis(
                    start_time="2024-01-01 00:00:00",
                    end_time="2024-01-02 00:00:00",
                    columns=["temperature"],
                    operation="average"
                )
                print(f"📈 统计分析: {result}")
            
            elif command == 'anomaly':
                result = await client.detect_anomalies(
                    column="temperature",
                    method="hybrid"
                )
                print(f"🚨 异常检测: {result}")
            
            elif command == 'chart':
                result = await client.generate_chart(
                    chart_type="line",
                    columns=["temperature"],
                    time_range="24h"
                )
                print(f"📊 图表生成: {result}")
            
            else:
                print("❓ 未知命令，可用命令: status, tools, stats, anomaly, chart, quit")
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("\n👋 退出交互模式")

async def main():
    """主函数"""
    print("🤖 简单MCP客户端")
    print("=" * 50)
    
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        await interactive_mode()
    else:
        await test_basic_functionality()

if __name__ == "__main__":
    asyncio.run(main())
