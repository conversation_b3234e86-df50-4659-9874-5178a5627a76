# 数据库分析MCP服务器

基于FastMCP 2.0框架构建的专业MySQL数据库分析系统，提供完整的本地数据分析、异常检测、智能提醒和语音交互功能。

## 🌟 核心功能

### 1. 统计分析
- **时间段统计**: 按指定时间范围进行求和、求平均、计数等操作
- **多列分析**: 同时分析多个数据列
- **实时计算**: 支持大数据量实时统计

### 2. 异常检测
- **多种检测策略**:
  - 静态阈值检测
  - 动态阈值检测（基于历史数据）
  - 统计学检测（均值±N倍标准差）
- **智能分析**: 自动识别数据异常模式
- **详细报告**: 提供异常点详情和异常率统计

### 3. 智能提醒系统
- **多种触发条件**:
  - 数值超过上限/下限
  - 数值超出正常范围
  - 变化率超过阈值
- **实时监控**: 持续监控数据变化
- **分级预警**: 支持不同严重级别的警报

### 4. 数据可视化
- **丰富图表类型**:
  - 折线图（时间序列）
  - 柱状图（对比分析）
  - 饼状图（比例分析）
  - 散点图（相关性分析）
  - 热力图（相关性矩阵）
  - 直方图（分布分析）
- **交互式图表**: 基于Plotly的高质量可视化
- **自定义配置**: 支持标题、时间范围、分组等配置

### 5. 趋势分析与预测
- **多种预测方法**:
  - 线性回归
  - 多项式回归
  - 移动平均
- **趋势判断**: 自动识别上升、下降、稳定趋势
- **置信度评估**: 基于数据量评估预测可靠性

### 6. 语音交互
- **语音识别**: 支持中文语音查询
- **智能解析**: 自动识别语音命令并执行相应操作
- **语音播报**: 文本转语音功能
- **自然语言**: 支持自然语言查询方式

### 7. 数据库管理
- **表结构查询**: 获取完整的数据库表信息
- **安全查询**: 支持自定义SQL查询（只读模式）
- **连接管理**: 自动管理数据库连接池

## 🏗️ 技术架构

### 基于FastMCP 2.0
- **MCP协议**: 遵循最新的Model Context Protocol规范
- **本地部署**: 完全本地运行，无需互联网连接
- **STDIO传输**: 使用标准输入输出进行通信
- **中间件支持**: 集成时间监控和结构化日志

### 核心依赖
```
fastmcp>=2.0.0              # MCP框架
mysql-connector-python>=8.0  # MySQL连接器
pandas>=2.0.0               # 数据处理
numpy>=1.24.0               # 数值计算
matplotlib>=3.7.0           # 基础绘图
plotly>=5.15.0              # 交互式图表
speechrecognition>=3.10.0   # 语音识别
pyttsx3>=2.90               # 语音合成
scikit-learn>=1.3.0         # 机器学习
```

## 🚀 快速开始

### 1. 自动安装（推荐）
```bash
# 克隆或下载项目文件
python install_server.py
```

安装脚本会自动：
- 安装所有Python依赖
- 创建环境配置文件
- 生成示例数据库脚本
- 安装到Claude Desktop

### 2. 手动安装

#### 步骤1: 安装依赖
```bash
pip install fastmcp mysql-connector-python pandas numpy matplotlib plotly speechrecognition pyttsx3 scikit-learn
```

#### 步骤2: 配置环境
创建 `.env` 文件：
```env
# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=sensor_data
DB_CHARSET=utf8mb4

# 异常检测阈值
TEMP_THRESHOLD=50.0
PRESSURE_THRESHOLD=100.0
FLOW_THRESHOLD=200.0

# 语音配置
VOICE_LANGUAGE=zh-CN
SPEECH_RATE=150
```

#### 步骤3: 创建数据库
```sql
-- 执行 create_database.sql 中的脚本
CREATE DATABASE sensor_data;
USE sensor_data;
-- ... 创建表和示例数据
```

#### 步骤4: 安装到Claude Desktop
```bash
fastmcp install claude-desktop database_analysis_server.py \
  --name "数据库分析助手" \
  --env-file .env \
  --with mysql-connector-python \
  --with pandas \
  --with numpy \
  --with matplotlib \
  --with plotly \
  --with speechrecognition \
  --with pyttsx3 \
  --with scikit-learn
```

#### 步骤5: 重启Claude Desktop
重启Claude Desktop，看到🔨图标表示MCP工具已加载。

## 📊 使用示例

### 统计分析
```
"分析过去24小时的温度平均值"
"计算昨天的压力总和"
"统计本周的湿度最大值"
```

### 异常检测
```
"检测温度异常数据"
"分析压力异常，使用动态阈值"
"查找过去一周的统计异常"
```

### 智能提醒
```
"设置温度超过50度时提醒"
"监控压力低于100时报警"
"当湿度变化率超过10%时通知"
```

### 数据可视化
```
"生成温度趋势折线图"
"创建压力和湿度的对比柱状图"
"显示各传感器数据的相关性热力图"
```

### 趋势分析
```
"分析温度数据趋势并预测未来3天"
"使用多项式回归预测压力变化"
"计算湿度的移动平均趋势"
```

### 语音交互
```
"语音查询温度数据"
"播报当前异常警报"
"语音生成数据报告"
```

## 🔧 高级配置

### 数据库优化
```sql
-- 为大数据量优化索引
CREATE INDEX idx_timestamp_temp ON sensor_data(timestamp, temperature);
CREATE INDEX idx_timestamp_pressure ON sensor_data(timestamp, pressure);

-- 分区表（适用于海量数据）
ALTER TABLE sensor_data PARTITION BY RANGE (YEAR(timestamp)) (
    PARTITION p2024 VALUES LESS THAN (2025),
    PARTITION p2025 VALUES LESS THAN (2026)
);
```

### 性能调优
```python
# 在 .env 文件中添加
DB_POOL_SIZE=10
DB_MAX_OVERFLOW=20
QUERY_TIMEOUT=30
CACHE_SIZE=1000
```

### 自定义阈值
```python
# 针对不同传感器设置不同阈值
TEMP_MIN=0.0
TEMP_MAX=100.0
PRESSURE_MIN=90000.0
PRESSURE_MAX=110000.0
HUMIDITY_MIN=0.0
HUMIDITY_MAX=100.0
```

## 🛠️ 开发指南

### 添加新的分析工具
```python
@mcp.tool
async def custom_analysis(
    parameter: str = Field(description="参数描述")
) -> CustomResult:
    """自定义分析工具"""
    ctx = get_context()
    await ctx.info("开始自定义分析")
    
    # 实现分析逻辑
    result = perform_analysis(parameter)
    
    await ctx.info("分析完成")
    return result
```

### 扩展数据源
```python
# 支持多种数据库
async def get_connection(db_type: str):
    if db_type == "mysql":
        return mysql.connector.connect(...)
    elif db_type == "postgresql":
        return psycopg2.connect(...)
    # 添加更多数据库支持
```

## 🔍 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查 `.env` 文件中的数据库配置
   - 确认MySQL服务正在运行
   - 验证用户权限

2. **语音功能不工作**
   - 安装系统语音支持包
   - 检查麦克风权限
   - 确认语音引擎配置

3. **图表显示异常**
   - 检查matplotlib字体配置
   - 确认plotly版本兼容性
   - 验证数据格式

4. **Claude Desktop连接问题**
   - 重启Claude Desktop
   - 检查MCP配置文件
   - 查看错误日志

### 日志调试
```bash
# 启用详细日志
export LOG_LEVEL=DEBUG
python database_analysis_server.py
```

## 📈 性能指标

- **查询响应时间**: < 100ms（小数据集）
- **大数据处理**: 支持百万级数据点
- **并发连接**: 支持多客户端同时访问
- **内存使用**: 优化的数据流处理
- **实时性**: 毫秒级异常检测

## 🤝 贡献指南

欢迎提交Issue和Pull Request！

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 📄 许可证

MIT License - 详见 LICENSE 文件

## 🙏 致谢

- [FastMCP](https://github.com/jlowin/fastmcp) - 优秀的MCP框架
- [Model Context Protocol](https://modelcontextprotocol.io/) - 标准化协议
- [Anthropic](https://www.anthropic.com/) - Claude AI支持
