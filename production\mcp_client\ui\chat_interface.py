#!/usr/bin/env python3
"""
聊天界面 - 基于Streamlit的智能对话界面
"""

import asyncio
import streamlit as st
import streamlit.components.v1 as components
from datetime import datetime
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent.parent))

from mcp_client.client import MCPClient
from mcp_client.config import config
from mcp_client.utils.logger import log

class ChatInterface:
    """聊天界面"""
    
    def __init__(self):
        self.client = None
        self.setup_page()
    
    def setup_page(self):
        """设置页面配置"""
        st.set_page_config(
            page_title="企业级数据库分析助手",
            page_icon="🤖",
            layout="wide",
            initial_sidebar_state="expanded"
        )
        
        # 自定义CSS
        st.markdown("""
        <style>
        .main-header {
            font-size: 2.5rem;
            color: #1f77b4;
            text-align: center;
            margin-bottom: 2rem;
        }
        .chat-message {
            padding: 1rem;
            margin: 0.5rem 0;
            border-radius: 0.5rem;
            border-left: 4px solid #1f77b4;
            background-color: #f8f9fa;
        }
        .user-message {
            border-left-color: #28a745;
            background-color: #e8f5e9;
        }
        .assistant-message {
            border-left-color: #1f77b4;
            background-color: #e3f2fd;
        }
        .error-message {
            border-left-color: #dc3545;
            background-color: #ffebee;
        }
        .metric-card {
            background: white;
            padding: 1rem;
            border-radius: 0.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin: 0.5rem 0;
        }
        </style>
        """, unsafe_allow_html=True)
    
    async def initialize_client(self):
        """初始化客户端"""
        if self.client is None:
            try:
                self.client = MCPClient()
                success = await self.client.connect()
                if success:
                    st.success("✅ 成功连接到MCP服务器")
                    return True
                else:
                    st.error("❌ 无法连接到MCP服务器")
                    return False
            except Exception as e:
                st.error(f"❌ 初始化客户端失败: {e}")
                return False
        return True
    
    def render_sidebar(self):
        """渲染侧边栏"""
        with st.sidebar:
            st.header("🛠️ 功能面板")
            
            # 连接状态
            if self.client and self.client.connected:
                st.success("🟢 已连接")
            else:
                st.error("🔴 未连接")
            
            st.divider()
            
            # 快速操作
            st.subheader("⚡ 快速操作")
            
            if st.button("📊 系统状态", use_container_width=True):
                st.session_state.quick_query = "获取系统状态"
            
            if st.button("📈 温度趋势", use_container_width=True):
                st.session_state.quick_query = "显示最近24小时温度趋势图"
            
            if st.button("🚨 异常检测", use_container_width=True):
                st.session_state.quick_query = "检测温度异常"
            
            if st.button("📋 统计分析", use_container_width=True):
                st.session_state.quick_query = "计算最近24小时温度平均值"
            
            st.divider()
            
            # 设置
            st.subheader("⚙️ 设置")
            
            # 主题设置
            theme = st.selectbox(
                "界面主题",
                ["dark", "light"],
                index=0 if config.ui.theme == "dark" else 1
            )
            
            # 自动刷新
            auto_refresh = st.checkbox(
                "自动刷新",
                value=config.ui.auto_refresh
            )
            
            if auto_refresh:
                refresh_interval = st.slider(
                    "刷新间隔(秒)",
                    min_value=10,
                    max_value=300,
                    value=config.ui.refresh_interval,
                    step=10
                )
    
    def render_chat_history(self):
        """渲染聊天历史"""
        if "chat_history" not in st.session_state:
            st.session_state.chat_history = []
        
        # 显示聊天历史
        for message in st.session_state.chat_history:
            if message["role"] == "user":
                with st.container():
                    st.markdown(f"""
                    <div class="chat-message user-message">
                        <strong>👤 您:</strong> {message["content"]}
                    </div>
                    """, unsafe_allow_html=True)
            else:
                with st.container():
                    st.markdown(f"""
                    <div class="chat-message assistant-message">
                        <strong>🤖 助手:</strong>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    # 显示结果
                    self.render_response(message["content"])
    
    def render_response(self, response_data):
        """渲染响应数据"""
        if isinstance(response_data, dict):
            if "error" in response_data:
                st.error(f"❌ 错误: {response_data['error']}")
                return
            
            # 根据结果类型渲染
            result_type = response_data.get("metadata", {}).get("result_type", "default")
            
            if result_type == "system_status":
                self.render_system_status(response_data)
            elif result_type == "statistical_analysis":
                self.render_statistical_analysis(response_data)
            elif result_type == "anomaly_detection":
                self.render_anomaly_detection(response_data)
            elif result_type == "chart_generation":
                self.render_chart(response_data)
            else:
                self.render_default_response(response_data)
        else:
            st.write(response_data)
    
    def render_system_status(self, data):
        """渲染系统状态"""
        summary = data.get("summary", {})
        
        # 状态指标
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            status_color = "🟢" if summary.get("database_healthy") else "🔴"
            st.metric("数据库状态", f"{status_color} {'健康' if summary.get('database_healthy') else '异常'}")
        
        with col2:
            st.metric("数据总量", summary.get("total_rows_formatted", "0"))
        
        with col3:
            st.metric("连接池", summary.get("connection_pool", 0))
        
        with col4:
            st.metric("运行时间", summary.get("uptime", "未知"))
    
    def render_statistical_analysis(self, data):
        """渲染统计分析结果"""
        summary = data.get("summary", {})
        
        st.subheader(f"📊 统计分析 - {summary.get('operation', '未知操作')}")
        
        # 基本信息
        col1, col2 = st.columns(2)
        with col1:
            st.write(f"**分析列:** {', '.join(summary.get('columns_analyzed', []))}")
            st.write(f"**时间范围:** {summary.get('time_range', '未知')}")
        
        with col2:
            st.write(f"**数据点数:** {summary.get('data_points', 0):,}")
        
        # 关键指标
        if "key_metrics" in data:
            st.subheader("📈 关键指标")
            metrics = data["key_metrics"]
            
            cols = st.columns(len(metrics))
            for i, (key, value) in enumerate(metrics.items()):
                with cols[i]:
                    st.metric(key.replace("_", " ").title(), f"{value:.2f}")
    
    def render_anomaly_detection(self, data):
        """渲染异常检测结果"""
        summary = data.get("summary", {})
        
        st.subheader(f"🚨 异常检测 - {summary.get('method', '未知方法')}")
        
        # 异常摘要
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("检测列", summary.get("column", "未知"))
        
        with col2:
            st.metric("异常总数", summary.get("total_anomalies", 0))
        
        with col3:
            severity_levels = summary.get("severity_levels", {})
            high_count = severity_levels.get("high", 0) + severity_levels.get("critical", 0)
            st.metric("高危异常", high_count)
        
        # 异常详情
        if "anomaly_categories" in data:
            categories = data["anomaly_categories"]
            
            if categories.get("high_severity"):
                st.subheader("⚠️ 高危异常")
                for anomaly in categories["high_severity"][:5]:  # 显示前5个
                    st.warning(f"时间: {anomaly.get('timestamp', '未知')}, 值: {anomaly.get('value', '未知')}")
    
    def render_chart(self, data):
        """渲染图表"""
        # 检查是否是 ECharts HTML 格式
        chart_html = data.get("data", "")

        if isinstance(chart_html, str) and ("echarts" in chart_html.lower() or "<script>" in chart_html):
            # 直接渲染 ECharts HTML
            st.subheader("📈 数据图表")
            components.html(chart_html, height=500, scrolling=False)
        else:
            # 兼容旧格式
            chart_data = data.get("data", {}).get("chart_data", {})
            summary = data.get("summary", {})

            if not chart_data or "data" not in chart_data:
                st.warning("📊 图表数据不可用")
                return

            st.subheader(f"📈 {summary.get('title', '数据图表')}")

            # 根据图表类型渲染
            chart_type = summary.get("chart_type", "line")
            chart_points = chart_data["data"]

            if chart_type == "line":
                self.render_line_chart(chart_points, summary)
            elif chart_type == "bar":
                self.render_bar_chart(chart_points, summary)
            else:
                st.json(chart_data)
    
    def render_line_chart(self, data_points, summary):
        """渲染折线图"""
        if not data_points:
            return
        
        # 提取数据
        x_values = [point.get("timestamp", i) for i, point in enumerate(data_points)]
        
        fig = go.Figure()
        
        # 添加每个列的数据
        columns = summary.get("columns", ["value"])
        for column in columns:
            y_values = [point.get(column, 0) for point in data_points]
            fig.add_trace(go.Scatter(
                x=x_values,
                y=y_values,
                mode='lines+markers',
                name=column.replace("_", " ").title()
            ))
        
        fig.update_layout(
            title=summary.get("title", "数据趋势"),
            xaxis_title="时间",
            yaxis_title="数值",
            height=400
        )
        
        # 注意：这是兼容性代码，建议使用 ECharts
        st.warning("⚠️ 使用兼容模式渲染图表，建议升级到 ECharts")
        st.json({"message": "图表数据", "x_values": x_values, "y_values": y_values})
    
    def render_bar_chart(self, data_points, summary):
        """渲染柱状图"""
        if not data_points:
            return
        
        # 简单柱状图
        x_values = [point.get("label", f"项目{i}") for i, point in enumerate(data_points)]
        y_values = [point.get("value", 0) for point in data_points]
        
        fig = go.Figure(data=[go.Bar(x=x_values, y=y_values)])
        fig.update_layout(
            title=summary.get("title", "数据分布"),
            xaxis_title="类别",
            yaxis_title="数值",
            height=400
        )
        
        # 注意：这是兼容性代码，建议使用 ECharts
        st.warning("⚠️ 使用兼容模式渲染图表，建议升级到 ECharts")
        st.json({"message": "图表数据", "x_values": x_values, "columns": columns})
    
    def render_default_response(self, data):
        """渲染默认响应"""
        if "summary" in data:
            st.json(data["summary"])
        
        if "data" in data and data["data"]:
            with st.expander("📋 详细数据"):
                st.json(data["data"])
    
    async def process_query(self, query):
        """处理查询"""
        if not self.client:
            return {"error": "客户端未初始化"}
        
        try:
            # 添加用户消息到历史
            st.session_state.chat_history.append({
                "role": "user",
                "content": query,
                "timestamp": datetime.now()
            })
            
            # 处理查询
            result = await self.client.query(query)
            
            # 添加助手响应到历史
            st.session_state.chat_history.append({
                "role": "assistant", 
                "content": result,
                "timestamp": datetime.now()
            })
            
            return result
            
        except Exception as e:
            error_result = {"error": str(e)}
            st.session_state.chat_history.append({
                "role": "assistant",
                "content": error_result,
                "timestamp": datetime.now()
            })
            return error_result
    
    def run(self):
        """运行聊天界面"""
        # 页面标题
        st.markdown('<h1 class="main-header">🤖 企业级数据库分析助手</h1>', unsafe_allow_html=True)
        
        # 初始化客户端
        if asyncio.run(self.initialize_client()):
            
            # 渲染侧边栏
            self.render_sidebar()
            
            # 主要内容区域
            col1, col2 = st.columns([3, 1])
            
            with col1:
                # 聊天历史
                st.subheader("💬 对话历史")
                chat_container = st.container()
                
                with chat_container:
                    self.render_chat_history()
                
                # 输入区域
                st.subheader("✍️ 输入查询")
                
                # 检查快速查询
                query_input = ""
                if "quick_query" in st.session_state:
                    query_input = st.session_state.quick_query
                    del st.session_state.quick_query
                
                query = st.text_input(
                    "请输入您的查询:",
                    value=query_input,
                    placeholder="例如: 获取系统状态, 显示温度趋势图, 检测异常等..."
                )
                
                col_send, col_clear = st.columns([1, 1])
                
                with col_send:
                    if st.button("🚀 发送", use_container_width=True) and query:
                        with st.spinner("🤔 正在处理..."):
                            result = asyncio.run(self.process_query(query))
                        st.rerun()
                
                with col_clear:
                    if st.button("🗑️ 清空历史", use_container_width=True):
                        st.session_state.chat_history = []
                        st.rerun()
            
            with col2:
                # 状态面板
                st.subheader("📊 状态面板")
                
                if self.client and self.client.connected:
                    # 获取系统状态
                    try:
                        status_result = asyncio.run(self.client.get_system_status())
                        if "error" not in status_result:
                            self.render_system_status(status_result)
                    except:
                        st.warning("无法获取系统状态")
                
                # 可用工具
                st.subheader("🛠️ 可用工具")
                try:
                    tools = asyncio.run(self.client.get_available_tools())
                    for name, tool in tools.items():
                        with st.expander(f"🔧 {name}"):
                            st.write(tool["description"])
                except:
                    st.warning("无法获取工具列表")

def main():
    """主函数"""
    chat_interface = ChatInterface()
    chat_interface.run()

if __name__ == "__main__":
    main()
