#!/usr/bin/env python3
"""
测试AI分析功能
直接测试ai_intelligent_analysis函数
"""

import os
import sys
import asyncio
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 添加当前目录到Python路径
sys.path.insert(0, os.getcwd())

async def test_ai_analysis():
    """测试AI分析功能"""
    print("🧪 测试AI分析功能")
    print("=" * 50)
    
    try:
        # 导入必要的模块
        print("📦 导入模块...")
        from enterprise_database_mcp_server import (
            init_openai_client,
            init_database_pool,
            _ai_intelligent_analysis_core,
            llm_config,
            openai_client
        )
        print("✅ 模块导入成功")
        
        # 初始化数据库连接池
        print("\n🗄️ 初始化数据库连接池...")
        if init_database_pool():
            print("✅ 数据库连接池初始化成功")
        else:
            print("❌ 数据库连接池初始化失败")
        
        # 初始化OpenAI客户端
        print("\n🤖 初始化OpenAI客户端...")
        if init_openai_client():
            print(f"✅ OpenAI客户端初始化成功 (模型: {llm_config.model})")
            print(f"   API密钥: {'已配置' if llm_config.api_key else '未配置'}")
            print(f"   客户端状态: {'已初始化' if openai_client else '未初始化'}")
        else:
            print("❌ OpenAI客户端初始化失败")
        
        # 测试AI分析
        print("\n🧠 测试AI智能分析...")
        test_query = "分析最近24小时的传感器数据，有什么异常吗？"
        
        print(f"📝 测试查询: {test_query}")
        print("⏳ 正在分析...")
        
        result = await _ai_intelligent_analysis_core(
            data_query=test_query,
            analysis_type="insight",
            context="企业级传感器监控系统",
            language="zh",
            include_visualization=True
        )
        
        print("\n📊 分析结果:")
        print("-" * 50)
        print(result)
        print("-" * 50)
        
        if "OpenAI" in result or "GPT" in result or "AI分析" in result:
            print("✅ AI分析功能正常工作")
        else:
            print("⚠️ 可能使用了本地分析回退")
        
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {e}")
        print(f"🔍 错误类型: {type(e).__name__}")
        print(f"📍 错误详情: {repr(e)}")
        import traceback
        print(f"📋 完整错误信息:\n{traceback.format_exc()}")
        return False

async def main():
    """主函数"""
    success = await test_ai_analysis()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 AI分析功能测试完成")
        print("💡 现在可以启动Web界面进行完整测试")
    else:
        print("⚠️ AI分析功能测试失败")
        print("💡 请检查配置和依赖")

if __name__ == "__main__":
    asyncio.run(main())
