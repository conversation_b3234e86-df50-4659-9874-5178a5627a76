#!/usr/bin/env python3
"""
数据处理器 - 负责数据的处理、转换和缓存
"""

import json
import pickle
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Union
from pathlib import Path

from ..config import config
from ..utils.logger import log
from ..utils.formatter import DataFormatter

class DataCache:
    """数据缓存"""
    
    def __init__(self, cache_dir: str = None):
        self.cache_dir = Path(cache_dir or config.cache.get("dir", "cache"))
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.ttl = config.cache.get("ttl", 300)  # 5分钟
        self.max_size = config.cache.get("max_size", 1000)
        self.enabled = config.cache.get("enabled", True)
    
    def _get_cache_file(self, key: str) -> Path:
        """获取缓存文件路径"""
        return self.cache_dir / f"{key}.cache"
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        if not self.enabled:
            return None
        
        try:
            cache_file = self._get_cache_file(key)
            if not cache_file.exists():
                return None
            
            with open(cache_file, 'rb') as f:
                cache_data = pickle.load(f)
            
            # 检查是否过期
            if datetime.now() - cache_data['timestamp'] > timedelta(seconds=self.ttl):
                cache_file.unlink()
                return None
            
            return cache_data['data']
            
        except Exception as e:
            log.warning(f"读取缓存失败: {e}")
            return None
    
    def set(self, key: str, data: Any):
        """设置缓存数据"""
        if not self.enabled:
            return
        
        try:
            cache_file = self._get_cache_file(key)
            cache_data = {
                'data': data,
                'timestamp': datetime.now()
            }
            
            with open(cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
                
        except Exception as e:
            log.warning(f"写入缓存失败: {e}")
    
    def clear(self):
        """清空缓存"""
        try:
            for cache_file in self.cache_dir.glob("*.cache"):
                cache_file.unlink()
            log.info("缓存已清空")
        except Exception as e:
            log.error(f"清空缓存失败: {e}")

class DataHandler:
    """数据处理器"""
    
    def __init__(self, client):
        self.client = client
        self.cache = DataCache()
        self.formatter = DataFormatter()
    
    def process_raw_data(self, raw_data: Any) -> Dict[str, Any]:
        """
        处理原始数据
        
        Args:
            raw_data: 原始数据
            
        Returns:
            处理后的数据
        """
        try:
            if isinstance(raw_data, str):
                # 尝试解析JSON
                try:
                    data = json.loads(raw_data)
                except json.JSONDecodeError:
                    data = {"raw_text": raw_data}
            elif isinstance(raw_data, dict):
                data = raw_data.copy()
            elif isinstance(raw_data, list):
                data = {"items": raw_data}
            else:
                data = {"value": raw_data}
            
            # 添加处理时间戳
            data["processed_at"] = datetime.now().isoformat()
            
            return data
            
        except Exception as e:
            log.error(f"处理原始数据失败: {e}")
            return {"error": str(e), "raw_data": str(raw_data)}
    
    def format_statistical_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """格式化统计数据"""
        formatted = data.copy()
        
        # 格式化数值
        for key, value in data.items():
            if isinstance(value, (int, float)):
                if key.endswith("_count") or key == "count":
                    formatted[key] = int(value)
                else:
                    formatted[key] = self.formatter.format_number(value)
        
        return formatted
    
    def format_anomaly_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """格式化异常检测数据"""
        formatted = data.copy()
        
        # 格式化异常列表
        if "anomalies" in data and isinstance(data["anomalies"], list):
            formatted_anomalies = []
            for anomaly in data["anomalies"]:
                if isinstance(anomaly, dict):
                    formatted_anomaly = anomaly.copy()
                    # 格式化数值
                    for key in ["value", "threshold", "score"]:
                        if key in formatted_anomaly:
                            formatted_anomaly[key] = self.formatter.format_number(
                                formatted_anomaly[key]
                            )
                    formatted_anomalies.append(formatted_anomaly)
                else:
                    formatted_anomalies.append(str(anomaly))
            
            formatted["anomalies"] = formatted_anomalies
        
        return formatted
    
    def format_chart_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """格式化图表数据"""
        formatted = data.copy()
        
        # 确保图表数据结构正确
        if "chart_data" in data:
            chart_data = data["chart_data"]
            if isinstance(chart_data, dict):
                # 格式化数据点
                if "data" in chart_data and isinstance(chart_data["data"], list):
                    formatted_data = []
                    for point in chart_data["data"]:
                        if isinstance(point, dict):
                            formatted_point = {}
                            for key, value in point.items():
                                if isinstance(value, (int, float)):
                                    formatted_point[key] = self.formatter.format_number(value)
                                else:
                                    formatted_point[key] = value
                            formatted_data.append(formatted_point)
                        else:
                            formatted_data.append(point)
                    
                    formatted["chart_data"]["data"] = formatted_data
        
        return formatted
    
    def aggregate_data(self, data_list: List[Dict[str, Any]], 
                      group_by: str = None) -> Dict[str, Any]:
        """
        聚合数据
        
        Args:
            data_list: 数据列表
            group_by: 分组字段
            
        Returns:
            聚合结果
        """
        if not data_list:
            return {"total": 0, "groups": {}}
        
        result = {
            "total": len(data_list),
            "groups": {},
            "summary": {}
        }
        
        if group_by:
            # 按字段分组
            groups = {}
            for item in data_list:
                if isinstance(item, dict) and group_by in item:
                    group_key = str(item[group_by])
                    if group_key not in groups:
                        groups[group_key] = []
                    groups[group_key].append(item)
            
            result["groups"] = {
                key: {
                    "count": len(items),
                    "items": items
                }
                for key, items in groups.items()
            }
        
        return result
    
    def cache_data(self, key: str, data: Any, ttl: int = None):
        """缓存数据"""
        if ttl:
            original_ttl = self.cache.ttl
            self.cache.ttl = ttl
            self.cache.set(key, data)
            self.cache.ttl = original_ttl
        else:
            self.cache.set(key, data)
    
    def get_cached_data(self, key: str) -> Optional[Any]:
        """获取缓存数据"""
        return self.cache.get(key)
    
    def export_data(self, data: Dict[str, Any], 
                   format_type: str = "json",
                   filename: str = None) -> str:
        """
        导出数据
        
        Args:
            data: 要导出的数据
            format_type: 导出格式 (json, csv, txt)
            filename: 文件名
            
        Returns:
            导出文件路径
        """
        if not filename:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"export_{timestamp}.{format_type}"
        
        export_dir = Path("exports")
        export_dir.mkdir(exist_ok=True)
        export_path = export_dir / filename
        
        try:
            if format_type == "json":
                with open(export_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
            
            elif format_type == "txt":
                with open(export_path, 'w', encoding='utf-8') as f:
                    f.write(str(data))
            
            elif format_type == "csv":
                # 简单的CSV导出
                import csv
                with open(export_path, 'w', newline='', encoding='utf-8') as f:
                    if isinstance(data, dict) and "items" in data:
                        items = data["items"]
                        if items and isinstance(items[0], dict):
                            writer = csv.DictWriter(f, fieldnames=items[0].keys())
                            writer.writeheader()
                            writer.writerows(items)
                        else:
                            writer = csv.writer(f)
                            writer.writerow(["value"])
                            for item in items:
                                writer.writerow([item])
                    else:
                        writer = csv.writer(f)
                        writer.writerow(["data"])
                        writer.writerow([str(data)])
            
            log.info(f"数据已导出到: {export_path}")
            return str(export_path)
            
        except Exception as e:
            log.error(f"导出数据失败: {e}")
            raise
