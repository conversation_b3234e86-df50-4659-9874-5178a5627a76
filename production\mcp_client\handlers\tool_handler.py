#!/usr/bin/env python3
"""
工具处理器 - 负责解析查询意图和执行相应的工具调用
"""

import re
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from ..utils.logger import log

class QueryIntent:
    """查询意图类"""
    
    def __init__(self, intent_type: str, tool_name: str, parameters: Dict[str, Any]):
        self.intent_type = intent_type
        self.tool_name = tool_name
        self.parameters = parameters

class ToolHandler:
    """工具处理器"""
    
    def __init__(self, client):
        self.client = client
        
        # 意图识别模式
        self.intent_patterns = {
            # 系统状态查询
            "system_status": [
                r"系统状态|状态|健康|运行情况|服务状态",
                r"system.*status|health|running"
            ],
            
            # 统计分析
            "statistical_analysis": [
                r"统计|分析|平均|求和|最大|最小|计算",
                r"statistical?|analysis|average|sum|max|min|calculate"
            ],
            
            # 异常检测
            "anomaly_detection": [
                r"异常|检测|异常检测|故障|问题|错误",
                r"anomaly|detection|fault|error|problem"
            ],
            
            # 数据可视化
            "visualization": [
                r"图表|可视化|画图|绘制|显示图|折线图|柱状图",
                r"chart|graph|plot|visualization|line|bar"
            ],
            
            # 趋势分析
            "trend_analysis": [
                r"趋势|预测|变化|发展|走势",
                r"trend|forecast|prediction|change"
            ],
            
            # 提醒规则
            "alert_rule": [
                r"提醒|警报|规则|告警|通知",
                r"alert|alarm|rule|notification"
            ]
        }
        
        # 时间范围模式
        self.time_patterns = {
            r"(\d+)小时|(\d+)h": lambda m: f"{m.group(1) or m.group(2)}h",
            r"(\d+)天|(\d+)d": lambda m: f"{m.group(1) or m.group(2)}d",
            r"今天|today": lambda m: "24h",
            r"昨天|yesterday": lambda m: "24h",
            r"本周|this week": lambda m: "7d",
            r"上周|last week": lambda m: "7d",
            r"本月|this month": lambda m: "30d"
        }
        
        # 数据列映射
        self.column_mapping = {
            "温度": "temperature",
            "压力": "pressure", 
            "湿度": "humidity",
            "流量": "flow_rate",
            "电压": "voltage",
            "电流": "current",
            "功率": "power",
            "设备": "device_id",
            "位置": "location",
            "状态": "status"
        }
    
    async def parse_query_intent(self, query_text: str) -> QueryIntent:
        """
        解析查询意图
        
        Args:
            query_text: 查询文本
            
        Returns:
            QueryIntent: 解析后的意图
        """
        query_lower = query_text.lower()
        log.info(f"解析查询意图: {query_text}")
        
        # 识别意图类型
        intent_type = self._identify_intent_type(query_lower)
        
        # 提取参数
        parameters = self._extract_parameters(query_text, intent_type)
        
        # 确定工具名称
        tool_name = self._get_tool_name(intent_type)
        
        intent = QueryIntent(intent_type, tool_name, parameters)
        log.info(f"识别意图: {intent_type}, 工具: {tool_name}, 参数: {parameters}")
        
        return intent
    
    def _identify_intent_type(self, query_lower: str) -> str:
        """识别意图类型"""
        for intent_type, patterns in self.intent_patterns.items():
            for pattern in patterns:
                if re.search(pattern, query_lower):
                    return intent_type
        
        # 默认返回系统状态查询
        return "system_status"
    
    def _extract_parameters(self, query_text: str, intent_type: str) -> Dict[str, Any]:
        """提取参数"""
        parameters = {}
        
        # 提取时间范围
        time_range = self._extract_time_range(query_text)
        if time_range:
            parameters["time_range"] = time_range
            parameters["time_window"] = time_range
        
        # 提取数据列
        columns = self._extract_columns(query_text)
        if columns:
            parameters["columns"] = columns
            if len(columns) == 1:
                parameters["column"] = columns[0]
        
        # 根据意图类型添加特定参数
        if intent_type == "statistical_analysis":
            parameters.update(self._extract_statistical_params(query_text))
        elif intent_type == "anomaly_detection":
            parameters.update(self._extract_anomaly_params(query_text))
        elif intent_type == "visualization":
            parameters.update(self._extract_visualization_params(query_text))
        
        return parameters
    
    def _extract_time_range(self, query_text: str) -> Optional[str]:
        """提取时间范围"""
        for pattern, converter in self.time_patterns.items():
            match = re.search(pattern, query_text)
            if match:
                return converter(match)
        
        # 默认时间范围
        return "24h"
    
    def _extract_columns(self, query_text: str) -> List[str]:
        """提取数据列"""
        columns = []
        
        for chinese, english in self.column_mapping.items():
            if chinese in query_text:
                columns.append(english)
        
        # 如果没有找到列，返回默认列
        if not columns:
            columns = ["temperature"]
        
        return columns
    
    def _extract_statistical_params(self, query_text: str) -> Dict[str, Any]:
        """提取统计分析参数"""
        params = {}
        
        # 操作类型
        if any(word in query_text for word in ["平均", "均值", "average"]):
            params["operation"] = "average"
        elif any(word in query_text for word in ["求和", "总和", "sum"]):
            params["operation"] = "sum"
        elif any(word in query_text for word in ["最大", "max"]):
            params["operation"] = "max"
        elif any(word in query_text for word in ["最小", "min"]):
            params["operation"] = "min"
        elif any(word in query_text for word in ["计数", "count"]):
            params["operation"] = "count"
        else:
            params["operation"] = "average"
        
        # 时间范围
        now = datetime.now()
        params["end_time"] = now.strftime("%Y-%m-%d %H:%M:%S")
        
        time_range = params.get("time_range", "24h")
        if time_range.endswith("h"):
            hours = int(time_range[:-1])
            start_time = now - timedelta(hours=hours)
        elif time_range.endswith("d"):
            days = int(time_range[:-1])
            start_time = now - timedelta(days=days)
        else:
            start_time = now - timedelta(hours=24)
        
        params["start_time"] = start_time.strftime("%Y-%m-%d %H:%M:%S")
        
        return params
    
    def _extract_anomaly_params(self, query_text: str) -> Dict[str, Any]:
        """提取异常检测参数"""
        params = {}
        
        # 检测方法
        if "统计" in query_text:
            params["method"] = "statistical"
        elif "孤立森林" in query_text:
            params["method"] = "isolation_forest"
        elif "混合" in query_text:
            params["method"] = "hybrid"
        else:
            params["method"] = "hybrid"
        
        # 敏感度
        sensitivity_match = re.search(r"敏感度[：:]?(\d+\.?\d*)", query_text)
        if sensitivity_match:
            params["sensitivity"] = float(sensitivity_match.group(1))
        else:
            params["sensitivity"] = 2.0
        
        return params
    
    def _extract_visualization_params(self, query_text: str) -> Dict[str, Any]:
        """提取可视化参数"""
        params = {}
        
        # 图表类型
        if any(word in query_text for word in ["折线图", "线图", "line"]):
            params["chart_type"] = "line"
        elif any(word in query_text for word in ["柱状图", "条形图", "bar"]):
            params["chart_type"] = "bar"
        elif any(word in query_text for word in ["饼图", "pie"]):
            params["chart_type"] = "pie"
        elif any(word in query_text for word in ["散点图", "scatter"]):
            params["chart_type"] = "scatter"
        else:
            params["chart_type"] = "line"
        
        # 图表标题
        title_match = re.search(r"标题[：:]?(.+?)(?:[，。]|$)", query_text)
        if title_match:
            params["title"] = title_match.group(1).strip()
        else:
            params["title"] = "数据图表"
        
        return params
    
    def _get_tool_name(self, intent_type: str) -> str:
        """获取工具名称"""
        tool_mapping = {
            "system_status": "get_system_status",
            "statistical_analysis": "advanced_statistical_analysis",
            "anomaly_detection": "intelligent_anomaly_detection",
            "visualization": "generate_advanced_chart",
            "trend_analysis": "advanced_trend_analysis",
            "alert_rule": "create_alert_rule"
        }
        
        return tool_mapping.get(intent_type, "get_system_status")
    
    async def execute_intent(self, intent: QueryIntent) -> Dict[str, Any]:
        """
        执行意图
        
        Args:
            intent: 查询意图
            
        Returns:
            执行结果
        """
        try:
            log.info(f"执行意图: {intent.intent_type}")
            
            # 调用相应的工具
            result = await self.client.call_tool(intent.tool_name, intent.parameters)
            
            # 后处理结果
            processed_result = self._post_process_result(intent, result)
            
            return processed_result
            
        except Exception as e:
            log.error(f"执行意图失败: {e}")
            return {
                "error": str(e),
                "intent_type": intent.intent_type,
                "tool_name": intent.tool_name
            }
    
    def _post_process_result(self, intent: QueryIntent, result: Dict[str, Any]) -> Dict[str, Any]:
        """后处理结果"""
        processed = {
            "intent_type": intent.intent_type,
            "tool_name": intent.tool_name,
            "parameters": intent.parameters,
            "result": result,
            "timestamp": datetime.now().isoformat()
        }
        
        # 添加友好的描述
        if intent.intent_type == "system_status":
            processed["description"] = "系统状态查询结果"
        elif intent.intent_type == "statistical_analysis":
            processed["description"] = f"统计分析结果 - {intent.parameters.get('operation', '未知操作')}"
        elif intent.intent_type == "anomaly_detection":
            processed["description"] = f"异常检测结果 - {intent.parameters.get('method', '未知方法')}"
        elif intent.intent_type == "visualization":
            processed["description"] = f"数据可视化结果 - {intent.parameters.get('chart_type', '未知图表')}"
        
        return processed
