#!/usr/bin/env python3
"""
简单测试脚本 - 验证数据库连接和基本功能
"""

import asyncio
import os
from datetime import datetime, timedelta

# 设置环境变量
os.environ.update({
    "DB_HOST": "localhost",
    "DB_PORT": "3306", 
    "DB_USER": "root",
    "DB_PASSWORD": "12369874b",
    "DB_NAME": "sensor_data",
    "DB_CHARSET": "utf8mb4"
})

# 导入服务器模块
from database_analysis_server_simple import (
    statistical_analysis,
    anomaly_detection,
    get_table_info
)

async def test_basic_functions():
    """测试基本功能"""
    print("开始测试数据库分析服务器...")
    
    try:
        # 1. 测试数据库连接
        print("\n1. 测试数据库连接...")
        table_info = await get_table_info()
        
        if "error" in table_info:
            print(f"   数据库连接失败: {table_info['error']}")
            return False
        else:
            print(f"   数据库连接成功!")
            print(f"   数据表: {table_info['table_name']}")
            print(f"   数据行数: {table_info['total_rows']}")
            print(f"   列数: {len(table_info['columns'])}")
        
        # 2. 测试统计分析
        print("\n2. 测试统计分析...")
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=24)
        
        stats = await statistical_analysis(
            start_time=start_time.strftime('%Y-%m-%d %H:%M:%S'),
            end_time=end_time.strftime('%Y-%m-%d %H:%M:%S'),
            columns=["temperature"],
            operation="average"
        )
        
        if stats:
            temp_avg = stats[0]
            print(f"   温度平均值: {temp_avg.result:.2f}°C")
            print(f"   数据点数: {temp_avg.count}")
        else:
            print("   统计分析无结果")
        
        # 3. 测试异常检测
        print("\n3. 测试异常检测...")
        anomaly_result = await anomaly_detection(
            column="temperature",
            threshold_type="statistical",
            time_window="24h",
            sensitivity=2.0
        )
        
        print(f"   检测数据点: {anomaly_result.total_points}")
        print(f"   异常点数量: {anomaly_result.anomaly_count}")
        print(f"   异常率: {anomaly_result.anomaly_rate:.2%}")
        
        print("\n所有测试完成!")
        return True
        
    except Exception as e:
        print(f"测试过程中出错: {str(e)}")
        return False

if __name__ == "__main__":
    success = asyncio.run(test_basic_functions())
    if success:
        print("\n✅ 服务器功能正常，可以安装到Claude Desktop!")
        print("\n📋 下一步:")
        print("1. 重启Claude Desktop")
        print("2. 在Claude Desktop中测试MCP工具")
        print("3. 尝试命令: '分析过去24小时的温度数据'")
    else:
        print("\n❌ 服务器功能测试失败，请检查配置")
