#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel数据导入MySQL脚本
"""

import pandas as pd
import pymysql
import numpy as np
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def read_excel_file(file_path):
    """读取Excel文件"""
    try:
        # 尝试读取Excel文件
        logger.info(f"正在读取Excel文件: {file_path}")
        
        # 读取所有工作表
        excel_file = pd.ExcelFile(file_path)
        logger.info(f"发现工作表: {excel_file.sheet_names}")
        
        # 读取第一个工作表
        df = pd.read_excel(file_path, sheet_name=0)
        logger.info(f"数据形状: {df.shape}")
        logger.info(f"列名: {list(df.columns)}")
        
        # 显示前几行数据
        logger.info("前5行数据:")
        print(df.head())
        
        # 显示数据类型
        logger.info("数据类型:")
        print(df.dtypes)
        
        # 检查缺失值
        logger.info("缺失值统计:")
        print(df.isnull().sum())
        
        return df
        
    except Exception as e:
        logger.error(f"读取Excel文件失败: {e}")
        return None

def analyze_data_structure(df):
    """分析数据结构"""
    logger.info("=== 数据结构分析 ===")
    
    # 基本信息
    logger.info(f"总行数: {len(df)}")
    logger.info(f"总列数: {len(df.columns)}")
    
    # 列信息
    for col in df.columns:
        dtype = df[col].dtype
        null_count = df[col].isnull().sum()
        unique_count = df[col].nunique()
        
        logger.info(f"列 '{col}': 类型={dtype}, 缺失值={null_count}, 唯一值={unique_count}")
        
        # 显示一些示例值
        sample_values = df[col].dropna().head(3).tolist()
        logger.info(f"  示例值: {sample_values}")

def create_mysql_table_sql(df, table_name="sensor_data"):
    """根据DataFrame生成MySQL建表SQL"""
    logger.info("=== 生成MySQL建表SQL ===")
    
    sql_parts = [f"CREATE TABLE IF NOT EXISTS `{table_name}` ("]
    sql_parts.append("  `id` INT AUTO_INCREMENT PRIMARY KEY,")
    
    for col in df.columns:
        # 清理列名（移除特殊字符）
        clean_col = col.replace(' ', '_').replace('(', '').replace(')', '').replace('/', '_')
        
        # 根据数据类型确定MySQL字段类型
        dtype = df[col].dtype
        
        if pd.api.types.is_integer_dtype(dtype):
            mysql_type = "INT"
        elif pd.api.types.is_float_dtype(dtype):
            mysql_type = "DECIMAL(10,2)"
        elif pd.api.types.is_datetime64_any_dtype(dtype):
            mysql_type = "DATETIME"
        else:
            # 字符串类型，检查最大长度
            max_length = df[col].astype(str).str.len().max()
            if pd.isna(max_length) or max_length <= 255:
                mysql_type = "VARCHAR(255)"
            else:
                mysql_type = "TEXT"
        
        sql_parts.append(f"  `{clean_col}` {mysql_type},")
    
    # 添加时间戳
    sql_parts.append("  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,")
    sql_parts.append("  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP")
    sql_parts.append(") ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;")
    
    sql = "\n".join(sql_parts)
    logger.info("建表SQL:")
    print(sql)
    
    return sql

def main():
    """主函数"""
    excel_file_path = "数据库.xls"
    
    # 读取Excel文件
    df = read_excel_file(excel_file_path)
    if df is None:
        return
    
    # 分析数据结构
    analyze_data_structure(df)
    
    # 生成建表SQL
    create_table_sql = create_mysql_table_sql(df)
    
    # 保存SQL到文件
    with open("create_table.sql", "w", encoding="utf-8") as f:
        f.write(create_table_sql)
    
    logger.info("建表SQL已保存到 create_table.sql")
    
    # 保存清理后的数据到CSV（用于后续导入）
    df.to_csv("cleaned_data.csv", index=False, encoding="utf-8")
    logger.info("清理后的数据已保存到 cleaned_data.csv")

if __name__ == "__main__":
    main()
