#!/usr/bin/env python3
"""
测试LLM配置脚本
验证OpenAI API配置是否正确
"""

import os
import sys
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

def test_env_config():
    """测试环境变量配置"""
    print("🔧 检查环境变量配置...")
    
    api_key = os.getenv("OPENAI_API_KEY", "")
    model = os.getenv("OPENAI_MODEL", "")
    
    print(f"   API密钥: {'已配置' if api_key else '❌ 未配置'}")
    print(f"   模型: {model if model else '❌ 未配置'}")
    print(f"   最大令牌: {os.getenv('AI_MAX_TOKENS', '未配置')}")
    print(f"   温度: {os.getenv('AI_TEMPERATURE', '未配置')}")
    
    return bool(api_key and model)

def test_openai_import():
    """测试OpenAI库导入"""
    print("\n📦 检查OpenAI库...")
    try:
        import openai
        from openai import OpenAI
        print("   ✅ OpenAI库导入成功")
        return True
    except ImportError as e:
        print(f"   ❌ OpenAI库导入失败: {e}")
        print("   💡 请运行: pip install openai")
        return False

def test_openai_connection():
    """测试OpenAI API连接"""
    print("\n🌐 测试OpenAI API连接...")
    
    try:
        from openai import OpenAI
        
        api_key = os.getenv("OPENAI_API_KEY", "")
        model = os.getenv("OPENAI_MODEL", "gpt-4o-mini")
        
        if not api_key:
            print("   ❌ API密钥未配置")
            return False
        
        client = OpenAI(api_key=api_key)
        
        # 简单的测试调用
        response = client.chat.completions.create(
            model=model,
            messages=[{"role": "user", "content": "Hello, this is a test."}],
            max_tokens=10,
            temperature=0
        )
        
        print(f"   ✅ API连接成功")
        print(f"   🤖 使用模型: {model}")
        print(f"   📝 测试响应: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"   ❌ API连接失败: {e}")
        return False

def test_mcp_server_config():
    """测试MCP服务器配置"""
    print("\n🔧 检查MCP服务器配置...")
    
    try:
        # 导入配置
        sys.path.append('mcp_client')
        from config import config
        
        # 检查LLM环境变量是否传递给MCP服务器
        server_config = config.get_server_config("enterprise_db")
        if server_config:
            env_vars = server_config.env_vars
            llm_vars = [
                "OPENAI_API_KEY",
                "OPENAI_MODEL", 
                "AI_MAX_TOKENS",
                "AI_TEMPERATURE"
            ]
            
            print("   MCP服务器环境变量:")
            for var in llm_vars:
                value = env_vars.get(var, "未配置")
                if var == "OPENAI_API_KEY" and value:
                    value = f"{'*' * (len(value) - 8)}{value[-8:]}"  # 隐藏大部分密钥
                print(f"     {var}: {value}")
            
            return all(env_vars.get(var) for var in llm_vars)
        else:
            print("   ❌ 未找到enterprise_db服务器配置")
            return False
            
    except Exception as e:
        print(f"   ❌ 配置检查失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 LLM配置测试开始")
    print("=" * 50)
    
    tests = [
        ("环境变量配置", test_env_config),
        ("OpenAI库导入", test_openai_import),
        ("OpenAI API连接", test_openai_connection),
        ("MCP服务器配置", test_mcp_server_config)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ 测试异常: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    all_passed = True
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if not result:
            all_passed = False
    
    print("\n" + "=" * 50)
    if all_passed:
        print("🎉 所有测试通过！LLM配置正确")
        print("💡 现在可以启动Web界面测试AI功能:")
        print("   streamlit run mcp_client/enterprise_ai_frontend.py --server.port=8501")
    else:
        print("⚠️ 部分测试失败，请检查配置")
        print("💡 请确保:")
        print("   1. OpenAI API密钥正确配置")
        print("   2. 网络连接正常")
        print("   3. 已安装openai库: pip install openai")

if __name__ == "__main__":
    main()
