#!/usr/bin/env python3
"""
响应处理器 - 负责处理MCP工具的响应结果
"""

from typing import Dict, Any, Optional
from datetime import datetime

from ..utils.logger import log
from ..utils.formatter import format_data

class ResponseHandler:
    """响应处理器"""
    
    def __init__(self, client):
        self.client = client
    
    async def process_tool_result(self, tool_name: str, result: Any) -> Dict[str, Any]:
        """
        处理工具执行结果
        
        Args:
            tool_name: 工具名称
            result: 工具执行结果
            
        Returns:
            处理后的结果
        """
        try:
            log.info(f"处理工具结果: {tool_name}")
            
            # 基础结果结构
            processed_result = {
                "tool_name": tool_name,
                "timestamp": datetime.now().isoformat(),
                "success": True,
                "data": None,
                "metadata": {}
            }
            
            # 处理不同类型的结果
            if hasattr(result, 'data') and result.data is not None:
                # FastMCP结构化数据
                processed_result["data"] = result.data
                processed_result["metadata"]["has_structured_data"] = True
            elif hasattr(result, 'content') and result.content:
                # 内容数据
                processed_result["data"] = self._process_content(result.content)
                processed_result["metadata"]["has_content"] = True
            else:
                # 原始结果
                processed_result["data"] = result
                processed_result["metadata"]["raw_result"] = True
            
            # 根据工具类型进行特殊处理
            processed_result = await self._process_by_tool_type(tool_name, processed_result)
            
            return processed_result
            
        except Exception as e:
            log.error(f"处理工具结果失败: {tool_name}, 错误: {e}")
            return {
                "tool_name": tool_name,
                "timestamp": datetime.now().isoformat(),
                "success": False,
                "error": str(e),
                "data": None
            }
    
    def _process_content(self, content: Any) -> Any:
        """处理内容数据"""
        if isinstance(content, list):
            processed_content = []
            for item in content:
                if hasattr(item, 'text'):
                    processed_content.append(item.text)
                elif hasattr(item, 'data'):
                    processed_content.append(item.data)
                else:
                    processed_content.append(str(item))
            return processed_content
        else:
            return content
    
    async def _process_by_tool_type(self, tool_name: str, result: Dict[str, Any]) -> Dict[str, Any]:
        """根据工具类型进行特殊处理"""
        
        if tool_name == "get_system_status":
            return self._process_system_status(result)
        
        elif tool_name == "advanced_statistical_analysis":
            return self._process_statistical_analysis(result)
        
        elif tool_name == "intelligent_anomaly_detection":
            return self._process_anomaly_detection(result)
        
        elif tool_name == "generate_advanced_chart":
            return self._process_chart_generation(result)
        
        elif tool_name == "advanced_trend_analysis":
            return self._process_trend_analysis(result)
        
        elif tool_name == "create_alert_rule":
            return self._process_alert_rule(result)
        
        else:
            # 默认处理
            return self._process_default(result)
    
    def _process_system_status(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """处理系统状态结果"""
        result["metadata"]["result_type"] = "system_status"
        
        if result["data"] and isinstance(result["data"], dict):
            data = result["data"]
            
            # 添加状态摘要
            result["summary"] = {
                "database_healthy": data.get("database_status") == "healthy",
                "total_rows": data.get("total_data_rows", 0),
                "connection_pool": data.get("connection_pool_size", 0),
                "uptime": data.get("uptime", "unknown")
            }
            
            # 格式化数值
            if "total_data_rows" in data:
                result["summary"]["total_rows_formatted"] = f"{data['total_data_rows']:,}"
        
        return result
    
    def _process_statistical_analysis(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """处理统计分析结果"""
        result["metadata"]["result_type"] = "statistical_analysis"
        
        if result["data"] and isinstance(result["data"], dict):
            data = result["data"]
            
            # 添加统计摘要
            result["summary"] = {
                "operation": data.get("operation", "unknown"),
                "columns_analyzed": data.get("columns", []),
                "time_range": data.get("time_range", "unknown"),
                "data_points": data.get("data_points", 0)
            }
            
            # 提取关键指标
            if "results" in data:
                results = data["results"]
                if isinstance(results, dict):
                    result["key_metrics"] = {
                        key: value for key, value in results.items()
                        if isinstance(value, (int, float))
                    }
        
        return result
    
    def _process_anomaly_detection(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """处理异常检测结果"""
        result["metadata"]["result_type"] = "anomaly_detection"
        
        if result["data"] and isinstance(result["data"], dict):
            data = result["data"]
            
            # 添加异常摘要
            anomalies = data.get("anomalies", [])
            result["summary"] = {
                "method": data.get("method", "unknown"),
                "column": data.get("column", "unknown"),
                "total_anomalies": len(anomalies) if isinstance(anomalies, list) else 0,
                "severity_levels": self._analyze_anomaly_severity(anomalies)
            }
            
            # 分类异常
            if isinstance(anomalies, list):
                result["anomaly_categories"] = self._categorize_anomalies(anomalies)
        
        return result
    
    def _process_chart_generation(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """处理图表生成结果"""
        result["metadata"]["result_type"] = "chart_generation"
        
        if result["data"] and isinstance(result["data"], dict):
            data = result["data"]
            
            # 添加图表摘要
            result["summary"] = {
                "chart_type": data.get("chart_type", "unknown"),
                "title": data.get("title", ""),
                "columns": data.get("columns", []),
                "data_points": len(data.get("chart_data", {}).get("data", [])) if "chart_data" in data else 0
            }
            
            # 检查图表数据完整性
            if "chart_data" in data:
                chart_data = data["chart_data"]
                result["metadata"]["chart_ready"] = bool(
                    chart_data and 
                    isinstance(chart_data, dict) and 
                    "data" in chart_data
                )
        
        return result
    
    def _process_trend_analysis(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """处理趋势分析结果"""
        result["metadata"]["result_type"] = "trend_analysis"
        
        if result["data"] and isinstance(result["data"], dict):
            data = result["data"]
            
            # 添加趋势摘要
            result["summary"] = {
                "trend_direction": data.get("trend_direction", "unknown"),
                "confidence": data.get("confidence", 0),
                "prediction_horizon": data.get("prediction_horizon", "unknown")
            }
        
        return result
    
    def _process_alert_rule(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """处理提醒规则结果"""
        result["metadata"]["result_type"] = "alert_rule"
        
        if result["data"] and isinstance(result["data"], dict):
            data = result["data"]
            
            # 添加规则摘要
            result["summary"] = {
                "rule_id": data.get("rule_id", "unknown"),
                "rule_name": data.get("name", "unknown"),
                "status": data.get("status", "unknown"),
                "created": data.get("created", False)
            }
        
        return result
    
    def _process_default(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """默认处理"""
        result["metadata"]["result_type"] = "default"
        
        # 添加基本摘要
        if result["data"]:
            if isinstance(result["data"], dict):
                result["summary"] = {
                    "keys": list(result["data"].keys()),
                    "data_type": "object"
                }
            elif isinstance(result["data"], list):
                result["summary"] = {
                    "length": len(result["data"]),
                    "data_type": "array"
                }
            else:
                result["summary"] = {
                    "value": str(result["data"])[:100],
                    "data_type": type(result["data"]).__name__
                }
        
        return result
    
    def _analyze_anomaly_severity(self, anomalies: list) -> Dict[str, int]:
        """分析异常严重程度"""
        severity_levels = {"low": 0, "medium": 0, "high": 0, "critical": 0}
        
        for anomaly in anomalies:
            if isinstance(anomaly, dict) and "severity" in anomaly:
                severity = anomaly["severity"].lower()
                if severity in severity_levels:
                    severity_levels[severity] += 1
        
        return severity_levels
    
    def _categorize_anomalies(self, anomalies: list) -> Dict[str, list]:
        """分类异常"""
        categories = {
            "high_severity": [],
            "recent": [],
            "recurring": []
        }
        
        for anomaly in anomalies:
            if isinstance(anomaly, dict):
                # 高严重程度
                if anomaly.get("severity", "").lower() in ["high", "critical"]:
                    categories["high_severity"].append(anomaly)
                
                # 最近发生的
                if "timestamp" in anomaly:
                    # 这里可以添加时间判断逻辑
                    categories["recent"].append(anomaly)
        
        return categories
