#!/usr/bin/env python3
"""
第1步：启动MCP HTTP服务器
基于FastMCP文档的HTTP传输方式
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

def start_http_server():
    """启动HTTP服务器"""
    print("🚀 第1步：启动MCP HTTP服务器")
    print("=" * 50)
    
    # 服务器文件路径
    server_path = Path(__file__).parent.parent / "enterprise_database_mcp_server.py"
    
    if not server_path.exists():
        print(f"❌ 服务器文件不存在: {server_path}")
        return False
    
    print(f"📁 服务器文件: {server_path}")
    
    # 设置环境变量
    env_vars = {
        "DB_HOST": "localhost",
        "DB_PORT": "3306",
        "DB_USER": "root",
        "DB_PASSWORD": "123456", 
        "DB_NAME": "sensor_data",
        "DB_CHARSET": "utf8mb4",
        "PYTHONIOENCODING": "utf-8"
    }
    
    print("\n🌍 设置环境变量:")
    for key, value in env_vars.items():
        os.environ[key] = value
        print(f"  {key} = {value}")
    
    print("\n🔧 修改服务器以使用HTTP传输...")
    
    # 读取服务器文件
    with open(server_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 根据FastMCP文档，使用fastmcp run命令启动HTTP服务器
    print("✅ 使用FastMCP CLI启动HTTP服务器")

    # 创建启动命令
    http_server_script = None  # 不需要临时脚本
        
        http_content = f'''#!/usr/bin/env python3
"""
临时HTTP服务器启动脚本
"""

import sys
import os
from pathlib import Path

# 设置环境变量
env_vars = {env_vars}

for key, value in env_vars.items():
    os.environ[key] = value

# 导入原始服务器
sys.path.append(str(Path(__file__).parent))

# 导入服务器模块
import importlib.util
spec = importlib.util.spec_from_file_location("server", "{server_path}")
server_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(server_module)

# 获取MCP实例
mcp = server_module.mcp

if __name__ == "__main__":
    print("🌐 启动HTTP服务器...")
    print("📍 地址: http://127.0.0.1:8000/mcp/")
    print("⏹️ 按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    try:
        # 使用HTTP传输启动服务器
        mcp.run(
            transport="http",
            host="127.0.0.1", 
            port=8000,
            log_level="INFO"
        )
    except KeyboardInterrupt:
        print("\\n👋 服务器已停止")
    except Exception as e:
        print(f"\\n❌ 服务器启动失败: {{e}}")
'''
        
        with open(http_server_script, 'w', encoding='utf-8') as f:
            f.write(http_content)
        
        print(f"✅ 创建HTTP服务器脚本: {http_server_script}")
    
    print("\n🚀 启动HTTP服务器...")
    print("📍 服务器地址: http://127.0.0.1:8000/mcp/")
    print("⏹️ 按 Ctrl+C 停止服务器")
    print("🔄 服务器启动后，请在新终端运行第2步")
    print("-" * 50)

    try:
        # 根据FastMCP文档，使用fastmcp run命令启动HTTP服务器
        import subprocess

        # 构建fastmcp run命令
        cmd = [
            sys.executable, "-m", "fastmcp", "run",
            str(server_path),
            "--transport", "http",
            "--host", "127.0.0.1",
            "--port", "8000"
        ]

        print(f"🔧 执行命令: {' '.join(cmd)}")

        # 设置环境变量并运行
        env = os.environ.copy()
        env.update(env_vars)

        subprocess.run(cmd, env=env)

    except KeyboardInterrupt:
        print("\n👋 服务器已停止")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        print("💡 尝试备用方法...")
        return start_server_alternative(server_path, env_vars)
    
    return True

def start_server_alternative(server_path, env_vars):
    """备用启动方法 - 直接修改服务器文件"""
    print("\n🔄 使用备用启动方法...")

    try:
        # 读取服务器文件
        with open(server_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 创建临时HTTP启动脚本
        temp_script = Path(__file__).parent.parent / "temp_http_server.py"

        temp_content = f'''#!/usr/bin/env python3
"""
临时HTTP服务器启动脚本
"""
import sys
import os
from pathlib import Path

# 设置环境变量
env_vars = {env_vars}
for key, value in env_vars.items():
    os.environ[key] = value

# 导入原始服务器
sys.path.append(str(Path(__file__).parent))

# 动态导入服务器模块
import importlib.util
spec = importlib.util.spec_from_file_location("server", "{server_path}")
server_module = importlib.util.module_from_spec(spec)
spec.loader.exec_module(server_module)

# 获取MCP实例
mcp = server_module.mcp

if __name__ == "__main__":
    print("🌐 启动HTTP服务器...")
    print("📍 地址: http://127.0.0.1:8000/mcp/")
    print("⏹️ 按 Ctrl+C 停止服务器")
    print("-" * 50)

    try:
        # 使用HTTP传输启动服务器
        mcp.run(
            transport="http",
            host="127.0.0.1",
            port=8000,
            log_level="INFO"
        )
    except KeyboardInterrupt:
        print("\\n👋 服务器已停止")
    except Exception as e:
        print(f"\\n❌ 服务器启动失败: {{e}}")
        import traceback
        traceback.print_exc()
'''

        # 写入临时脚本
        with open(temp_script, 'w', encoding='utf-8') as f:
            f.write(temp_content)

        print(f"✅ 创建临时启动脚本: {temp_script}")

        # 运行临时脚本
        import subprocess
        subprocess.run([sys.executable, str(temp_script)])

        return True

    except Exception as e:
        print(f"❌ 备用方法也失败: {e}")
        return False

def main():
    """主函数"""
    try:
        start_http_server()
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False

if __name__ == "__main__":
    main()
