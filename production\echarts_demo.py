#!/usr/bin/env python3
"""
ECharts 图表演示脚本
生成各种类型的图表并保存为 HTML 文件
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from echarts_utils import EChartsConfigGenerator, create_echarts_html
import os

def generate_sample_data():
    """生成示例数据"""
    dates = pd.date_range(start='2024-01-01', periods=100, freq='h')
    data = {
        'timestamp': dates,
        'temperature': 20 + 10 * np.sin(np.arange(100) * 0.1) + np.random.normal(0, 2, 100),
        'humidity': 50 + 20 * np.cos(np.arange(100) * 0.15) + np.random.normal(0, 5, 100),
        'pressure': 1013 + 10 * np.sin(np.arange(100) * 0.05) + np.random.normal(0, 3, 100),
        'voltage': 220 + 5 * np.sin(np.arange(100) * 0.2) + np.random.normal(0, 1, 100),
        'current': 10 + 2 * np.cos(np.arange(100) * 0.12) + np.random.normal(0, 0.5, 100)
    }
    return pd.DataFrame(data)

def create_demo_charts():
    """创建演示图表"""
    print("🎨 开始生成 ECharts 演示图表...")
    
    # 生成数据
    df = generate_sample_data()
    generator = EChartsConfigGenerator()
    
    # 创建输出目录
    output_dir = "echarts_demo_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # 图表配置
    chart_configs = [
        {
            "type": "line",
            "columns": ["temperature", "humidity"],
            "title": "温湿度时间序列图",
            "filename": "line_chart.html"
        },
        {
            "type": "bar",
            "columns": ["temperature", "humidity", "pressure"],
            "title": "传感器数据柱状图",
            "filename": "bar_chart.html"
        },
        {
            "type": "pie",
            "columns": ["temperature", "humidity", "pressure", "voltage", "current"],
            "title": "传感器数据分布饼图",
            "filename": "pie_chart.html"
        },
        {
            "type": "scatter",
            "columns": ["temperature", "humidity"],
            "title": "温度与湿度散点图",
            "filename": "scatter_chart.html"
        },
        {
            "type": "heatmap",
            "columns": ["temperature", "humidity", "pressure", "voltage"],
            "title": "传感器数据相关性热力图",
            "filename": "heatmap_chart.html"
        },
        {
            "type": "histogram",
            "columns": ["temperature"],
            "title": "温度分布直方图",
            "filename": "histogram_chart.html"
        },
        {
            "type": "box",
            "columns": ["temperature", "humidity", "pressure"],
            "title": "传感器数据箱线图",
            "filename": "box_chart.html"
        }
    ]
    
    # 生成图表
    for config in chart_configs:
        try:
            print(f"  📊 生成 {config['title']}...")
            
            # 生成 ECharts 配置
            echarts_config = generator.generate_config(
                chart_type=config["type"],
                df=df,
                columns=config["columns"],
                title=config["title"]
            )
            
            # 生成 HTML
            html = create_echarts_html(echarts_config, width="100%", height="600px")
            
            # 保存文件
            filepath = os.path.join(output_dir, config["filename"])
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(html)
            
            print(f"    ✅ 已保存到: {filepath}")
            
        except Exception as e:
            print(f"    ❌ 生成失败: {str(e)}")
    
    # 创建索引页面
    create_index_page(output_dir, chart_configs)
    
    print(f"🎉 演示图表生成完成！请查看 {output_dir} 目录")

def create_index_page(output_dir, chart_configs):
    """创建索引页面"""
    html_content = """
    <!DOCTYPE html>
    <html lang="zh-CN">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>ECharts 演示图表</title>
        <style>
            body {
                font-family: Arial, sans-serif;
                margin: 0;
                padding: 20px;
                background-color: #f5f5f5;
            }
            .container {
                max-width: 1200px;
                margin: 0 auto;
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            h1 {
                color: #333;
                text-align: center;
                margin-bottom: 30px;
            }
            .chart-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-top: 30px;
            }
            .chart-card {
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 20px;
                background-color: #fafafa;
                transition: transform 0.2s;
            }
            .chart-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            }
            .chart-card h3 {
                margin-top: 0;
                color: #555;
            }
            .chart-card a {
                display: inline-block;
                background-color: #007bff;
                color: white;
                padding: 10px 20px;
                text-decoration: none;
                border-radius: 5px;
                margin-top: 10px;
            }
            .chart-card a:hover {
                background-color: #0056b3;
            }
            .info {
                background-color: #e7f3ff;
                border-left: 4px solid #007bff;
                padding: 15px;
                margin-bottom: 20px;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🎨 ECharts 演示图表集合</h1>
            
            <div class="info">
                <strong>📋 说明：</strong>这是使用 ECharts 替代 Plotly/Matplotlib 后生成的演示图表。
                所有图表都是基于模拟的传感器数据生成的，展示了不同类型图表的渲染效果。
            </div>
            
            <div class="chart-grid">
    """
    
    # 添加图表卡片
    for config in chart_configs:
        html_content += f"""
                <div class="chart-card">
                    <h3>📊 {config['title']}</h3>
                    <p><strong>类型：</strong>{config['type'].title()}</p>
                    <p><strong>数据列：</strong>{', '.join(config['columns'])}</p>
                    <a href="{config['filename']}" target="_blank">查看图表</a>
                </div>
        """
    
    html_content += """
            </div>
            
            <div style="margin-top: 40px; text-align: center; color: #666;">
                <p>🚀 由 ECharts 强力驱动 | 替代 Plotly/Matplotlib 的现代化图表解决方案</p>
            </div>
        </div>
    </body>
    </html>
    """
    
    # 保存索引页面
    index_path = os.path.join(output_dir, "index.html")
    with open(index_path, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"  📄 索引页面已保存到: {index_path}")

def main():
    """主函数"""
    print("🚀 ECharts 图表演示程序")
    print("=" * 50)
    
    try:
        create_demo_charts()
        print("\n✨ 演示完成！请打开 echarts_demo_output/index.html 查看所有图表")
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
