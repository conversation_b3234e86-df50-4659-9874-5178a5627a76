# 本地LLM部署配置指南

## 🎯 为什么选择本地LLM？

基于您客户的需求：
- ✅ **完全本地化** - 数据不出本地，确保安全
- ✅ **无需互联网** - 离线运行，不依赖外部服务
- ✅ **成本控制** - 一次部署，长期使用
- ✅ **定制化** - 可针对特定领域优化
- ✅ **实时响应** - 本地处理，低延迟

## 🏗️ 推荐架构方案

### 方案1：Ollama + Open WebUI（强烈推荐）

**优势：**
- 安装简单，配置方便
- 支持多种开源大模型
- 优秀的Web界面
- 原生支持MCP协议
- 中文支持良好

**适用场景：**
- 企业内部部署
- 需要Web界面交互
- 多用户访问

### 方案2：本地API服务

**优势：**
- 更高的定制化程度
- 可集成到现有系统
- 支持RESTful API

**适用场景：**
- 需要API集成
- 自定义界面开发

## 🚀 Ollama + Open WebUI 详细部署

### 第一步：安装Ollama

#### Windows安装
```powershell
# 方法1：使用winget
winget install Ollama.Ollama

# 方法2：手动下载
# 访问 https://ollama.ai/download/windows
# 下载并安装 OllamaSetup.exe
```

#### Linux安装
```bash
# 官方安装脚本
curl -fsSL https://ollama.ai/install.sh | sh

# 或者手动安装
wget https://github.com/ollama/ollama/releases/latest/download/ollama-linux-amd64
sudo mv ollama-linux-amd64 /usr/local/bin/ollama
sudo chmod +x /usr/local/bin/ollama
```

#### macOS安装
```bash
# 使用Homebrew
brew install ollama

# 或下载安装包
# 访问 https://ollama.ai/download/mac
```

### 第二步：下载和配置中文大模型

#### 推荐模型列表

1. **Qwen2.5系列（阿里通义千问）**
   ```bash
   # 14B模型 - 最佳性能（需要16GB内存）
   ollama pull qwen2.5:14b
   
   # 7B模型 - 平衡性能（需要8GB内存）
   ollama pull qwen2.5:7b
   
   # 3B模型 - 轻量级（需要4GB内存）
   ollama pull qwen2.5:3b
   ```

2. **ChatGLM3系列（清华智谱）**
   ```bash
   # 6B模型 - 对话优化
   ollama pull chatglm3:6b
   ```

3. **Baichuan2系列（百川智能）**
   ```bash
   # 7B模型 - 中文优化
   ollama pull baichuan2:7b
   ```

4. **Yi系列（零一万物）**
   ```bash
   # 6B模型 - 性能均衡
   ollama pull yi:6b
   ```

#### 模型性能对比

| 模型 | 参数量 | 内存需求 | 中文能力 | 推理速度 | 推荐场景 |
|------|--------|----------|----------|----------|----------|
| qwen2.5:14b | 14B | 16GB | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | 高精度分析 |
| qwen2.5:7b | 7B | 8GB | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 生产环境 |
| qwen2.5:3b | 3B | 4GB | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 轻量部署 |
| chatglm3:6b | 6B | 8GB | ⭐⭐⭐⭐ | ⭐⭐⭐⭐ | 对话交互 |

### 第三步：安装Open WebUI

#### 使用Docker（推荐）
```bash
# 拉取并运行Open WebUI
docker run -d \
  --name open-webui \
  -p 3000:8080 \
  -e OLLAMA_BASE_URL=http://host.docker.internal:11434 \
  -v open-webui:/app/backend/data \
  --restart always \
  ghcr.io/open-webui/open-webui:main
```

#### 使用pip安装
```bash
# 安装Open WebUI
pip install open-webui

# 启动服务
open-webui serve --host 0.0.0.0 --port 3000
```

### 第四步：配置MCP集成

#### 在Open WebUI中配置MCP

1. **访问Web界面**
   ```
   http://localhost:3000
   ```

2. **创建管理员账户**
   - 首次访问会提示创建账户
   - 设置用户名和密码

3. **配置MCP服务器**
   - 进入设置 → 连接 → MCP服务器
   - 添加新服务器：
     ```
     名称: 数据库分析助手
     命令: python enterprise_database_mcp_server.py
     工作目录: /path/to/your/project
     环境变量: 从.env文件加载
     ```

4. **测试连接**
   - 在聊天界面输入：`"获取数据库表信息"`
   - 验证MCP工具是否正常工作

### 第五步：优化配置

#### Ollama配置优化

1. **创建Modelfile自定义模型**
   ```bash
   # 创建自定义配置文件
   cat > Modelfile << EOF
   FROM qwen2.5:7b
   
   # 设置系统提示
   SYSTEM """
   你是一个专业的数据库分析助手，专门帮助用户分析MySQL数据库中的传感器数据。
   你可以：
   1. 执行统计分析
   2. 检测数据异常
   3. 生成可视化图表
   4. 分析数据趋势
   5. 设置智能提醒
   
   请用简洁、专业的中文回答用户问题。
   """
   
   # 设置参数
   PARAMETER temperature 0.7
   PARAMETER top_p 0.9
   PARAMETER top_k 40
   PARAMETER num_ctx 4096
   EOF
   
   # 创建自定义模型
   ollama create database-analyst -f Modelfile
   ```

2. **性能调优**
   ```bash
   # 设置环境变量
   export OLLAMA_NUM_PARALLEL=2
   export OLLAMA_MAX_LOADED_MODELS=2
   export OLLAMA_FLASH_ATTENTION=1
   ```

#### Open WebUI配置优化

1. **创建配置文件**
   ```yaml
   # docker-compose.yml
   version: '3.8'
   services:
     open-webui:
       image: ghcr.io/open-webui/open-webui:main
       container_name: open-webui
       ports:
         - "3000:8080"
       environment:
         - OLLAMA_BASE_URL=http://host.docker.internal:11434
         - WEBUI_SECRET_KEY=your-secret-key
         - WEBUI_AUTH=True
       volumes:
         - open-webui:/app/backend/data
       restart: unless-stopped
   
   volumes:
     open-webui:
   ```

2. **启动服务**
   ```bash
   docker-compose up -d
   ```

## 🎤 语音功能集成

### 配置语音识别

1. **安装语音依赖**
   ```bash
   pip install speechrecognition pyttsx3 pyaudio
   ```

2. **配置音频设备**
   ```python
   # 测试麦克风
   import speech_recognition as sr
   
   r = sr.Recognizer()
   with sr.Microphone() as source:
       print("请说话...")
       audio = r.listen(source)
       try:
           text = r.recognize_google(audio, language='zh-CN')
           print(f"识别结果: {text}")
       except:
           print("识别失败")
   ```

3. **集成到Open WebUI**
   - 使用浏览器的Web Speech API
   - 或集成到MCP服务器中

## 📊 性能监控和优化

### 监控指标

1. **模型性能**
   ```bash
   # 查看模型状态
   ollama list
   
   # 监控资源使用
   ollama ps
   ```

2. **系统资源**
   ```bash
   # GPU使用情况（如果有）
   nvidia-smi
   
   # 内存使用
   free -h
   
   # CPU使用
   top
   ```

### 性能优化建议

1. **硬件优化**
   - 使用SSD存储模型
   - 增加内存容量
   - 使用GPU加速（如果支持）

2. **软件优化**
   - 选择合适大小的模型
   - 调整并发参数
   - 优化系统提示词

## 🔧 高级配置

### 多模型部署

```bash
# 同时运行多个模型
ollama run qwen2.5:7b &
ollama run chatglm3:6b &

# 在Open WebUI中切换模型
```

### API集成

```python
# 直接调用Ollama API
import requests

def query_ollama(prompt, model="qwen2.5:7b"):
    response = requests.post(
        "http://localhost:11434/api/generate",
        json={
            "model": model,
            "prompt": prompt,
            "stream": False
        }
    )
    return response.json()["response"]

# 示例使用
result = query_ollama("分析数据库中的温度趋势")
print(result)
```

### 自定义插件开发

```python
# Open WebUI插件示例
class DatabaseAnalysisPlugin:
    def __init__(self):
        self.name = "数据库分析插件"
        self.version = "1.0.0"
    
    def process_message(self, message):
        # 处理用户消息
        if "数据分析" in message:
            return self.trigger_mcp_analysis(message)
        return message
    
    def trigger_mcp_analysis(self, query):
        # 触发MCP工具调用
        pass
```

## 🔍 故障排除

### 常见问题

1. **Ollama启动失败**
   ```bash
   # 检查端口占用
   netstat -an | grep 11434
   
   # 重启服务
   ollama serve
   ```

2. **模型下载失败**
   ```bash
   # 使用代理
   export HTTP_PROXY=http://proxy:port
   export HTTPS_PROXY=http://proxy:port
   ollama pull qwen2.5:7b
   ```

3. **内存不足**
   ```bash
   # 选择更小的模型
   ollama pull qwen2.5:3b
   
   # 或增加虚拟内存
   sudo swapon /swapfile
   ```

4. **Open WebUI连接问题**
   ```bash
   # 检查Ollama服务
   curl http://localhost:11434/api/tags
   
   # 重启Open WebUI
   docker restart open-webui
   ```

## 🎯 使用示例

### 基本对话
```
用户: "分析过去24小时的温度数据"
助手: "我来为您分析过去24小时的温度数据..."
[调用MCP工具: advanced_statistical_analysis]
助手: "分析完成！过去24小时温度平均值为26.8°C，基于18个数据点..."
```

### 语音交互
```
用户: [语音] "检测异常数据"
助手: "收到语音指令，正在检测异常数据..."
[调用MCP工具: intelligent_anomaly_detection]
助手: [语音播报] "发现2个温度异常点，异常率11.11%..."
```

## 📈 扩展功能

### 集成更多模型
- 添加专业领域模型
- 集成多模态模型
- 支持代码生成模型

### 自定义界面
- 开发专用Web界面
- 集成到现有系统
- 移动端适配

---

**完成本地LLM部署后，您将拥有一个完全自主可控的AI助手，专门用于数据库分析任务！** 🎉
