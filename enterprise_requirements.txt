# 企业级AI数据分析MCP系统 - 完整版依赖包

# ==========================================
# 核心框架
# ==========================================
fastmcp>=2.0.0
streamlit>=1.28.0
fastapi>=0.100.0
uvicorn>=0.23.0
websockets>=11.0.0

# ==========================================
# 数据库和缓存
# ==========================================
mysql-connector-python>=8.0.0
redis>=4.5.0
sqlalchemy>=2.0.0

# ==========================================
# 数据处理和分析
# ==========================================
pandas>=2.0.0
numpy>=1.24.0
scipy>=1.10.0
scikit-learn>=1.3.0

# ==========================================
# AI和机器学习
# ==========================================
openai>=1.0.0
transformers>=4.30.0
torch>=2.0.0

# ==========================================
# 可视化
# ==========================================
matplotlib>=3.7.0
plotly>=5.15.0
seaborn>=0.12.0

# ==========================================
# 语音处理
# ==========================================
speechrecognition>=3.10.0
pyttsx3>=2.90
pyaudio>=0.2.11

# ==========================================
# 图像和多媒体
# ==========================================
opencv-python>=4.8.0
pillow>=10.0.0

# ==========================================
# 网络和通信
# ==========================================
requests>=2.31.0
aiohttp>=3.8.0

# ==========================================
# 数据验证
# ==========================================
pydantic>=2.0.0

# ==========================================
# 配置和环境
# ==========================================
python-dotenv>=1.0.0

# ==========================================
# 系统工具
# ==========================================
psutil>=5.9.0

# ==========================================
# 其他工具
# ==========================================
tqdm>=4.65.0
rich>=13.5.0
