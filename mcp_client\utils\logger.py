#!/usr/bin/env python3
"""
日志工具模块
"""

import sys
from pathlib import Path
from loguru import logger
from typing import Optional
from ..config import config

class MCPLogger:
    """MCP客户端日志器"""
    
    def __init__(self):
        self._setup_logger()
    
    def _setup_logger(self):
        """设置日志器"""
        # 移除默认处理器
        logger.remove()
        
        # 控制台输出
        logger.add(
            sys.stdout,
            level=config.log.level,
            format="<green>{time:HH:mm:ss}</green> | <level>{level: <8}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | <level>{message}</level>",
            colorize=True
        )
        
        # 文件输出
        log_file = Path(config.log.file_path)
        log_file.parent.mkdir(parents=True, exist_ok=True)
        
        logger.add(
            log_file,
            level=config.log.level,
            format=config.log.format,
            rotation=config.log.max_size,
            retention=config.log.backup_count,
            encoding="utf-8"
        )
        
        # 错误文件
        error_file = log_file.parent / "error.log"
        logger.add(
            error_file,
            level="ERROR",
            format=config.log.format,
            rotation="1 MB",
            retention=3,
            encoding="utf-8"
        )
    
    def get_logger(self, name: Optional[str] = None):
        """获取日志器实例"""
        if name:
            return logger.bind(name=name)
        return logger

# 全局日志器实例
mcp_logger = MCPLogger()
log = mcp_logger.get_logger("MCPClient")

# 导出
__all__ = ["MCPLogger", "mcp_logger", "log"]
