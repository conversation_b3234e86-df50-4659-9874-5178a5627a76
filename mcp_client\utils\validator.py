#!/usr/bin/env python3
"""
数据验证工具
"""

import re
from datetime import datetime
from typing import Any, Dict, List, Union, Optional

class DataValidator:
    """数据验证器"""
    
    @staticmethod
    def validate_time_range(time_range: str) -> bool:
        """验证时间范围格式"""
        pattern = r'^\d+[hd]$'
        return bool(re.match(pattern, time_range))
    
    @staticmethod
    def validate_datetime(dt_str: str) -> bool:
        """验证日期时间格式"""
        try:
            datetime.strptime(dt_str, "%Y-%m-%d %H:%M:%S")
            return True
        except ValueError:
            return False
    
    @staticmethod
    def validate_column_name(column: str) -> bool:
        """验证列名"""
        valid_columns = [
            "temperature", "pressure", "humidity", "flow_rate",
            "voltage", "current", "power", "device_id", "location", "status"
        ]
        return column in valid_columns
    
    @staticmethod
    def validate_chart_type(chart_type: str) -> bool:
        """验证图表类型"""
        valid_types = ["line", "bar", "pie", "scatter", "heatmap", "histogram", "box"]
        return chart_type in valid_types
    
    @staticmethod
    def validate_operation(operation: str) -> bool:
        """验证统计操作"""
        valid_operations = ["sum", "average", "count", "min", "max", "std", "median"]
        return operation in valid_operations
    
    @staticmethod
    def validate_anomaly_method(method: str) -> bool:
        """验证异常检测方法"""
        valid_methods = ["statistical", "isolation_forest", "zscore", "iqr", "hybrid"]
        return method in valid_methods

def validate_input(data: Dict[str, Any], rules: Dict[str, Any]) -> Dict[str, Any]:
    """
    验证输入数据
    
    Args:
        data: 输入数据
        rules: 验证规则
        
    Returns:
        验证结果
    """
    result = {
        "valid": True,
        "errors": [],
        "warnings": []
    }
    
    for field, rule in rules.items():
        if field not in data:
            if rule.get("required", False):
                result["valid"] = False
                result["errors"].append(f"缺少必需字段: {field}")
            continue
        
        value = data[field]
        
        # 类型检查
        if "type" in rule:
            expected_type = rule["type"]
            if not isinstance(value, expected_type):
                result["valid"] = False
                result["errors"].append(f"字段 {field} 类型错误，期望 {expected_type.__name__}")
        
        # 自定义验证器
        if "validator" in rule:
            validator = rule["validator"]
            if not validator(value):
                result["valid"] = False
                result["errors"].append(f"字段 {field} 验证失败")
    
    return result

__all__ = ["DataValidator", "validate_input"]
