#!/usr/bin/env python3
"""
MySQL数据库分析MCP服务器 - 简化版本
基于FastMCP 2.0框架构建，提供完整的数据分析功能
"""

import asyncio
import json
import logging
import os
import base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Literal
from dataclasses import dataclass
from pathlib import Path

import mysql.connector
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from pydantic import BaseModel, Field

from fastmcp import FastMCP

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 数据模型定义
@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = os.getenv("DB_HOST", "localhost")
    port: int = int(os.getenv("DB_PORT", "3306"))
    user: str = os.getenv("DB_USER", "root")
    password: str = os.getenv("DB_PASSWORD", "")
    database: str = os.getenv("DB_NAME", "sensor_data")
    charset: str = os.getenv("DB_CHARSET", "utf8mb4")

class StatisticalResult(BaseModel):
    """统计分析结果"""
    operation: str = Field(description="统计操作类型")
    column: str = Field(description="分析的列名")
    start_time: str = Field(description="开始时间")
    end_time: str = Field(description="结束时间")
    result: float = Field(description="统计结果")
    count: int = Field(description="数据点数量")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="额外元数据")

class AnomalyResult(BaseModel):
    """异常检测结果"""
    column: str = Field(description="检测的列名")
    anomalies: List[Dict[str, Any]] = Field(description="异常数据点")
    threshold: float = Field(description="阈值")
    total_points: int = Field(description="总数据点")
    anomaly_count: int = Field(description="异常点数量")
    anomaly_rate: float = Field(description="异常率")

# 创建FastMCP服务器实例
mcp = FastMCP(
    name="数据库分析助手",
    instructions="""
    我是一个专业的MySQL数据库分析助手，能够：
    1. 执行复杂的统计分析和数据挖掘
    2. 实时监控数据异常并提供智能预警
    3. 生成各种类型的数据可视化图表
    4. 提供数据趋势分析和预测
    
    我专门针对实时传感器数据进行优化，支持大数据量处理。
    """,
    dependencies=[
        "mysql-connector-python>=8.0.0",
        "pandas>=2.0.0", 
        "numpy>=1.24.0",
        "matplotlib>=3.7.0",
        "plotly>=5.15.0",
        "scikit-learn>=1.3.0"
    ]
)

# 全局变量
db_config = DatabaseConfig()

async def get_db_connection():
    """获取数据库连接"""
    try:
        connection = mysql.connector.connect(
            host=db_config.host,
            port=db_config.port,
            user=db_config.user,
            password=db_config.password,
            database=db_config.database,
            charset=db_config.charset,
            autocommit=True
        )
        return connection
    except Exception as e:
        logger.error(f"数据库连接失败: {str(e)}")
        raise

async def execute_query(query: str, params: tuple = None) -> pd.DataFrame:
    """执行SQL查询并返回DataFrame"""
    logger.debug(f"执行查询: {query}")
    
    connection = await get_db_connection()
    try:
        df = pd.read_sql(query, connection, params=params)
        logger.info(f"查询返回 {len(df)} 行数据")
        return df
    finally:
        connection.close()

# 统计分析工具
@mcp.tool
async def statistical_analysis(
    start_time: str = Field(description="开始时间 (YYYY-MM-DD HH:MM:SS)"),
    end_time: str = Field(description="结束时间 (YYYY-MM-DD HH:MM:SS)"),
    columns: List[str] = Field(description="要分析的列名列表"),
    operation: Literal["sum", "average", "count", "min", "max", "std"] = Field(description="统计操作类型")
) -> List[StatisticalResult]:
    """
    按时间段进行数据统计分析
    
    支持的操作：
    - sum: 求和
    - average: 平均值
    - count: 计数
    - min: 最小值
    - max: 最大值
    - std: 标准差
    """
    logger.info(f"开始统计分析: {operation} 操作，时间范围 {start_time} 到 {end_time}")
    
    results = []
    
    for column in columns:
        try:
            # 构建查询
            if operation == "sum":
                query = f"SELECT SUM({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
            elif operation == "average":
                query = f"SELECT AVG({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
            elif operation == "count":
                query = f"SELECT COUNT({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
            elif operation == "min":
                query = f"SELECT MIN({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
            elif operation == "max":
                query = f"SELECT MAX({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
            elif operation == "std":
                query = f"SELECT STDDEV({column}) as result, COUNT(*) as count FROM sensor_data WHERE timestamp BETWEEN %s AND %s"
            
            df = await execute_query(query, (start_time, end_time))
            
            if not df.empty and df.iloc[0]['result'] is not None:
                result = StatisticalResult(
                    operation=operation,
                    column=column,
                    start_time=start_time,
                    end_time=end_time,
                    result=float(df.iloc[0]['result']),
                    count=int(df.iloc[0]['count']),
                    metadata={
                        "query_time": datetime.now().isoformat(),
                        "data_range": f"{start_time} - {end_time}"
                    }
                )
                results.append(result)
                logger.info(f"列 {column} 的 {operation} 结果: {result.result}")
            else:
                logger.warning(f"列 {column} 在指定时间范围内无有效数据")
                
        except Exception as e:
            logger.error(f"分析列 {column} 时出错: {str(e)}")
            continue
    
    return results

# 异常检测工具
@mcp.tool
async def anomaly_detection(
    column: str = Field(description="要检测的列名"),
    threshold_type: Literal["static", "dynamic", "statistical"] = Field(description="阈值类型"),
    threshold_value: Optional[float] = Field(default=None, description="静态阈值（仅static类型需要）"),
    time_window: str = Field(default="24h", description="时间窗口 (如: 1h, 24h, 7d)"),
    sensitivity: float = Field(default=2.0, description="敏感度（标准差倍数，仅statistical类型）")
) -> AnomalyResult:
    """
    基于不同策略检测数据异常
    
    阈值类型：
    - static: 使用固定阈值
    - dynamic: 基于历史数据动态计算阈值
    - statistical: 基于统计学方法（均值±N倍标准差）
    """
    logger.info(f"开始异常检测: 列={column}, 类型={threshold_type}, 时间窗口={time_window}")
    
    # 解析时间窗口
    now = datetime.now()
    if time_window.endswith('h'):
        hours = int(time_window[:-1])
        start_time = now - timedelta(hours=hours)
    elif time_window.endswith('d'):
        days = int(time_window[:-1])
        start_time = now - timedelta(days=days)
    else:
        start_time = now - timedelta(hours=24)  # 默认24小时
    
    # 获取数据
    query = f"""
    SELECT timestamp, {column} 
    FROM sensor_data 
    WHERE timestamp >= %s AND {column} IS NOT NULL
    ORDER BY timestamp
    """
    df = await execute_query(query, (start_time.strftime('%Y-%m-%d %H:%M:%S'),))
    
    if df.empty:
        logger.warning(f"在时间窗口 {time_window} 内没有找到列 {column} 的有效数据")
        return AnomalyResult(
            column=column,
            anomalies=[],
            threshold=0.0,
            total_points=0,
            anomaly_count=0,
            anomaly_rate=0.0
        )
    
    values = df[column].values
    anomalies = []
    
    # 根据阈值类型计算阈值
    if threshold_type == "static":
        if threshold_value is None:
            raise ValueError("静态阈值类型需要提供threshold_value参数")
        threshold = threshold_value
        anomaly_mask = np.abs(values) > threshold
        
    elif threshold_type == "dynamic":
        # 使用滑动窗口计算动态阈值
        window_size = min(50, len(values) // 4)  # 动态窗口大小
        rolling_mean = pd.Series(values).rolling(window=window_size, center=True).mean()
        rolling_std = pd.Series(values).rolling(window=window_size, center=True).std()
        threshold = rolling_std.mean() * 2  # 平均标准差的2倍
        anomaly_mask = np.abs(values - rolling_mean) > (rolling_std * 2)
        
    elif threshold_type == "statistical":
        mean_val = np.mean(values)
        std_val = np.std(values)
        threshold = std_val * sensitivity
        anomaly_mask = np.abs(values - mean_val) > threshold
    
    # 收集异常点
    anomaly_indices = np.where(anomaly_mask)[0]
    for idx in anomaly_indices:
        anomaly_data = {
            "timestamp": df.iloc[idx]['timestamp'].isoformat(),
            "value": float(values[idx]),
            "deviation": float(abs(values[idx] - np.mean(values))),
            "index": int(idx)
        }
        anomalies.append(anomaly_data)
    
    result = AnomalyResult(
        column=column,
        anomalies=anomalies,
        threshold=float(threshold),
        total_points=len(values),
        anomaly_count=len(anomalies),
        anomaly_rate=len(anomalies) / len(values) if len(values) > 0 else 0.0
    )
    
    logger.info(f"检测完成: 发现 {len(anomalies)} 个异常点，异常率 {result.anomaly_rate:.2%}")
    return result

# 数据库管理工具
@mcp.tool
async def get_table_info() -> Dict[str, Any]:
    """获取数据库表结构信息"""
    logger.info("获取数据库表结构信息")
    
    try:
        # 获取表结构
        query = "DESCRIBE sensor_data"
        df = await execute_query(query)
        
        columns_info = []
        for _, row in df.iterrows():
            columns_info.append({
                "field": row['Field'],
                "type": row['Type'],
                "null": row['Null'],
                "key": row['Key'],
                "default": row['Default'],
                "extra": row['Extra']
            })
        
        # 获取数据统计
        count_query = "SELECT COUNT(*) as total_rows FROM sensor_data"
        count_df = await execute_query(count_query)
        total_rows = int(count_df.iloc[0]['total_rows'])
        
        # 获取时间范围
        time_range_query = """
        SELECT 
            MIN(timestamp) as earliest,
            MAX(timestamp) as latest
        FROM sensor_data
        """
        time_df = await execute_query(time_range_query)
        
        return {
            "table_name": "sensor_data",
            "columns": columns_info,
            "total_rows": total_rows,
            "time_range": {
                "earliest": time_df.iloc[0]['earliest'].isoformat() if time_df.iloc[0]['earliest'] else None,
                "latest": time_df.iloc[0]['latest'].isoformat() if time_df.iloc[0]['latest'] else None
            },
            "database_config": {
                "host": db_config.host,
                "database": db_config.database,
                "charset": db_config.charset
            }
        }
        
    except Exception as e:
        logger.error(f"获取表信息错误: {str(e)}")
        return {"error": str(e)}

# 服务器启动和配置
if __name__ == "__main__":
    # 设置matplotlib中文字体
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 启动服务器
    print("🚀 数据库分析MCP服务器启动中...")
    print(f"📊 数据库连接: {db_config.host}:{db_config.port}/{db_config.database}")
    print("🎯 支持功能:")
    print("   • 统计分析 (statistical_analysis)")
    print("   • 异常检测 (anomaly_detection)")  
    print("   • 数据库管理 (get_table_info)")
    print("\n🔧 使用STDIO传输，适合本地Claude Desktop连接")
    print("💡 安装命令: fastmcp install claude-desktop database_analysis_server_simple.py")
    
    # 使用STDIO传输（本地连接）
    mcp.run()
