#!/usr/bin/env python3
"""
MCP客户端配置文件
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass

# 项目根目录
PROJECT_ROOT = Path(__file__).parent.parent
MCP_CLIENT_ROOT = Path(__file__).parent

@dataclass
class MCPServerConfig:
    """MCP服务器配置"""
    name: str
    server_path: str
    env_vars: Dict[str, str]
    timeout: float = 30.0
    keep_alive: bool = True
    
@dataclass
class UIConfig:
    """UI配置"""
    theme: str = "dark"
    language: str = "zh-CN"
    auto_refresh: bool = True
    refresh_interval: int = 30
    page_size: int = 20

@dataclass
class LogConfig:
    """日志配置"""
    level: str = "INFO"
    file_path: str = "logs/mcp_client.log"
    max_size: str = "10MB"
    backup_count: int = 5
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"

class Config:
    """主配置类"""
    
    def __init__(self):
        # MCP服务器配置
        self.mcp_servers = {
            "enterprise_db": MCPServerConfig(
                name="企业级数据库分析助手",
                server_path=str(PROJECT_ROOT / "enterprise_database_mcp_server.py"),
                env_vars={
                    "DB_HOST": os.getenv("DB_HOST", "localhost"),
                    "DB_PORT": os.getenv("DB_PORT", "3306"),
                    "DB_USER": os.getenv("DB_USER", "root"),
                    "DB_PASSWORD": os.getenv("DB_PASSWORD", "123456"),
                    "DB_NAME": os.getenv("DB_NAME", "sensor_data"),
                    "DB_CHARSET": os.getenv("DB_CHARSET", "utf8mb4"),
                    "PYTHONIOENCODING": "utf-8",
                    # LLM配置
                    "OPENAI_API_KEY": os.getenv("OPENAI_API_KEY", ""),
                    "OPENAI_MODEL": os.getenv("OPENAI_MODEL", "gpt-4o-mini"),
                    "USE_LOCAL_MODEL": os.getenv("USE_LOCAL_MODEL", "false"),
                    "AI_MAX_TOKENS": os.getenv("AI_MAX_TOKENS", "2000"),
                    "AI_TEMPERATURE": os.getenv("AI_TEMPERATURE", "0.7")
                },
                timeout=60.0,
                keep_alive=True
            )
        }
        
        # UI配置
        self.ui = UIConfig(
            theme=os.getenv("UI_THEME", "dark"),
            language=os.getenv("UI_LANGUAGE", "zh-CN"),
            auto_refresh=os.getenv("AUTO_REFRESH", "true").lower() == "true",
            refresh_interval=int(os.getenv("REFRESH_INTERVAL", "30")),
            page_size=int(os.getenv("PAGE_SIZE", "20"))
        )
        
        # 日志配置
        self.log = LogConfig(
            level=os.getenv("LOG_LEVEL", "INFO"),
            file_path=os.getenv("LOG_FILE", str(MCP_CLIENT_ROOT / "logs" / "mcp_client.log")),
            max_size=os.getenv("LOG_MAX_SIZE", "10MB"),
            backup_count=int(os.getenv("LOG_BACKUP_COUNT", "5"))
        )
        
        # 应用配置
        self.app = {
            "name": "企业级数据库分析MCP客户端",
            "version": "1.0.0",
            "description": "本地化MCP客户端，支持智能数据分析和可视化",
            "author": "Enterprise Data Team",
            "debug": os.getenv("DEBUG", "false").lower() == "true"
        }
        
        # 网络配置
        self.network = {
            "timeout": float(os.getenv("NETWORK_TIMEOUT", "30.0")),
            "retry_count": int(os.getenv("RETRY_COUNT", "3")),
            "retry_delay": float(os.getenv("RETRY_DELAY", "1.0"))
        }
        
        # 缓存配置
        self.cache = {
            "enabled": os.getenv("CACHE_ENABLED", "true").lower() == "true",
            "ttl": int(os.getenv("CACHE_TTL", "300")),  # 5分钟
            "max_size": int(os.getenv("CACHE_MAX_SIZE", "1000"))
        }
        
        # 数据分析配置
        self.analysis = {
            "default_time_range": os.getenv("DEFAULT_TIME_RANGE", "24h"),
            "max_data_points": int(os.getenv("MAX_DATA_POINTS", "10000")),
            "anomaly_sensitivity": float(os.getenv("ANOMALY_SENSITIVITY", "2.0")),
            "chart_height": int(os.getenv("CHART_HEIGHT", "500"))
        }
        
        # 创建必要的目录
        self._create_directories()
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            MCP_CLIENT_ROOT / "logs",
            MCP_CLIENT_ROOT / "cache",
            MCP_CLIENT_ROOT / "exports",
            MCP_CLIENT_ROOT / "temp"
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
    
    def get_server_config(self, server_name: str) -> Optional[MCPServerConfig]:
        """获取服务器配置"""
        return self.mcp_servers.get(server_name)
    
    def add_server_config(self, server_name: str, config: MCPServerConfig):
        """添加服务器配置"""
        self.mcp_servers[server_name] = config
    
    def update_env_vars(self, server_name: str, env_vars: Dict[str, str]):
        """更新服务器环境变量"""
        if server_name in self.mcp_servers:
            self.mcp_servers[server_name].env_vars.update(env_vars)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "mcp_servers": {
                name: {
                    "name": config.name,
                    "server_path": config.server_path,
                    "env_vars": config.env_vars,
                    "timeout": config.timeout,
                    "keep_alive": config.keep_alive
                }
                for name, config in self.mcp_servers.items()
            },
            "ui": {
                "theme": self.ui.theme,
                "language": self.ui.language,
                "auto_refresh": self.ui.auto_refresh,
                "refresh_interval": self.ui.refresh_interval,
                "page_size": self.ui.page_size
            },
            "log": {
                "level": self.log.level,
                "file_path": self.log.file_path,
                "max_size": self.log.max_size,
                "backup_count": self.log.backup_count,
                "format": self.log.format
            },
            "app": self.app,
            "network": self.network,
            "cache": self.cache,
            "analysis": self.analysis
        }

# 全局配置实例
config = Config()

# 常用配置快捷访问
DEFAULT_SERVER = "enterprise_db"
LOG_LEVEL = config.log.level
UI_THEME = config.ui.theme
CACHE_ENABLED = config.cache["enabled"]
DEBUG_MODE = config.app["debug"]

# 导出配置
__all__ = [
    "Config",
    "MCPServerConfig", 
    "UIConfig",
    "LogConfig",
    "config",
    "DEFAULT_SERVER",
    "LOG_LEVEL",
    "UI_THEME",
    "CACHE_ENABLED",
    "DEBUG_MODE"
]
