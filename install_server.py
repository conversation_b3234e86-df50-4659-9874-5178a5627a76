#!/usr/bin/env python3
"""
数据库分析MCP服务器安装脚本
自动安装依赖并配置Claude Desktop连接
"""

import os
import sys
import subprocess
import json
from pathlib import Path

def install_dependencies():
    """安装Python依赖包"""
    print("📦 安装Python依赖包...")
    
    dependencies = [
        "fastmcp>=2.0.0",
        "mysql-connector-python>=8.0.0",
        "pandas>=2.0.0", 
        "numpy>=1.24.0",
        "matplotlib>=3.7.0",
        "plotly>=5.15.0",
        "speechrecognition>=3.10.0",
        "pyttsx3>=2.90",
        "scikit-learn>=1.3.0"
    ]
    
    for dep in dependencies:
        try:
            print(f"  安装 {dep}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
        except subprocess.CalledProcessError as e:
            print(f"  ❌ 安装 {dep} 失败: {e}")
            return False
    
    print("✅ 所有依赖包安装完成")
    return True

def create_env_file():
    """创建环境配置文件"""
    print("⚙️ 创建环境配置文件...")
    
    env_content = """# 数据库配置
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=sensor_data
DB_CHARSET=utf8mb4

# 异常检测阈值配置
TEMP_THRESHOLD=50.0
PRESSURE_THRESHOLD=100.0
FLOW_THRESHOLD=200.0

# 语音配置
VOICE_LANGUAGE=zh-CN
SPEECH_RATE=150

# 日志配置
LOG_LEVEL=INFO
"""
    
    with open(".env", "w", encoding="utf-8") as f:
        f.write(env_content)
    
    print("✅ 环境配置文件 .env 已创建")
    print("⚠️  请编辑 .env 文件，配置您的数据库连接信息")

def install_to_claude_desktop():
    """使用FastMCP CLI安装到Claude Desktop"""
    print("🔧 安装到Claude Desktop...")
    
    try:
        # 检查fastmcp命令是否可用
        subprocess.check_call(["fastmcp", "--version"], 
                            stdout=subprocess.DEVNULL, 
                            stderr=subprocess.DEVNULL)
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ fastmcp命令未找到，请先安装FastMCP")
        print("   安装命令: pip install fastmcp")
        return False
    
    # 获取当前脚本目录
    current_dir = Path(__file__).parent.absolute()
    server_path = current_dir / "database_analysis_server.py"
    
    if not server_path.exists():
        print(f"❌ 服务器文件未找到: {server_path}")
        return False
    
    # 构建安装命令
    install_cmd = [
        "fastmcp", "install", "claude-desktop", str(server_path),
        "--name", "数据库分析助手",
        "--env-file", str(current_dir / ".env")
    ]
    
    # 添加依赖
    dependencies = [
        "mysql-connector-python",
        "pandas", 
        "numpy",
        "matplotlib",
        "plotly",
        "speechrecognition",
        "pyttsx3",
        "scikit-learn"
    ]
    
    for dep in dependencies:
        install_cmd.extend(["--with", dep])
    
    try:
        print("  执行安装命令...")
        subprocess.check_call(install_cmd)
        print("✅ 成功安装到Claude Desktop")
        print("🔄 请重启Claude Desktop以加载新的MCP服务器")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ 安装失败: {e}")
        return False

def create_sample_database():
    """创建示例数据库结构"""
    print("🗄️ 创建示例数据库结构...")
    
    sql_script = """-- 创建传感器数据表
CREATE DATABASE IF NOT EXISTS sensor_data CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE sensor_data;

CREATE TABLE IF NOT EXISTS sensor_data (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    timestamp DATETIME NOT NULL,
    temperature DECIMAL(5,2) COMMENT '温度(°C)',
    pressure DECIMAL(8,2) COMMENT '压力(Pa)',
    humidity DECIMAL(5,2) COMMENT '湿度(%)',
    flow_rate DECIMAL(8,2) COMMENT '流量(L/min)',
    voltage DECIMAL(6,2) COMMENT '电压(V)',
    current DECIMAL(6,2) COMMENT '电流(A)',
    power DECIMAL(8,2) COMMENT '功率(W)',
    INDEX idx_timestamp (timestamp),
    INDEX idx_temperature (temperature),
    INDEX idx_pressure (pressure)
) ENGINE=InnoDB COMMENT='传感器数据表';

-- 插入示例数据
INSERT INTO sensor_data (timestamp, temperature, pressure, humidity, flow_rate, voltage, current, power) VALUES
('2025-07-24 08:00:00', 25.5, 101325.0, 65.2, 150.0, 220.0, 5.5, 1210.0),
('2025-07-24 08:01:00', 25.7, 101320.0, 65.0, 152.0, 220.5, 5.6, 1234.8),
('2025-07-24 08:02:00', 25.6, 101318.0, 64.8, 151.5, 220.2, 5.5, 1211.1),
('2025-07-24 08:03:00', 25.8, 101322.0, 65.1, 153.0, 220.8, 5.7, 1258.6),
('2025-07-24 08:04:00', 25.9, 101315.0, 64.9, 154.0, 221.0, 5.8, 1281.8);
"""
    
    with open("create_database.sql", "w", encoding="utf-8") as f:
        f.write(sql_script)
    
    print("✅ 数据库脚本已创建: create_database.sql")
    print("💡 请在MySQL中执行此脚本来创建示例数据库")

def main():
    """主安装流程"""
    print("🚀 数据库分析MCP服务器安装程序")
    print("=" * 50)
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    
    # 1. 安装依赖
    if not install_dependencies():
        print("❌ 依赖安装失败，请检查网络连接和权限")
        sys.exit(1)
    
    # 2. 创建配置文件
    create_env_file()
    
    # 3. 创建示例数据库
    create_sample_database()
    
    # 4. 安装到Claude Desktop
    print("\n" + "=" * 50)
    choice = input("是否安装到Claude Desktop? (y/n): ").lower().strip()
    
    if choice in ['y', 'yes', '是']:
        if install_to_claude_desktop():
            print("\n🎉 安装完成！")
            print("\n📋 后续步骤:")
            print("1. 编辑 .env 文件，配置数据库连接")
            print("2. 在MySQL中执行 create_database.sql 创建数据库")
            print("3. 重启Claude Desktop")
            print("4. 在Claude Desktop中测试MCP工具")
            print("\n💡 测试命令示例:")
            print("   '分析过去24小时的温度数据'")
            print("   '检测压力异常值'")
            print("   '生成温度趋势图'")
        else:
            print("❌ Claude Desktop安装失败")
    else:
        print("\n✅ 基础安装完成")
        print("💡 手动安装到Claude Desktop:")
        print("   fastmcp install claude-desktop database_analysis_server.py --name '数据库分析助手' --env-file .env")

if __name__ == "__main__":
    main()
