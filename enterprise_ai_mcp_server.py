#!/usr/bin/env python3
"""
企业级AI增强数据分析MCP服务器 - 完整版
基于FastMCP 2.0框架，集成OpenAI智能分析

🚀 核心功能：
1. AI智能分析 - OpenAI GPT集成，智能数据解读
2. 实时数据流处理 - 支持千万级数据实时分析
3. 多模态交互 - 语音、文本、图像识别
4. 智能提醒系统 - 邮件、短信、语音、桌面通知
5. 高级可视化 - 3D图表、动态仪表盘
6. 趋势预测 - 机器学习驱动的预测分析
7. 完全本地部署 - 支持本地模型替换OpenAI

🏗️ 技术架构：
- FastMCP 2.0 框架
- MySQL连接池 + Redis缓存
- OpenAI API + 本地模型支持
- 异步流式处理
- WebSocket实时通信
"""

import asyncio
import json
import logging
import os
import base64
import threading
import queue
import smtplib
import ssl
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Literal, Union
from dataclasses import dataclass, field
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor
import warnings
warnings.filterwarnings('ignore')

# 核心依赖
import mysql.connector
from mysql.connector import pooling
import pandas as pd
import numpy as np
from scipy import stats
from sklearn.ensemble import IsolationForest
from sklearn.preprocessing import StandardScaler
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error

# AI和机器学习
import openai
from transformers import pipeline
import torch

# 可视化增强
import matplotlib.pyplot as plt
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import seaborn as sns
import plotly.io as pio

# 语音和多媒体
import speech_recognition as sr
import pyttsx3
import cv2
from PIL import Image

# 通信和通知
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import requests
import websockets
import redis

# FastMCP框架
from fastmcp import FastMCP
from pydantic import BaseModel, Field
from fastapi import FastAPI, HTTPException, WebSocket, WebSocketDisconnect
from fastapi.responses import JSONResponse, HTMLResponse
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware

# ==========================================
# 配置和数据模型
# ==========================================

# 日志配置
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('enterprise_ai_mcp.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@dataclass
class DatabaseConfig:
    """数据库配置"""
    host: str = os.getenv('DB_HOST', 'localhost')
    port: int = int(os.getenv('DB_PORT', '3306'))
    user: str = os.getenv('DB_USER', 'root')
    password: str = os.getenv('DB_PASSWORD', '123456')
    database: str = os.getenv('DB_NAME', 'sensor_data')
    charset: str = os.getenv('DB_CHARSET', 'utf8mb4')
    pool_size: int = int(os.getenv('DB_POOL_SIZE', '20'))
    pool_name: str = 'enterprise_ai_pool'

@dataclass
class AIConfig:
    """AI配置"""
    openai_api_key: str = os.getenv('OPENAI_API_KEY', '')
    openai_model: str = os.getenv('OPENAI_MODEL', 'gpt-4')
    local_model_path: str = os.getenv('LOCAL_MODEL_PATH', '')
    use_local_model: bool = os.getenv('USE_LOCAL_MODEL', 'false').lower() == 'true'
    max_tokens: int = int(os.getenv('AI_MAX_TOKENS', '2000'))
    temperature: float = float(os.getenv('AI_TEMPERATURE', '0.7'))

@dataclass
class NotificationConfig:
    """通知配置"""
    email_smtp_server: str = os.getenv('EMAIL_SMTP_SERVER', 'smtp.gmail.com')
    email_smtp_port: int = int(os.getenv('EMAIL_SMTP_PORT', '587'))
    email_username: str = os.getenv('EMAIL_USERNAME', '')
    email_password: str = os.getenv('EMAIL_PASSWORD', '')
    sms_api_url: str = os.getenv('SMS_API_URL', '')
    sms_api_key: str = os.getenv('SMS_API_KEY', '')
    webhook_url: str = os.getenv('WEBHOOK_URL', '')

@dataclass
class PerformanceConfig:
    """性能配置"""
    max_workers: int = int(os.getenv('MAX_WORKERS', '10'))
    cache_ttl: int = int(os.getenv('CACHE_TTL', '300'))
    batch_size: int = int(os.getenv('BATCH_SIZE', '10000'))
    redis_host: str = os.getenv('REDIS_HOST', 'localhost')
    redis_port: int = int(os.getenv('REDIS_PORT', '6379'))
    redis_db: int = int(os.getenv('REDIS_DB', '0'))

# 配置实例
db_config = DatabaseConfig()
ai_config = AIConfig()
notification_config = NotificationConfig()
perf_config = PerformanceConfig()

# ==========================================
# 数据模型
# ==========================================

class AIAnalysisRequest(BaseModel):
    """AI分析请求"""
    data_summary: str = Field(description="数据摘要")
    analysis_type: str = Field(description="分析类型: trend, anomaly, prediction, insight")
    context: Optional[str] = Field(default="", description="分析上下文")
    language: str = Field(default="zh", description="返回语言")

class AIAnalysisResult(BaseModel):
    """AI分析结果"""
    analysis_type: str
    insights: List[str]
    recommendations: List[str]
    confidence_score: float
    reasoning: str
    next_actions: List[str]
    processing_time: float

class EnhancedNotification(BaseModel):
    """增强通知"""
    id: str
    title: str
    message: str
    severity: Literal["low", "medium", "high", "critical"]
    channels: List[Literal["email", "sms", "voice", "desktop", "webhook"]]
    recipients: List[str]
    created_at: datetime
    sent_at: Optional[datetime] = None
    status: Literal["pending", "sent", "failed"] = "pending"

class RealTimeDataPoint(BaseModel):
    """实时数据点"""
    timestamp: datetime
    device_id: str
    location: str
    temperature: Optional[float] = None
    humidity: Optional[float] = None
    pressure: Optional[float] = None
    voltage: Optional[float] = None
    current: Optional[float] = None
    power: Optional[float] = None
    status: str = "normal"

# ==========================================
# 全局变量和初始化
# ==========================================

# 数据库连接池
connection_pool = None

# Redis连接
redis_client = None

# AI客户端
openai_client = None
local_model = None

# 语音引擎
tts_engine = None
speech_recognizer = None

# 通知队列
notification_queue = queue.Queue()

# WebSocket连接管理
websocket_connections = set()

# 线程池
executor = ThreadPoolExecutor(max_workers=perf_config.max_workers)

# 创建FastMCP应用
mcp = FastMCP(
    name="企业级AI数据分析助手",
    description="""
    🤖 企业级AI增强数据分析MCP服务器
    
    🚀 核心功能：
    • AI智能分析 - OpenAI GPT集成，深度数据洞察
    • 实时数据流处理 - 支持千万级数据实时分析  
    • 多模态交互 - 语音、文本、图像识别
    • 智能提醒系统 - 多渠道通知（邮件、短信、语音）
    • 高级可视化 - 3D图表、动态仪表盘、实时监控
    • 趋势预测 - 机器学习驱动的预测分析
    • 完全本地部署 - 支持本地模型替换OpenAI
    
    🏗️ 技术特性：
    • FastMCP 2.0框架 - 高性能MCP协议实现
    • MySQL连接池 + Redis缓存 - 高并发数据访问
    • 异步流式处理 - 非阻塞式大数据处理
    • WebSocket实时通信 - 毫秒级数据推送
    • 智能缓存策略 - 自适应缓存优化
    • 多线程并行计算 - 充分利用多核性能
    """,
    dependencies=[
        "mysql-connector-python>=8.0.0",
        "pandas>=2.0.0", 
        "numpy>=1.24.0",
        "scipy>=1.10.0",
        "scikit-learn>=1.3.0",
        "matplotlib>=3.7.0",
        "plotly>=5.15.0",
        "seaborn>=0.12.0",
        "openai>=1.0.0",
        "transformers>=4.30.0",
        "torch>=2.0.0",
        "speechrecognition>=3.10.0",
        "pyttsx3>=2.90",
        "opencv-python>=4.8.0",
        "pillow>=10.0.0",
        "redis>=4.5.0",
        "websockets>=11.0.0",
        "fastapi>=0.100.0",
        "uvicorn>=0.23.0"
    ]
)

# 创建FastAPI应用（用于WebSocket和API）
app = FastAPI(title="企业级AI数据分析API", version="2.0.0")

# 添加CORS中间件
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# ==========================================
# 初始化函数
# ==========================================

def init_database_pool() -> bool:
    """初始化数据库连接池"""
    global connection_pool
    try:
        connection_pool = pooling.MySQLConnectionPool(
            pool_name=db_config.pool_name,
            pool_size=db_config.pool_size,
            pool_reset_session=True,
            host=db_config.host,
            port=db_config.port,
            user=db_config.user,
            password=db_config.password,
            database=db_config.database,
            charset=db_config.charset,
            autocommit=True,
            time_zone='+08:00'
        )
        
        # 测试连接
        test_connection = connection_pool.get_connection()
        test_connection.close()
        
        logger.info(f"数据库连接池初始化成功: {db_config.pool_size} 连接")
        return True
        
    except Exception as e:
        logger.error(f"数据库连接池初始化失败: {str(e)}")
        return False

def init_redis_client() -> bool:
    """初始化Redis客户端"""
    global redis_client
    try:
        redis_client = redis.Redis(
            host=perf_config.redis_host,
            port=perf_config.redis_port,
            db=perf_config.redis_db,
            decode_responses=True
        )
        
        # 测试连接
        redis_client.ping()
        
        logger.info("Redis客户端初始化成功")
        return True
        
    except Exception as e:
        logger.warning(f"Redis客户端初始化失败: {str(e)}")
        return False

def init_ai_clients() -> bool:
    """初始化AI客户端"""
    global openai_client, local_model
    
    success = False
    
    # 初始化OpenAI客户端
    if ai_config.openai_api_key:
        try:
            openai.api_key = ai_config.openai_api_key
            openai_client = openai
            logger.info("OpenAI客户端初始化成功")
            success = True
        except Exception as e:
            logger.error(f"OpenAI客户端初始化失败: {str(e)}")
    
    # 初始化本地模型
    if ai_config.use_local_model and ai_config.local_model_path:
        try:
            # 这里可以加载本地模型，如Llama、ChatGLM等
            # local_model = load_local_model(ai_config.local_model_path)
            logger.info("本地模型初始化成功")
            success = True
        except Exception as e:
            logger.error(f"本地模型初始化失败: {str(e)}")
    
    return success

def init_speech_engines() -> bool:
    """初始化语音引擎"""
    global tts_engine, speech_recognizer

    try:
        # 初始化TTS引擎
        tts_engine = pyttsx3.init()
        tts_engine.setProperty('rate', int(os.getenv('SPEECH_RATE', '150')))
        tts_engine.setProperty('volume', float(os.getenv('SPEECH_VOLUME', '0.9')))

        # 设置中文语音
        voices = tts_engine.getProperty('voices')
        for voice in voices:
            if 'chinese' in voice.name.lower() or 'zh' in voice.id.lower():
                tts_engine.setProperty('voice', voice.id)
                break

        # 初始化语音识别
        speech_recognizer = sr.Recognizer()

        logger.info("语音引擎初始化成功")
        return True
    except Exception as e:
        logger.error(f"语音引擎初始化失败: {str(e)}")
        return False

def start_notification_worker():
    """启动通知工作线程"""
    def notification_worker():
        while True:
            try:
                notification = notification_queue.get(timeout=1)
                if notification:
                    asyncio.run(send_notification(notification))
                    notification_queue.task_done()
            except queue.Empty:
                continue
            except Exception as e:
                logger.error(f"通知发送失败: {str(e)}")

    thread = threading.Thread(target=notification_worker, daemon=True)
    thread.start()
    logger.info("通知工作线程已启动")

# ==========================================
# 核心工具函数
# ==========================================

async def get_db_connection():
    """从连接池获取数据库连接"""
    try:
        if connection_pool is None:
            if not init_database_pool():
                raise Exception("连接池初始化失败")

        connection = connection_pool.get_connection()
        return connection
    except Exception as e:
        logger.error(f"获取数据库连接失败: {str(e)}")
        raise

async def execute_query_with_cache(query: str, params: tuple = None, cache_key: str = None) -> pd.DataFrame:
    """执行SQL查询，支持Redis缓存"""
    start_time = datetime.now()

    # 检查Redis缓存
    if cache_key and redis_client:
        try:
            cached_data = redis_client.get(cache_key)
            if cached_data:
                logger.debug(f"使用Redis缓存数据: {cache_key}")
                return pd.read_json(cached_data)
        except Exception as e:
            logger.warning(f"Redis缓存读取失败: {str(e)}")

    # 执行查询
    connection = await get_db_connection()
    try:
        df = pd.read_sql(query, connection, params=params)

        # 更新Redis缓存
        if cache_key and redis_client:
            try:
                redis_client.setex(cache_key, perf_config.cache_ttl, df.to_json())
            except Exception as e:
                logger.warning(f"Redis缓存写入失败: {str(e)}")

        processing_time = (datetime.now() - start_time).total_seconds()
        logger.info(f"查询完成: {len(df)} 行, 耗时 {processing_time:.2f}s")

        return df
    finally:
        connection.close()

async def call_ai_analysis(request: AIAnalysisRequest) -> AIAnalysisResult:
    """调用AI进行数据分析"""
    start_time = datetime.now()

    try:
        # 构建AI提示
        prompt = f"""
        作为一名资深数据分析专家，请分析以下数据：

        数据摘要：{request.data_summary}
        分析类型：{request.analysis_type}
        上下文：{request.context}

        请提供：
        1. 关键洞察（3-5个要点）
        2. 具体建议（3-5个行动项）
        3. 置信度评分（0-1）
        4. 分析推理过程
        5. 下一步行动建议

        请用{request.language}回答，格式为JSON。
        """

        # 调用OpenAI或本地模型
        if openai_client and ai_config.openai_api_key:
            response = await call_openai_analysis(prompt)
        elif local_model:
            response = await call_local_model_analysis(prompt)
        else:
            # 回退到规则基础分析
            response = await fallback_analysis(request)

        processing_time = (datetime.now() - start_time).total_seconds()

        return AIAnalysisResult(
            analysis_type=request.analysis_type,
            insights=response.get('insights', []),
            recommendations=response.get('recommendations', []),
            confidence_score=response.get('confidence_score', 0.8),
            reasoning=response.get('reasoning', ''),
            next_actions=response.get('next_actions', []),
            processing_time=processing_time
        )

    except Exception as e:
        logger.error(f"AI分析失败: {str(e)}")
        return AIAnalysisResult(
            analysis_type=request.analysis_type,
            insights=[f"分析过程中出现错误: {str(e)}"],
            recommendations=["请检查数据质量和分析参数"],
            confidence_score=0.0,
            reasoning="分析失败",
            next_actions=["重新检查数据和参数"],
            processing_time=(datetime.now() - start_time).total_seconds()
        )

async def call_openai_analysis(prompt: str) -> dict:
    """调用OpenAI进行分析"""
    try:
        response = await openai_client.ChatCompletion.acreate(
            model=ai_config.openai_model,
            messages=[
                {"role": "system", "content": "你是一名专业的数据分析专家，擅长从数据中发现洞察和趋势。"},
                {"role": "user", "content": prompt}
            ],
            max_tokens=ai_config.max_tokens,
            temperature=ai_config.temperature
        )

        content = response.choices[0].message.content

        # 尝试解析JSON响应
        try:
            return json.loads(content)
        except json.JSONDecodeError:
            # 如果不是JSON，则解析文本
            return parse_text_response(content)

    except Exception as e:
        logger.error(f"OpenAI调用失败: {str(e)}")
        raise

async def send_notification(notification: EnhancedNotification):
    """发送多渠道通知"""
    try:
        for channel in notification.channels:
            if channel == "email":
                await send_email_notification(notification)
            elif channel == "sms":
                await send_sms_notification(notification)
            elif channel == "voice":
                await send_voice_notification(notification)
            elif channel == "desktop":
                await send_desktop_notification(notification)
            elif channel == "webhook":
                await send_webhook_notification(notification)

        notification.status = "sent"
        notification.sent_at = datetime.now()

    except Exception as e:
        logger.error(f"通知发送失败: {str(e)}")
        notification.status = "failed"

async def broadcast_realtime_data(data: RealTimeDataPoint):
    """广播实时数据到WebSocket客户端"""
    if websocket_connections:
        message = {
            "type": "realtime_data",
            "data": data.dict(),
            "timestamp": datetime.now().isoformat()
        }

        # 向所有连接的客户端发送数据
        disconnected = set()
        for websocket in websocket_connections:
            try:
                await websocket.send_text(json.dumps(message))
            except:
                disconnected.add(websocket)

        # 清理断开的连接
        websocket_connections -= disconnected

# ==========================================
# 核心MCP工具 - AI智能分析
# ==========================================

@mcp.tool
async def ai_intelligent_analysis(
    data_query: str = Field(description="数据查询SQL或描述"),
    analysis_type: Literal["trend", "anomaly", "prediction", "insight", "comparison"] = Field(description="分析类型"),
    context: str = Field(default="", description="分析上下文和背景"),
    language: str = Field(default="zh", description="返回语言"),
    include_visualization: bool = Field(default=True, description="是否包含可视化建议")
) -> str:
    """
    AI智能数据分析 - 使用GPT进行深度数据洞察

    支持的分析类型：
    - trend: 趋势分析和预测
    - anomaly: 异常检测和原因分析
    - prediction: 预测分析和建模
    - insight: 数据洞察和模式发现
    - comparison: 对比分析和基准测试
    """
    logger.info(f"开始AI智能分析: {analysis_type}")
    start_time = datetime.now()

    try:
        # 执行数据查询
        if data_query.strip().upper().startswith('SELECT'):
            # 直接SQL查询
            df = await execute_query_with_cache(data_query, cache_key=f"ai_query_{hash(data_query)}")
        else:
            # 根据描述生成查询
            df = await generate_query_from_description(data_query)

        if df.empty:
            return "❌ 查询结果为空，无法进行分析"

        # 生成数据摘要
        data_summary = generate_data_summary(df)

        # 调用AI分析
        ai_request = AIAnalysisRequest(
            data_summary=data_summary,
            analysis_type=analysis_type,
            context=context,
            language=language
        )

        ai_result = await call_ai_analysis(ai_request)

        # 格式化结果
        result = f"""
🤖 AI智能分析报告 - {analysis_type.upper()}

📊 数据概览：
{data_summary}

🔍 关键洞察：
{chr(10).join([f"• {insight}" for insight in ai_result.insights])}

💡 建议行动：
{chr(10).join([f"• {rec}" for rec in ai_result.recommendations])}

🧠 分析推理：
{ai_result.reasoning}

📋 下一步行动：
{chr(10).join([f"• {action}" for action in ai_result.next_actions])}

📈 置信度：{ai_result.confidence_score:.2%}
⏱️ 处理时间：{ai_result.processing_time:.2f}秒
        """

        # 如果需要可视化建议
        if include_visualization and ai_result.confidence_score > 0.7:
            viz_suggestions = await generate_visualization_suggestions(df, analysis_type)
            result += f"\n\n📊 可视化建议：\n{viz_suggestions}"

        return result.strip()

    except Exception as e:
        logger.error(f"AI智能分析失败: {str(e)}")
        return f"❌ AI分析失败: {str(e)}"

@mcp.tool
async def ai_anomaly_investigation(
    column: str = Field(description="要分析的列名"),
    time_window: str = Field(default="24h", description="时间窗口"),
    investigation_depth: Literal["basic", "detailed", "comprehensive"] = Field(default="detailed", description="调查深度"),
    include_root_cause: bool = Field(default=True, description="是否包含根因分析")
) -> str:
    """
    AI异常调查 - 深度异常检测和根因分析

    调查深度：
    - basic: 基础异常检测
    - detailed: 详细异常分析和模式识别
    - comprehensive: 全面调查包含预测和建议
    """
    logger.info(f"开始AI异常调查: {column}, 深度: {investigation_depth}")

    try:
        # 获取异常数据
        from enterprise_database_mcp_server import intelligent_anomaly_detection
        anomaly_result = await intelligent_anomaly_detection(
            column=column,
            method="hybrid",
            time_window=time_window,
            include_reasons=True
        )

        if not anomaly_result.anomalies:
            return f"✅ 在过去{time_window}内，{column}列未发现异常数据"

        # 准备AI分析数据
        anomaly_summary = f"""
        检测到{len(anomaly_result.anomalies)}个异常点：
        - 异常值范围: {min([a['value'] for a in anomaly_result.anomalies]):.2f} ~ {max([a['value'] for a in anomaly_result.anomalies]):.2f}
        - 异常时间: {anomaly_result.anomalies[0]['timestamp']} 到 {anomaly_result.anomalies[-1]['timestamp']}
        - 检测方法: {anomaly_result.method}
        - 置信度: {anomaly_result.confidence:.2%}
        """

        # 根据调查深度调用不同的AI分析
        if investigation_depth == "basic":
            analysis_type = "anomaly"
            context = f"基础异常检测，关注异常值识别"
        elif investigation_depth == "detailed":
            analysis_type = "anomaly"
            context = f"详细异常分析，需要模式识别和影响评估"
        else:  # comprehensive
            analysis_type = "prediction"
            context = f"全面异常调查，包含预测、影响分析和预防措施"

        ai_request = AIAnalysisRequest(
            data_summary=anomaly_summary,
            analysis_type=analysis_type,
            context=context,
            language="zh"
        )

        ai_result = await call_ai_analysis(ai_request)

        # 格式化调查报告
        report = f"""
🔍 AI异常调查报告 - {column.upper()}

⚠️ 异常概况：
• 检测到 {len(anomaly_result.anomalies)} 个异常点
• 时间范围：{time_window}
• 检测置信度：{anomaly_result.confidence:.2%}
• 调查深度：{investigation_depth}

🤖 AI分析洞察：
{chr(10).join([f"• {insight}" for insight in ai_result.insights])}

🎯 根因分析：
{ai_result.reasoning}

🛠️ 建议措施：
{chr(10).join([f"• {rec}" for rec in ai_result.recommendations])}

📋 行动计划：
{chr(10).join([f"• {action}" for action in ai_result.next_actions])}

📊 技术细节：
• 异常检测方法：{anomaly_result.method}
• 处理时间：{anomaly_result.processing_time:.2f}秒
• AI分析时间：{ai_result.processing_time:.2f}秒
        """

        return report.strip()

    except Exception as e:
        logger.error(f"AI异常调查失败: {str(e)}")
        return f"❌ AI异常调查失败: {str(e)}"

@mcp.tool
async def ai_predictive_maintenance(
    equipment_id: str = Field(description="设备ID"),
    prediction_horizon: int = Field(default=72, description="预测时间范围（小时）"),
    maintenance_type: Literal["preventive", "predictive", "emergency"] = Field(default="predictive", description="维护类型"),
    risk_threshold: float = Field(default=0.7, description="风险阈值")
) -> str:
    """
    AI预测性维护 - 基于数据预测设备维护需求

    维护类型：
    - preventive: 预防性维护计划
    - predictive: 预测性维护建议
    - emergency: 紧急维护预警
    """
    logger.info(f"开始AI预测性维护分析: {equipment_id}")

    try:
        # 获取设备历史数据
        query = f"""
        SELECT timestamp, temperature, humidity, pressure, voltage, current, power, status
        FROM sensor_data
        WHERE device_id = %s
        AND timestamp >= DATE_SUB(NOW(), INTERVAL 30 DAY)
        ORDER BY timestamp DESC
        """

        df = await execute_query_with_cache(
            query,
            (equipment_id,),
            cache_key=f"maintenance_data_{equipment_id}"
        )

        if df.empty:
            return f"❌ 未找到设备 {equipment_id} 的历史数据"

        # 计算设备健康指标
        health_metrics = calculate_equipment_health(df)

        # 生成维护预测数据摘要
        maintenance_summary = f"""
        设备ID: {equipment_id}
        数据时间范围: {df['timestamp'].min()} 到 {df['timestamp'].max()}
        数据点数量: {len(df)}

        健康指标:
        - 温度稳定性: {health_metrics.get('temperature_stability', 0):.2%}
        - 电压稳定性: {health_metrics.get('voltage_stability', 0):.2%}
        - 功率效率: {health_metrics.get('power_efficiency', 0):.2%}
        - 整体健康度: {health_metrics.get('overall_health', 0):.2%}

        异常事件: {health_metrics.get('anomaly_count', 0)} 次
        """

        # AI预测性维护分析
        ai_request = AIAnalysisRequest(
            data_summary=maintenance_summary,
            analysis_type="prediction",
            context=f"预测性维护分析，预测时间{prediction_horizon}小时，维护类型{maintenance_type}",
            language="zh"
        )

        ai_result = await call_ai_analysis(ai_request)

        # 计算维护风险评分
        risk_score = calculate_maintenance_risk(health_metrics, df)

        # 生成维护建议
        maintenance_plan = generate_maintenance_plan(
            risk_score,
            prediction_horizon,
            maintenance_type,
            ai_result
        )

        # 格式化维护报告
        report = f"""
🔧 AI预测性维护报告 - {equipment_id}

📊 设备健康状态：
• 整体健康度：{health_metrics.get('overall_health', 0):.2%}
• 风险评分：{risk_score:.2f}/10
• 预测时间范围：{prediction_horizon}小时

🤖 AI分析结果：
{chr(10).join([f"• {insight}" for insight in ai_result.insights])}

⚠️ 风险评估：
{ai_result.reasoning}

🛠️ 维护建议：
{chr(10).join([f"• {rec}" for rec in ai_result.recommendations])}

📅 维护计划：
{maintenance_plan}

📋 行动项：
{chr(10).join([f"• {action}" for action in ai_result.next_actions])}

🎯 关键指标：
• 温度稳定性：{health_metrics.get('temperature_stability', 0):.2%}
• 电压稳定性：{health_metrics.get('voltage_stability', 0):.2%}
• 功率效率：{health_metrics.get('power_efficiency', 0):.2%}
        """

        # 如果风险超过阈值，发送预警通知
        if risk_score > risk_threshold * 10:
            notification = EnhancedNotification(
                id=f"maintenance_alert_{equipment_id}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                title=f"设备维护预警 - {equipment_id}",
                message=f"设备风险评分 {risk_score:.1f}/10 超过阈值，建议立即检查",
                severity="high" if risk_score > 8 else "medium",
                channels=["email", "desktop"],
                recipients=["<EMAIL>"],
                created_at=datetime.now()
            )
            notification_queue.put(notification)

        return report.strip()

    except Exception as e:
        logger.error(f"AI预测性维护失败: {str(e)}")
        return f"❌ AI预测性维护失败: {str(e)}"
