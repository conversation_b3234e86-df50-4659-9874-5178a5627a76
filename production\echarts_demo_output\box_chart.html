
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
        <style>
            body { margin: 0; padding: 10px; font-family: Arial, sans-serif; }
            #chart { width: 100%; height: 600px; }
        </style>
    </head>
    <body>
        <div id="chart"></div>
        <script>
            var chartDom = document.getElementById('chart');
            var myChart = echarts.init(chartDom);
            var option = {
  "title": {
    "text": "传感器数据箱线图",
    "left": "center",
    "textStyle": {
      "fontSize": 16,
      "fontWeight": "bold"
    }
  },
  "tooltip": {
    "trigger": "item",
    "axisPointer": {
      "type": "shadow"
    }
  },
  "legend": {
    "top": "bottom",
    "data": [
      "temperature",
      "humidity",
      "pressure"
    ]
  },
  "grid": {
    "left": "3%",
    "right": "4%",
    "bottom": "15%",
    "containLabel": true
  },
  "color": [
    "#5470c6",
    "#91cc75",
    "#fac858",
    "#ee6666",
    "#73c0de",
    "#3ba272",
    "#fc8452",
    "#9a60b4",
    "#ea7ccc",
    "#ff9f7f"
  ],
  "xAxis": {
    "type": "category",
    "data": [
      "temperature",
      "humidity",
      "pressure"
    ]
  },
  "yAxis": {
    "type": "value"
  },
  "series": [
    {
      "name": "箱线图",
      "type": "boxplot",
      "data": [
        [
          8.507343362967877,
          15.331519828737216,
          23.82805216972958,
          27.75486439712641,
          33.569938971080894
        ],
        [
          24.274860542008454,
          38.586848630744655,
          52.59596664087627,
          63.45146082118422,
          78.35716023685582
        ],
        [
          999.3718841665788,
          1008.6274572775415,
          1016.2657401618574,
          1020.5079570632331,
          1030.1827130258941
        ]
      ]
    }
  ]
};
            
            myChart.setOption(option);
            
            // 响应式调整
            window.addEventListener('resize', function() {
                myChart.resize();
            });
            
            // 错误处理
            myChart.on('error', function(params) {
                console.error('ECharts 渲染错误:', params);
            });
        </script>
    </body>
    </html>
    