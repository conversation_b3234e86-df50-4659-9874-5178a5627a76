#!/usr/bin/env python3
"""
简单启动脚本
"""

import asyncio
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from fastmcp import Client

async def test_connection():
    """测试连接"""
    print("🔗 测试MCP连接")
    print("=" * 30)

    # 服务器文件路径
    server_path = str(Path(__file__).parent.parent / "enterprise_database_mcp_server.py")
    print(f"📁 服务器路径: {server_path}")

    # 检查服务器文件是否存在
    if not Path(server_path).exists():
        print(f"❌ 服务器文件不存在: {server_path}")
        return False
    else:
        print("✅ 服务器文件存在")

    try:
        # 创建客户端 - 根据FastMCP文档的标准方式
        print("🤖 创建客户端...")
        print("   正在初始化FastMCP客户端...")

        import time
        start_time = time.time()

        client = Client(server_path)
        print(f"   ✅ 客户端创建完成 ({time.time() - start_time:.2f}秒)")

        # 测试连接
        print("🔗 测试连接...")
        print("   正在启动服务器进程...")
        print("   ⏱️ 设置30秒超时...")

        connection_start = time.time()

        # 添加超时机制
        try:
            async with asyncio.timeout(30):  # 30秒超时
                async with client:
            print(f"   ✅ 进入客户端上下文 ({time.time() - connection_start:.2f}秒)")

            print("   正在发送ping请求...")
            ping_start = time.time()
            await client.ping()
            print(f"   ✅ Ping成功! ({time.time() - ping_start:.2f}秒)")
            print("✅ 连接成功!")
            
            # 获取工具列表
            print("\n📋 获取工具列表...")
            print("   正在请求工具列表...")
            tools_start = time.time()

            tools = await client.list_tools()
            print(f"   ✅ 工具列表获取完成 ({time.time() - tools_start:.2f}秒)")
            print(f"✅ 找到 {len(tools)} 个工具:")

            for tool in tools[:5]:  # 显示前5个工具
                print(f"  • {tool.name}: {tool.description}")

            if len(tools) > 5:
                print(f"  ... 还有 {len(tools) - 5} 个工具")

            # 测试调用工具
            print("\n🛠️ 测试工具调用...")
            print("   正在调用 get_system_status 工具...")
            try:
                tool_start = time.time()
                result = await client.call_tool("get_system_status")
                print(f"   ✅ 工具调用完成 ({time.time() - tool_start:.2f}秒)")
                print("✅ 工具调用成功!")

                # 显示结果
                print(f"   📊 结果类型: {type(result)}")
                print(f"   📊 结果属性: {dir(result)}")

                if hasattr(result, 'data') and result.data:
                    data = result.data
                    print(f"  • 数据库状态: {data.get('database_status', 'unknown')}")
                    print(f"  • 数据总量: {data.get('total_data_rows', 0):,}")
                elif hasattr(result, 'content') and result.content:
                    print(f"  • 内容: {result.content}")
                else:
                    print(f"  • 原始结果: {result}")

            except Exception as e:
                print(f"⚠️ 工具调用失败: {e}")
                import traceback
                print(f"   详细错误: {traceback.format_exc()}")
        
                print(f"\n🎉 总耗时: {time.time() - start_time:.2f}秒")
                return True

        except asyncio.TimeoutError:
            print(f"⏰ 连接超时 (30秒)")
            print("   可能的原因:")
            print("   1. 服务器启动时间过长")
            print("   2. 数据库连接问题")
            print("   3. 依赖包缺失")
            return False

    except Exception as e:
        print(f"❌ 连接失败: {e}")
        import traceback
        print(f"详细错误信息:")
        print(traceback.format_exc())
        return False

async def interactive_client():
    """交互式客户端"""
    print("💬 交互式MCP客户端")
    print("=" * 30)
    print("命令: status, tools, test, quit")
    
    server_path = str(Path(__file__).parent.parent / "enterprise_database_mcp_server.py")
    client = Client(server_path)
    
    while True:
        try:
            command = input("\n> ").strip().lower()
            
            if command in ['quit', 'exit', 'q']:
                break
            
            elif command == 'status':
                async with client:
                    result = await client.call_tool("get_system_status")
                    print(f"📊 系统状态: {result}")
            
            elif command == 'tools':
                async with client:
                    tools = await client.list_tools()
                    print("🛠️ 可用工具:")
                    for tool in tools:
                        print(f"  • {tool.name}: {tool.description}")
            
            elif command == 'test':
                async with client:
                    # 测试统计分析
                    result = await client.call_tool("advanced_statistical_analysis", {
                        "start_time": "2024-01-01 00:00:00",
                        "end_time": "2024-01-02 00:00:00",
                        "columns": ["temperature"],
                        "operation": "average"
                    })
                    print(f"📈 统计分析结果: {result}")
            
            else:
                print("❓ 可用命令: status, tools, test, quit")
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("👋 再见!")

def main():
    """主函数"""
    print("🚀 MCP客户端启动器")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        if mode == "interactive":
            asyncio.run(interactive_client())
        elif mode == "test":
            asyncio.run(test_connection())
        else:
            print("用法: python start.py [test|interactive]")
    else:
        # 默认运行测试
        asyncio.run(test_connection())

if __name__ == "__main__":
    main()
