#!/usr/bin/env python3
"""
测试环境变量加载
"""

import os
from dotenv import load_dotenv

print("🔍 测试环境变量加载...")

# 加载.env文件
load_dotenv()

print("\n📋 数据库配置:")
print(f"DB_HOST: {os.getenv('DB_HOST', 'NOT_SET')}")
print(f"DB_PORT: {os.getenv('DB_PORT', 'NOT_SET')}")
print(f"DB_USER: {os.getenv('DB_USER', 'NOT_SET')}")
print(f"DB_PASSWORD: {'*' * len(os.getenv('DB_PASSWORD', '')) if os.getenv('DB_PASSWORD') else 'NOT_SET'}")
print(f"DB_NAME: {os.getenv('DB_NAME', 'NOT_SET')}")
print(f"DB_CHARSET: {os.getenv('DB_CHARSET', 'NOT_SET')}")

print(f"\n🔐 密码长度: {len(os.getenv('DB_PASSWORD', ''))}")

# 测试直接连接
print("\n🧪 测试数据库连接...")
try:
    import mysql.connector
    
    connection = mysql.connector.connect(
        host=os.getenv('DB_HOST', 'localhost'),
        port=int(os.getenv('DB_PORT', '3306')),
        user=os.getenv('DB_USER', 'root'),
        password=os.getenv('DB_PASSWORD', ''),
        database=os.getenv('DB_NAME', 'sensor_data'),
        charset=os.getenv('DB_CHARSET', 'utf8mb4')
    )
    
    cursor = connection.cursor()
    cursor.execute("SELECT 1")
    result = cursor.fetchone()
    
    cursor.close()
    connection.close()
    
    print("✅ 数据库连接成功!")
    
except Exception as e:
    print(f"❌ 数据库连接失败: {e}")

print("\n📝 如果密码显示为NOT_SET，请检查.env文件是否存在且格式正确")
