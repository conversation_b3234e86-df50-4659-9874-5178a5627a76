# 数据库密码更新后的快速设置指南

## 🔧 数据库配置更新

您的MySQL数据库配置已更新为：
- **主机**: localhost
- **端口**: 3306
- **用户名**: root
- **密码**: 123456
- **数据库**: sensor_data

## 🚀 快速启动步骤

### 第一步：创建数据库和示例数据

```bash
# 使用新密码创建数据库
mysql -u root -p123456 -e "source create_db_minimal.sql"

# 或者手动执行
mysql -u root -p123456
```

然后在MySQL命令行中执行：
```sql
source create_db_minimal.sql;
```

### 第二步：测试数据库连接

```bash
# 运行数据库连接测试
python test_direct.py
```

应该看到类似输出：
```
✅ 数据库连接成功，数据行数: 18
✅ 数据表结构正常，列数: 13
✅ 数据时间范围: 2024-xx-xx xx:xx:xx 到 2024-xx-xx xx:xx:xx
✅ 温度平均值: 28.96°C，有效数据点: 18
✅ 高温异常数据点 (>40°C): 2
```

### 第三步：启动系统

选择以下任一方式启动：

#### 方式A：混合系统（推荐）
```bash
# 同时启动MCP服务器和Streamlit前端
python start_hybrid_system.py
```

访问地址：
- 🌐 **Streamlit前端**: http://localhost:8501
- 📚 **API文档**: http://localhost:8000/docs

#### 方式B：仅MCP服务器
```bash
# 启动MCP服务器（用于AI对话）
python enterprise_database_mcp_server.py
```

#### 方式C：仅Streamlit前端
```bash
# 先启动API服务器
export HTTP_API_MODE=true
python enterprise_database_mcp_server.py &

# 再启动Streamlit前端
streamlit run streamlit_dashboard.py --server.port 8501
```

## 🧪 验证系统功能

### 1. 测试数据库连接
```bash
python test_direct.py
```

### 2. 测试Streamlit前端
```bash
streamlit run test_streamlit.py --server.port 8502
```
访问：http://localhost:8502

### 3. 测试API接口
```bash
# 启动API服务器
export HTTP_API_MODE=true
python enterprise_database_mcp_server.py &

# 测试API健康状态
curl http://localhost:8000/api/health

# 测试获取系统状态
curl http://localhost:8000/api/system/status
```

## 🔍 故障排除

### 数据库连接问题

1. **检查MySQL服务状态**
   ```bash
   # Windows
   net start mysql
   
   # Linux/macOS
   sudo systemctl status mysql
   ```

2. **验证密码**
   ```bash
   mysql -u root -p123456 -e "SELECT 1"
   ```

3. **检查端口**
   ```bash
   netstat -an | grep 3306
   ```

### 常见错误解决

1. **"Access denied for user 'root'@'localhost'"**
   - 确认密码是 `123456`
   - 检查MySQL用户权限

2. **"Can't connect to MySQL server"**
   - 确认MySQL服务正在运行
   - 检查防火墙设置

3. **"Unknown database 'sensor_data'"**
   - 重新运行数据库创建脚本
   ```bash
   mysql -u root -p123456 -e "source create_db_minimal.sql"
   ```

## 📋 配置文件更新状态

以下文件已更新为新密码：
- ✅ `.env` - 主环境配置文件
- ✅ `.env.example` - 示例配置文件
- ✅ `test_direct.py` - 数据库测试脚本
- ✅ `start_hybrid_system.py` - 混合系统启动脚本
- ✅ `quick_start.py` - 快速启动脚本

## 🎯 下一步操作

1. **创建数据库**：`mysql -u root -p123456 -e "source create_db_minimal.sql"`
2. **测试连接**：`python test_direct.py`
3. **启动系统**：`python start_hybrid_system.py`
4. **访问前端**：http://localhost:8501

## 💡 使用提示

### Streamlit前端使用
- 访问 http://localhost:8501
- 选择不同功能页面进行数据分析
- 支持实时监控、统计分析、异常检测等

### AI对话使用
- 安装到Claude Desktop或Open WebUI
- 使用自然语言查询：`"分析过去24小时的温度数据"`
- 支持语音交互功能

### API接口使用
- 查看API文档：http://localhost:8000/docs
- 适合第三方系统集成
- 支持RESTful API调用

---

**🎉 数据库密码已成功更新为 `123456`，现在可以正常使用系统了！**
