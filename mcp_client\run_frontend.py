#!/usr/bin/env python3
"""
第3步：启动前端界面
简化版本，直接运行Streamlit
"""

import subprocess
import sys
from pathlib import Path

def main():
    """启动Streamlit前端"""
    print("🚀 第3步：启动前端界面")
    print("=" * 50)
    
    # 前端脚本路径
    frontend_script = Path(__file__).parent / "step3_start_frontend.py"
    
    if not frontend_script.exists():
        print(f"❌ 前端脚本不存在: {frontend_script}")
        return
    
    print(f"📁 前端脚本: {frontend_script}")
    print("🌐 启动Streamlit...")
    print("📍 地址: http://localhost:8501")
    print("⏹️ 按 Ctrl+C 停止前端")
    print("-" * 50)
    
    try:
        # 启动Streamlit
        cmd = [
            sys.executable, "-m", "streamlit", "run",
            str(frontend_script),
            "--server.port=8501",
            "--server.address=localhost",
            "--browser.gatherUsageStats=false"
        ]
        
        print(f"🔧 执行命令: {' '.join(cmd)}")
        subprocess.run(cmd)
        
    except KeyboardInterrupt:
        print("\n👋 前端已停止")
    except FileNotFoundError:
        print("❌ Streamlit未安装，请运行: pip install streamlit")
    except Exception as e:
        print(f"❌ 启动失败: {e}")

if __name__ == "__main__":
    main()
